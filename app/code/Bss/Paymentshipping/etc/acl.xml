<?xml version="1.0"?>
<!--
/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category   BSS
 * @package    Bss_Paymentshipping
 * <AUTHOR> Team
 * @copyright  Copyright (c) 2015-2021 BSS Commerce Co. ( http://bsscommerce.com )
 * @license    http://bsscommerce.com/Bss-Commerce-License.txt
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magento_Backend::stores">
                    <resource id="Bss_Paymentshipping::bss_paymentshippingindex" title="Methods For Customer Group by BssCommerce" sortOrder="80" />
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Bss_Paymentshipping::config_section" title="Bss Shipping Payment" sortOrder="90" />
                        </resource>
                    </resource>
                    <resource id="Bss_Paymentshipping::api" title="Bss Payment Shipping API" sortOrder="10">
                        <resource id="Bss_Paymentshipping::add_edit" title="Payment shipping" sortOrder="10" />
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
