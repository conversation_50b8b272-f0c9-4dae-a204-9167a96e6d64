<?php
/**
 * BSS Commerce Co.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://bsscommerce.com/Bss-Commerce-License.txt
 *
 * @category   BSS
 * @package    Bss_Paymentshipping
 * <AUTHOR> Team
 * @copyright  Copyright (c) 2015-2021 BSS Commerce Co. ( http://bsscommerce.com )
 * @license    http://bsscommerce.com/Bss-Commerce-License.txt
 */
namespace Bss\Paymentshipping\Api;

/**
 * Company Credit Management
 *
 * @api
 * @since 100.0.0
 */
interface PaymentshippingManagementInterface
{

    /**
     * Get module configs
     *
     * @param int $storeViewId
     * @return mixed|array
     */
    public function getConfig($storeViewId = null);

}
