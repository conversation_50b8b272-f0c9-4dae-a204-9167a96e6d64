<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<fieldset name="general">
        <field name="product_sku" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                    <item name="label" xsi:type="string" translate="true">SKU</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="dataScope" xsi:type="string">product_sku</item>
                    <item name="disabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </field>
        <field name="order_item_id" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="sortOrder" xsi:type="number">20</item>
                    <item name="label" xsi:type="string" translate="true">Order Item ID</item>
                    <item name="dataScope" xsi:type="string">order_item_id</item>
                    <item name="disabled" xsi:type="boolean">true</item>
                </item>
            </argument>
        </field>
        <field name="status" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="sortOrder" xsi:type="number">30</item>
                    <item name="label" xsi:type="string" translate="true">Status</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="dataScope" xsi:type="string">status</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                </item>
            </argument>
            <formElements>
                <select>
                    <settings>
                        <options class="CopeX\SerialNumber\Ui\Component\Listing\Columns\Active"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="supplier_name" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="sortOrder" xsi:type="number">40</item>
                    <item name="label" xsi:type="string" translate="true">Supplier Name</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="dataScope" xsi:type="string">supplier_name</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
            <formElements>
                <select>
                    <settings>
                        <options class="Vivo\SerialNumber\Ui\Component\Listing\Columns\Manufacturers"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="voucher_value" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="sortOrder" xsi:type="number">50</item>
                    <item name="label" xsi:type="string" translate="true">Voucher Value</item>
                    <item name="dataType" xsi:type="string">float</item>
                    <item name="dataScope" xsi:type="string">voucher_value</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="serial_number" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="sortOrder" xsi:type="number">60</item>
                    <item name="label" xsi:type="string" translate="true">Serial Number</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="dataScope" xsi:type="string">serial_number</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="voucher_link" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="sortOrder" xsi:type="number">70</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Voucher Link</item>
                    <item name="dataScope" xsi:type="string">voucher_link</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="voucher_external_link" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">SerialNumber</item>
                    <item name="sortOrder" xsi:type="number">80</item>
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="label" xsi:type="string" translate="true">Voucher Value Status Link</item>
                    <item name="dataScope" xsi:type="string">voucher_external_link</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                </item>
            </argument>
        </field>
        <field name="start_date" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="label" xsi:type="string" translate="true">Start Date</item>
                    <item name="formElement" xsi:type="string">date</item>
                    <item name="source" xsi:type="string">page</item>
                    <item name="sortOrder" xsi:type="number">90</item>
                    <item name="dataScope" xsi:type="string">start_date</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                    <item name="options" xsi:type="array">
                        <item name="dateFormat" xsi:type="string">yyyy-MM-dd</item>
                        <item name="timeFormat" xsi:type="string">HH:mm:ss</item>
                        <item name="showsTime" xsi:type="boolean">true</item>
                    </item>
                    <item name="storeTimeZone" xsi:type="string">string</item>
                </item>
            </argument>
        </field>
        <field name="end_date" formElement="input">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="label" xsi:type="string" translate="true">End Date</item>
                    <item name="formElement" xsi:type="string">date</item>
                    <item name="source" xsi:type="string">page</item>
                    <item name="sortOrder" xsi:type="number">100</item>
                    <item name="dataScope" xsi:type="string">end_date</item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">true</item>
                    </item>
                    <item name="options" xsi:type="array">
                        <item name="dateFormat" xsi:type="string">yyyy-MM-dd</item>
                        <item name="timeFormat" xsi:type="string">HH:mm:ss</item>
                        <item name="showsTime" xsi:type="boolean">true</item>
                    </item>
                    <item name="storeTimeZone" xsi:type="string">string</item>
                </item>
            </argument>
        </field>
	</fieldset>
</form>
