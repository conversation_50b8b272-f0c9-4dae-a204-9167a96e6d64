<?xml version="1.0"?>
<grid xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Hyva_Admin:etc/hyva-grid.xsd">
    <source>
        <collection>\Vivo\SerialNumber\Model\ResourceModel\SalesReport\Collection</collection>
    </source>
    <massActions idColumn="id">
        <action id="delete" label="Delete" url="*/*/massDelete" requireConfirmation="true"/>
    </massActions>
    <columns>
        <include>
            <column name="creation_date" type="datetime" label="Creation Date"/>
            <column name="download_link" type="text" label="Download Link"/>
        </include>
    </columns>
    <actions idColumn="id">
        <action id="salesreport_download" label="Download" url="*/*/download" idParam="id"/>
    </actions>
    <navigation>
        <sorting>
            <defaultSortByColumn>creation_date</defaultSortByColumn>
            <defaultSortDirection>desc</defaultSortDirection>
        </sorting>
    </navigation>
</grid>
