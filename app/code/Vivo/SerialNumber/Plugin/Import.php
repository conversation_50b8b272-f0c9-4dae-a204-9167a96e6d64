<?php

declare(strict_types=1);

namespace Vivo\SerialNumber\Plugin;

use CopeX\SerialNumber\Api\Data\SerialNumberInterface;
use CopeX\SerialNumber\Controller\Adminhtml\SerialNumber\Import as ImportModel;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Attribute\Source\Status;
use Magento\Catalog\Model\Product\Type;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Model\ProductFactory;
use Magento\Catalog\Model\ProductRepository;
use Magento\Eav\Api\AttributeOptionManagementInterface;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Eav\Api\Data\AttributeOptionInterfaceFactory;
use Magento\Eav\Api\Data\AttributeOptionLabelInterfaceFactory;
use Magento\Framework\Message\ManagerInterface;
use Vivo\SerialNumber\Helper\Data;
use Magento\Eav\Model\Config as EavConfig;
use Magento\InventoryApi\Api\Data\SourceItemInterface;
use Magento\InventoryApi\Api\SourceItemsSaveInterface;
use Magento\InventoryApi\Api\Data\SourceItemInterfaceFactory;

class Import
{
    private ProductFactory $productFactory;
    private ProductRepository $productRepository;
    private AttributeRepositoryInterface $attributeRepository;
    private AttributeOptionManagementInterface $attributeOptionManagement;
    private AttributeOptionLabelInterfaceFactory $optionLabelFactory;
    private AttributeOptionInterfaceFactory $optionFactory;
    private ManagerInterface $messageManager;
    private Data $helper;
    private EavConfig $eavConfig;
    private SourceItemsSaveInterface $sourceItemsSave;
    private SourceItemInterfaceFactory $sourceItemFactory;

    public function __construct(
        ProductFactory $productFactory,
        ProductRepository $productRepository,
        AttributeRepositoryInterface $attributeRepository,
        AttributeOptionManagementInterface $attributeOptionManagement,
        AttributeOptionLabelInterfaceFactory $optionLabelFactory,
        AttributeOptionInterfaceFactory $optionFactory,
        ManagerInterface $messageManager,
        Data $helper,
        EavConfig $eavConfig
    ) {
        $this->productFactory = $productFactory;
        $this->productRepository = $productRepository;
        $this->attributeRepository = $attributeRepository;
        $this->attributeOptionManagement = $attributeOptionManagement;
        $this->optionLabelFactory = $optionLabelFactory;
        $this->optionFactory = $optionFactory;
        $this->messageManager = $messageManager;
        $this->helper = $helper;
        $this->eavConfig = $eavConfig;
    }

    public function afterGetMappedData(ImportModel $subject, $result): mixed
    {
        if ($this->hasSupplierName($result)) {
            if (is_int($result['start_date'])) {
                $result['start_date'] = $this->helper->excelDateToDate($result['start_date']);
            }
            if (is_int($result['end_date'])) {
                $result['end_date'] = $this->helper->excelDateToDate($result['end_date']);
            }
            if ($this->helper->checkDate($result['start_date']) && $this->helper->checkDate($result['end_date'])) {
                if (in_array($result['lieferant_name'], $this->getManufacturers())) {
                    $result = $this->processMappedData($result);
                } else {
                    $this->messageManager->addErrorMessage(
                        __('Please first create supplier %1 manually in the system before voucher codes can be imported. Voucher Number: %2',
                            $result['lieferant_name'],
                            $result['voucher_code']
                        )
                    );
                }
            } else {
                $this->messageManager->addErrorMessage(__('Start or End Date is incorrect. Voucher Number: %1', $result['voucher_code']));
            }
        }

        return $result;
    }

    private function getManufacturers(): array
    {
        $attribute = $this->eavConfig->getAttribute('catalog_product', 'manufacturer');
        $options = $attribute->getSource()->getAllOptions();
        $manufacturers = [];
        foreach ($options as $option) {
            if (! empty($option['value'])) {
                $manufacturers[$option['value']] = $option['label'];
            }
        }

        return $manufacturers;
    }

    public function afterGetRequiredColumns(ImportModel $subject, $result)
    {
        return ['LieferantName', 'VoucherValue', 'VoucherCode', 'VoucherLink', 'StartDate', 'EndDate'];
    }

    public function getProductSku(array $data): string
    {
        $sku = $this->helper->getSKUFormat($data);
        try {
            $product = $this->getProductBySku($sku);
        } catch (\Exception $e) {
            $product = $this->createNewProduct($sku, $data);
        }

        return $product->getSku();
    }

    public function aroundGetFileData(ImportModel $subject, \Closure $proceed)
    {
        $file = $this->getUploadedFile($subject);
        $fileExtension = $this->getFileExtension($file);
        if ($this->isValidSpreadsheet($fileExtension)) {
            return $this->helper->getSpreadSheetData($file);
        }

        return $proceed();
    }

    public function getOptionId(mixed $attribute, mixed $manufacturerLabel): mixed
    {
        $options = $attribute->getOptions();

        return $this->findOptionIdByLabel($options, $manufacturerLabel);
    }

    public function aroundImportData(ImportModel $subject, \Closure $proceed, array $data): void
    {
        $allLineQty = 0;
        $errorLineQty = 0;
        $successLineQty = 0;
        $headerMapping = $subject->getHeaderMapping($data);
        foreach ($data as $row) {
            $mappedRow = $subject->getMappedData($headerMapping, $row);
            if ($this->isValidRow($mappedRow)) {
                $allLineQty++;
                $isMapRow = $this->processMappedRow($subject, $mappedRow);
                if ($isMapRow) {
                    $successLineQty ++;
                } else {
                    $errorLineQty++;
                }
            }
        }
        if ($errorLineQty == 0 && $successLineQty > 0 && $allLineQty > 0) {
            $this->messageManager->addSuccessMessage(__('%1 vouchers were successfully imported. ', $successLineQty));
        }
        if ($errorLineQty > 0 && $successLineQty > 0 && $allLineQty > 0) {
            $this->messageManager->addWarningMessage(
                __('%1 of %2 vouchers were successfully imported. See notes for details.', $successLineQty, $successLineQty + $errorLineQty)
            );
        }
        if ($successLineQty == 0 && $allLineQty > 0) {
            $this->messageManager->addErrorMessage(
                __('%1 of %2 vouchers were successfully imported. See notes for details.', $successLineQty, $successLineQty + $errorLineQty)
            );
        }
        $this->cleanCache();
    }

    private function hasSupplierName(array $result): bool
    {
        return isset($result['lieferant_name']) && ! empty($result['lieferant_name']);
    }

    private function processMappedData(array $result): array
    {
        $result['sku'] = $this->getProductSku($result);
        $result['serial_number'] = $result['voucher_code'];
        $result['voucher_external_link'] = $result['voucher_value_status_link'] ?? false;
        $result['supplier_name'] = $result['lieferant_name'];

        if ($this->hasEndDate($result)) {
            $result['end_date'] = $this->formatEndDate($result['end_date']);
        }

        $this->unsetUnnecessaryKeys($result);

        return $result;
    }

    private function hasEndDate(array $result): bool
    {
        return isset($result['end_date']) && ! empty($result['end_date']);
    }

    private function formatEndDate(string $endDate): string
    {
        return $endDate . ' 23:59:59';
    }

    private function unsetUnnecessaryKeys(array &$result): void
    {
        unset($result['voucher_value_status_link'], $result['voucher_code'], $result['lieferant_name']);
    }

    private function getProductBySku(string $sku)
    {
        return $this->productRepository->get($sku);
    }

    private function createNewProduct(string $sku, array $data)
    {
        $product = $this->productFactory->create();
        $product->setSku($sku);
        $product->setTypeId(\Magento\Catalog\Model\Product\Type::TYPE_VIRTUAL); // vouchers → virtual
        $product->setAttributeSetId($product->getDefaultAttributeSetId());
        $product->setWebsiteIds([$this->storeManager->getWebsite()->getId()]);
        $product->setName($data['lieferant_name'] . ' ' . (float)$data['voucher_value']);
        $product->setPrice((float)$data['voucher_value']);
        $product->setStatus(\Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED);
        $product->setVisibility(\Magento\Catalog\Model\Product\Visibility::VISIBILITY_NOT_VISIBLE);
        $product->setTaxClassId(0);
        $product->setData('has_serials', true);

        // Turn OFF stock management (prevents StockItem save)
        $product->setStockData([
            'use_config_manage_stock' => 0,
            'manage_stock'            => 0,
            'is_in_stock'             => 1,
            'qty'                     => 0,
        ]);

        $this->setProductAttributes($product, $data);

        return $this->productRepository->save($product);
    }

    private function setProductAttributes($product, array $data): void
    {
        $this->setGeneralAttributes($product);
        $this->setStockAttributes($product);
        $this->setManufacturerAttribute($product, $data);
    }

    private function setGeneralAttributes($product): void
    {
        $product->setAttributeSetId(4);
        $product->setTypeId(Type::TYPE_VIRTUAL);
        $product->setVisibility(Visibility::VISIBILITY_BOTH);
        $product->setStatus(Status::STATUS_ENABLED);
    }

    private function setStockAttributes($product): void
    {
        $stockData = [
            'use_config_manage_stock' => 0,
            'is_in_stock' => 1,
            'manage_stock' => 0,
        ];
        $product->setStockData($stockData);
        $product->setQuantityAndStockStatus(['is_in_stock' => 1]);
    }

    private function setManufacturerAttribute($product, array $data): void
    {
        $product->setManufacturer($this->getManufacturer($data));
    }

    private function getUploadedFile(ImportModel $subject)
    {
        $file = $subject->getRequest()->getFiles('import_serial_numbers');
        if (! $file) {
            $this->messageManager->addErrorMessage(__('Please upload a file'));
        }

        return $file;
    }

    private function getFileExtension(array $file): string
    {
        return strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    }

    private function isValidSpreadsheet(string $fileExtension): bool
    {
        return in_array($fileExtension, ['xls', 'xlsx']);
    }

    private function findOptionIdByLabel(array $options, string $manufacturerLabel): mixed
    {
        foreach ($options as $option) {
            if ($this->isMatchingLabel($option, $manufacturerLabel)) {
                return $option->getValue();
            }
        }

        return null;
    }

    private function isMatchingLabel($option, string $manufacturerLabel): bool
    {
        return strtolower($option->getLabel()) === strtolower($manufacturerLabel);
    }

    private function getManufacturer($data)
    {
        $manufacturerLabel = $data['lieferant_name'];
        $attribute = $this->attributeRepository->get(Product::ENTITY, 'manufacturer');
        return $this->findOrCreateManufacturer($attribute, $manufacturerLabel);
    }

    private function findOrCreateManufacturer($attribute, $manufacturerLabel)
    {
        $optionId = $this->getOptionId($attribute, $manufacturerLabel);
        if (! $optionId) {
            $optionId = $this->createManufacturer($attribute, $manufacturerLabel);
        }

        return $optionId;
    }

    private function createManufacturer($attribute, $label)
    {
        $optionLabel = $this->createOptionLabel($label);
        $option = $this->createOption($optionLabel);

        $this->addOptionToAttribute($attribute, $option);

        return $this->getOptionId($attribute, $label);
    }

    private function createOptionLabel($label)
    {
        /** @var \Magento\Eav\Model\Entity\Attribute\OptionLabel $optionLabel */
        $optionLabel = $this->optionLabelFactory->create();
        $optionLabel->setStoreId(0);
        $optionLabel->setLabel($label);

        return $optionLabel;
    }

    private function createOption($optionLabel)
    {
        $option = $this->optionFactory->create();
        $option->setLabel($optionLabel->getLabel());
        $option->setStoreLabels([$optionLabel]);
        $option->setSortOrder(0);
        $option->setIsDefault(false);

        return $option;
    }

    private function addOptionToAttribute($attribute, $option): void
    {
        $this->attributeOptionManagement->add(
            Product::ENTITY,
            $attribute->getAttributeId(),
            $option
        );
    }

    private function isValidRow(array $mappedRow): bool
    {
        return !empty(array_filter($mappedRow));
    }

    private function processMappedRow(ImportModel $subject, array $mappedRow): bool
    {
        if (!array_key_exists('sku', $mappedRow)) {
            return false;
        } else {
            $mappedRow['product_sku'] = $mappedRow['sku'];
            unset($mappedRow['sku']);
        }
        $mappedRow['status'] = SerialNumberInterface::ACTIVE;
        if (!array_key_exists('supplier_name', $mappedRow)) {
            return false;
        } else {
            if (!$this->manufacturerExists($mappedRow['supplier_name'])) {
                return false;
            }
        }
        if ($subject->getSerialNumberManagement()->isVoucher($mappedRow['serial_number'])) {
            $this->messageManager->addErrorMessage(__('This voucher already exists: %1', $mappedRow['serial_number']));
            return false;
        } else {
            $this->processSerialNumber($subject, $mappedRow);
            return true;
        }
    }

    public function manufacturerExists(string $manufacturerName): bool
    {
        $attribute = $this->eavConfig->getAttribute(Product::ENTITY, 'manufacturer');
        if (!$attribute || !$attribute->getId()) {
            return false;
        }
        $options = $attribute->getSource()->getAllOptions(false);
        foreach ($options as $option) {
            if (strcasecmp($option['label'], $manufacturerName) === 0) {
                return true;
            }
        }

        return false;
    }

    private function processSerialNumber(ImportModel $subject, array $mappedRow): void
    {
        if (! $this->isValidDateRange($mappedRow)) {
            $this->messageManager->addErrorMessage(__(
                'The date format is incorrect in the importing row with the voucher code: %1',
                $mappedRow['serial_number']
            ));
        } else {
            $subject->getSerialNumberManagement()->addSerialNumber($mappedRow);
        }
    }

    private function isValidDateRange(array $mappedRow): bool
    {
        return $this->helper->checkDate($this->helper->splitDate($mappedRow['start_date'])) &&
            $this->helper->checkDate($this->helper->splitDate($mappedRow['end_date']));
    }

    private function cleanCache(): void
    {
        $this->helper->cleanEavCache();
        $this->helper->cleanFrontendCache();
    }
}
