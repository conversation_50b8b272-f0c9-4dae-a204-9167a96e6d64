<?php

declare(strict_types=1);

namespace Vivo\SerialNumber\Plugin;

use CopeX\SerialNumber\Controller\Adminhtml\SerialNumber\Import as Subject;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Controller\Result\Redirect;

class ImportPlugin
{
    protected RedirectFactory $redirectFactory;
    protected ManagerInterface $messageManager;

    public function __construct(
        RedirectFactory $redirectFactory,
        ManagerInterface $messageManager
    ) {
        $this->redirectFactory = $redirectFactory;
        $this->messageManager = $messageManager;
    }

    public function aroundExecute(Subject $subject, \Closure $proceed): Redirect
    {
        try {
            $subject->importData($subject->getFileData());
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        return $this->redirectFactory->create()->setPath('copex_serialnumber/serialnumber/index');
    }
}
