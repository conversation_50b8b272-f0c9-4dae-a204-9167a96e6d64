<?php

declare(strict_types=1);

namespace Vivo\SerialNumber\Plugin;

use CopeX\SerialNumber\Api\Data\SerialNumberInterface;
use CopeX\SerialNumber\Controller\Adminhtml\Product\AddSerialNumber as ParentAddSerialNumber;
use JetBrains\PhpStorm\Pure;

class AddSerialNumber extends ParentAddSerialNumber
{
    public function aroundExecute(ParentAddSerialNumber $subject, callable $proceed)
    {
        $result = $subject->jsonResultFactory->create();

        try {
            $requestData = $this->getRequestData($subject);
            $product = $subject->productRepository->getById($requestData['product_id']);

            if ($error = $this->validateRequestData($subject, $requestData, $product)) {
                return $this->errorResponse($result, $error);
            }

            $this->saveSerialNumber($subject, $product, $requestData);
            return $this->successResponse($result, __('The %1 serial number was added successfully', $requestData['serial_number']));
        } catch (\Exception $e) {
            return $this->errorResponse($result, $e->getMessage());
        }
    }

    private function validateRequestData($subject, array $requestData, $product)
    {
        if (! $this->validateEmptyData($requestData)) {
            return __('The Voucher Code and Start Date and End Date fields are required');
        }

        if (! $this->validateDates($requestData['start_date'], $requestData['end_date'])) {
            return __('Start time cannot be greater than end time');
        }

        if (! $this->validateSerialNumber($subject, $product->getSku(), $requestData['serial_number'])) {
            return __('Voucher code %1 already exists', $requestData['serial_number']);
        }

        return null;
    }

    private function getRequestData(ParentAddSerialNumber $subject): array
    {
        return [
            'serial_number' => $subject->getRequest()->getParam('serial_number'),
            'start_date' => $subject->getRequest()->getParam('start_date') . ' 00:00:00',
            'end_date' => $subject->getRequest()->getParam('end_date') . ' 23:59:59',
            'product_id' => $subject->getRequest()->getParam('product_id'),
            'voucher_link' => $subject->getRequest()->getParam('voucher_link'),
            'voucher_value_status_link' => $subject->getRequest()->getParam('voucher_value_status_link'),
        ];
    }

    private function validateEmptyData(array $requestData): bool
    {
        $invalidDates = [' 00:00:00', ' 23:59:59'];

        return ! (empty($requestData['serial_number']) ||
            in_array($requestData['start_date'], $invalidDates, true) ||
            in_array($requestData['end_date'], $invalidDates, true));
    }

    #[Pure]
    private function validateDates($startDate, $endDate): bool
    {
        return strtotime($endDate) >= strtotime($startDate);
    }

    private function validateSerialNumber(ParentAddSerialNumber $subject, $productSku, $serialNumber): bool
    {
        return $subject->serialNumberManagement->checkProductSerialNumberAvailability($productSku, $serialNumber);
    }

    private function getManufacturerLabel($product)
    {
        $manufacturerId = $product->getData('manufacturer');
        $attribute = $product->getResource()->getAttribute('manufacturer');
        if ($attribute && $attribute->usesSource()) {
            return $attribute->getSource()->getOptionText($manufacturerId);
        }

        return null;
    }

    private function saveSerialNumber(ParentAddSerialNumber $subject, $product, array $data): void
    {
        $model = $this->serialNumberFactory->create();
        $model->setSupplierName($this->getManufacturerLabel($product));
        $model->setSerialNumber($data['serial_number']);
        $model->setStatus(SerialNumberInterface::ACTIVE);
        $model->setProductSku($product->getSku());
        $model->setVoucherValue($product->getPrice());
        $model->setStartDate($data['start_date']);
        $model->setEndDate($data['end_date']);
        $model->setVoucherLink($data['voucher_link']);
        $model->setVoucherValueStatusLink($data['voucher_value_status_link']);
        $model->save();
    }

    private function successResponse($result, $message)
    {
        return $result->setData([
            'success' => true,
            'message' => $message,
        ]);
    }

    private function errorResponse($result, $message)
    {
        return $result->setData([
            'success' => false,
            'message' => $message,
        ]);
    }
}
