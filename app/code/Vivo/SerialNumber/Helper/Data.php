<?php

declare(strict_types=1);

namespace Vivo\SerialNumber\Helper;

use CopeX\SerialNumber\Api\SerialNumberRepositoryInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Cache\Frontend\Pool;
use Magento\Framework\App\Cache\TypeListInterface;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\NoSuchEntityException;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\CatalogRule\Model\ResourceModel\Rule\CollectionFactory as RuleCollectionFactory;

/**
 * Class Data
 *
 * @package CopeX\SerialNumber\Helper
 */
class Data extends AbstractHelper
{
    public const REQUEST_PRICE_TYPE = 'price';
    public const REQUEST_SKU_TYPE = 'sku';
    private const DATE_VALIDATION_RULE = "/^(0[1-9]|[12][0-9]|3[01])\.(0[1-9]|1[0-2])\.(\d{4})$/";
    private const FULL_DATE_VALIDATION_RULE = "/^(0[1-9]|[12][0-9]|3[01])\.(0[1-9]|1[0-2])\.(\d{4}) (0[0-9]|1[0-9]|2[0-3]):([0-5][0-9]):([0-5][0-9])$/";
    private const SHORT_DATE_VALIDATION_RULE = "/^\d{2}\.\d{2}\.\d{4}/";
    private const MATERIAL_NUMBERS = 'vivo_manufacturer_serial_number/material_numbers_tab/material_numbers';
    private const CATEGORIES = 'vivo_manufacturer_category/manufacturer_category_tab/manufacturer_category';

    private TypeListInterface $cacheTypeList;

    private Pool $cacheFrontendPool;

    private ProductRepositoryInterface $productRepository;

    private SearchCriteriaBuilder $searchCriteriaBuilder;

    private FilterBuilder $filterBuilder;

    private AttributeRepositoryInterface $attributeRepository;

    private EavConfig $eavConfig;

    private CollectionFactory $productCollectionFactory;

    private SerialNumberRepositoryInterface $serialNumberRepository;

    private CategoryRepositoryInterface $categoryRepository;

    private RuleCollectionFactory $ruleCollectionFactory;

    public function __construct(
        Context $context,
        TypeListInterface $cacheTypeList,
        Pool $cacheFrontendPool,
        ProductRepositoryInterface $productRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        AttributeRepositoryInterface $attributeRepository,
        EavConfig $eavConfig,
        CollectionFactory $productCollectionFactory,
        SerialNumberRepositoryInterface $serialNumberRepository,
        CategoryRepositoryInterface $categoryRepository,
        RuleCollectionFactory $ruleCollectionFactory
    ) {
        $this->cacheTypeList = $cacheTypeList;
        $this->cacheFrontendPool = $cacheFrontendPool;
        $this->productRepository = $productRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->attributeRepository = $attributeRepository;
        $this->eavConfig = $eavConfig;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->serialNumberRepository = $serialNumberRepository;
        $this->categoryRepository = $categoryRepository;
        $this->ruleCollectionFactory = $ruleCollectionFactory;
        parent::__construct($context);
    }

    public function getProductDataByManufacturer($label, $requestType)
    {
        $data = [];
        $manufacturerId = $this->getManufacturerIdByLabel($label);
        if ($manufacturerId) {
            $products = $this->getProductsByManufacturer($manufacturerId);
            foreach ($products as $product) {
                $data[$product->getId()] = $this->getProductData($product, $requestType);
            }
        }

        return $data;
    }

    public function excelDateToDate($excelDate)
    {
        $unixTimestamp = ($excelDate - 25569) * 86400;

        return date('d.m.Y', $unixTimestamp);
    }

    public function checkDate($date)
    {
        if ($date) {
            if (preg_match(self::DATE_VALIDATION_RULE, (string) $date)) {
                return true;
            }
        }

        return false;
    }

    public function splitDate($date)
    {
        if ($date) {
            if ($this->checkDate($date)) {
                return $date;
            }
            if (preg_match(self::FULL_DATE_VALIDATION_RULE, (string) $date)) {
                if (preg_match(self::SHORT_DATE_VALIDATION_RULE, (string) $date, $matches)) {
                    return $matches[0];
                }
            }
        }

        return false;
    }

    public function cleanEavCache(): void
    {
        $types = ['eav'];
        foreach ($types as $type) {
            $this->cacheTypeList->cleanType($type);
        }
    }

    public function cleanFrontendCache(): void
    {
        foreach ($this->cacheFrontendPool as $cacheFrontend) {
            $cacheFrontend->getBackend()->clean();
        }
    }

    public function getSKUFormat($data)
    {
        return str_replace('_', '', mb_strtoupper($data['lieferant_name'])) . '_' . $data['voucher_value'];
    }

    public function getSpreadSheetData(array $file): array
    {
        $worksheet = IOFactory::load($file['tmp_name'])->getActiveSheet();
        return $this->processWorksheetData($worksheet);
    }

    public function getManufacturerBySku($sku)
    {
        try {
            return $this->productRepository->get($sku)->getAttributeText('manufacturer');
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }

    public function getMaterialBySku($sku)
    {
        try {
            return $this->productRepository->get($sku)->getAttributeText('material');
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }

    public function getMinStockLevelBySku($sku)
    {
        try {
            $product = $this->productRepository->get($sku);
            $minLevAttr = $product->getCustomAttribute('minimum_stock_level');
            return $minLevAttr ? $minLevAttr->getValue() : null;
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }

    public function getAllVouchers()
    {
        $resultArr = [];
        $collection = $this->serialNumberRepository->getList($this->searchCriteriaBuilder->create())->getItems();
        foreach ($collection as $item) {
            if ($item['status'] === '1') {
                $sku = $item['product_sku'];
                if (array_key_exists($sku, $resultArr)) {
                    $resultArr[$item['product_sku']]['in_stock'] += 1;
                    $resultArr[$item['product_sku']]['total_value'] += (float) $item['voucher_value'];
                } else {
                    $subArr = [
                        'supplier_name' => $item['supplier_name'],
                        'material' => $this->getManufacturerMaterialNumbers($this->getManufacturerIdByLabel($item['supplier_name'])),
                        'voucher_value' => (float) $item['voucher_value'],
                        'in_stock' => 1,
                        'total_value' => (float) $item['voucher_value'],
                    ];
                    $resultArr[$item['product_sku']] = [];
                    $resultArr[$item['product_sku']] = $subArr;
                }
            }
        }

        return $resultArr;
    }

    public function getManufacturerIdByLabel($manufacturerLabel)
    {
        $attribute = $this->eavConfig->getAttribute('catalog_product', 'manufacturer');
        $options = $attribute->getSource()->getAllOptions(false);
        foreach ($options as $option) {
            if ($option['label'] === $manufacturerLabel) {
                return $option['value'];
            }
        }

        return null;
    }

    public function getManufacturerMaterialNumbers($manufacturerId): string
    {
        $materialNumbers = '';
        if ($this->getMaterialNumbers()) {
            $numbersArr = json_decode($this->getMaterialNumbers(), true);
            if (is_array($numbersArr)) {
                foreach ($numbersArr as $item) {
                    if ($item['manufacturer'] === $manufacturerId) {
                        if ($item['material_number'] && ! empty($item['material_number'])) {
                            if ($materialNumbers !== '') {
                                $materialNumbers .= ';';
                            }
                            $materialNumbers .= $item['material_number'];
                        }
                    }
                }
            }
        }

        return $materialNumbers;
    }

    public function getManufacturerCategories($manufacturerId): array
    {
        $categories = [];
        if ($this->getCategories()) {
            $categoriesArr = json_decode($this->getCategories(), true);
            if (is_array($categoriesArr)) {
                foreach ($categoriesArr as $item) {
                    if ($item['manufacturer'] === $manufacturerId) {
                        if ($item['category'] && ! empty($item['category'])) {
                            $manufacturerCategories = $item['category'];
                            $categoryNames = [];
                            foreach ($manufacturerCategories as $category) {
                                try {
                                    $categoryName = $this->categoryRepository->get($category)->getName();
                                } catch (NoSuchEntityException $e) {
                                    $categoryName = false;
                                }
                                if ($categoryName) {
                                    array_push($categoryNames, $categoryName);
                                }
                            }
                            array_push($categories, $categoryNames);
                        }
                    }
                }
            }
        }

        return $categories;
    }

    private function getProductsByManufacturer($manufacturerId)
    {
        $filter = $this->filterBuilder
            ->setField('manufacturer')
            ->setValue($manufacturerId)
            ->setConditionType('eq')
            ->create();
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilters([$filter])
            ->create();
        return $this->productRepository->getList($searchCriteria)->getItems();
    }

    private function getProductData($product, $requestType)
    {
        switch ($requestType) {
            case self::REQUEST_SKU_TYPE:
                return $product->getSku();
            case self::REQUEST_PRICE_TYPE:
                return $product->getPrice();
            default:
                return null;
        }
    }

    private function processWorksheetData($worksheet): array
    {
        $endRow = $worksheet->getHighestDataRow();
        $rowIterator = $worksheet->getRowIterator(1, $endRow);
        $data = [];
        foreach ($rowIterator as $row) {
            $data[] = $this->processRow($row);
        }
        return $this->trimEmptyColumns($data);
    }

    private function processRow($row): array
    {
        return array_map([$this, 'getCellValue'], iterator_to_array($row->getCellIterator()));
    }

    private function getCellValue($cell): mixed
    {
        return Date::isDateTime($cell)
            ? Date::excelToDateTimeObject($cell->getValue())->format('Y-m-d')
            : $cell->getValue();
    }

    private function trimEmptyColumns(array $data): array
    {
        $headerCount = count(array_filter(reset($data) ?? []));
        return array_map(static fn ($row) => array_slice($row, 0, $headerCount), $data);
    }

    private function getMaterialNumbers()
    {
        return $this->scopeConfig->getValue(self::MATERIAL_NUMBERS);
    }

    private function getCategories()
    {
        return $this->scopeConfig->getValue(self::CATEGORIES);
    }

    public function getManufacturerDiscounts(): array
    {
        $resArr = [];
        $collection = $this->ruleCollectionFactory->create()
            ->addFieldToFilter('is_active', 1)
            ->addFieldToFilter('simple_action', 'by_percent');
        if ($collection->getSize() > 0) {
            foreach ($collection as $item) {
                $conditionsArr = json_decode($item->getConditionsSerialized(), true);
                if (isset($conditionsArr['conditions']) && !empty($conditionsArr['conditions'])) {
                    $conditions = $conditionsArr['conditions'];
                    foreach ($conditions as $condition) {
                        if ($condition['attribute'] == 'manufacturer' && $condition['operator'] == '==') {
                            if (array_key_exists($condition['value'], $resArr)) {
                                if ((float)$resArr[$condition['value']] < (float)$item['discount_amount'] ) {
                                    $resArr[$condition['value']] = $item['discount_amount'];
                                }
                            } else {
                                $resArr[$condition['value']] = $item['discount_amount'];
                            }
                        }
                    }
                }
            }
        }

        return $resArr;
    }
}
