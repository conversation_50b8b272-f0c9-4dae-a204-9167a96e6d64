<?php

declare(strict_types=1);

namespace Vivo\SerialNumber\Cron;

use CopeX\SerialNumber\Api\SerialNumberRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;
use Vivo\SerialNumber\Helper\Data;
use Vivo\SerialNumber\Helper\XlsxHelper;
use Vivo\SerialNumber\Model\ReportFactory;
use Vivo\SerialNumber\Model\ReportRepository;
use Vivo\SerialNumber\Ui\Component\Listing\Columns\ManualCron;

class YearReport
{
    protected const CRON_JOB_CONFIGURATION = 'vivo_serialnumber/serial_number/year_report_generate';

    protected LoggerInterface $logger;

    protected ScopeConfigInterface $scopeConfig;

    protected ResourceConnection $resource;

    protected Xlsxhelper $xlsxHelper;

    protected ReportFactory $reportFactory;

    protected ReportRepository $reportRepository;

    protected SerialNumberRepositoryInterface $serialNumberRepository;

    protected SearchCriteriaBuilder $searchCriteriaBuilder;

    protected Data $dataHelper;

    public function __construct(
        LoggerInterface $logger,
        ScopeConfigInterface $scopeConfig,
        ResourceConnection $resource,
        XlsxHelper $xlsxHelper,
        ReportFactory $reportFactory,
        ReportRepository $reportRepository,
        SerialNumberRepositoryInterface $serialNumberRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        Data $dataHelper
    ) {
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
        $this->resource = $resource;
        $this->xlsxHelper = $xlsxHelper;
        $this->reportFactory = $reportFactory;
        $this->reportRepository = $reportRepository;
        $this->serialNumberRepository = $serialNumberRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->dataHelper = $dataHelper;
    }

    public function execute(): void
    {
        if ($this->scopeConfig->getValue(self::CRON_JOB_CONFIGURATION)) {
            $this->logger->info('The annual vouchers report generation started');
            try {
                if ($filepath = $this->xlsxHelper->generateXLSXFile($this->dataHelper->getAllVouchers())) {
                    $reportExport = $this->reportFactory->create();
                    $reportExport->setYear($this->xlsxHelper->getCurrentTime('Y'));
                    $reportExport->setCreationDate($this->xlsxHelper->convertDateToSQLFormat($this->xlsxHelper->getCurrentTime('Y_m_d-H-i-s')));
                    $reportExport->setType(ManualCron::CRONJOB_LABEL);
                    $reportExport->setDownloadLink($this->xlsxHelper->normalizeFileLink($filepath, XlsxHelper::REPORT_DIR));
                    $this->reportRepository->save($reportExport);
                    $this->logger->info('The annual vouchers report has been successfully created');
                } else {
                    $this->logger->info('The annual vouchers report can not be created');
                }
            } catch (\Exception $e) {
                $this->logger->error('Error in vouchers report cron job: ' . $e->getMessage());
            }
            $this->logger->info('The annual vouchers report generation ended');
        }
    }
}
