<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Setup;

use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Eav\Model\Entity\Attribute\SetFactory as AttributeSetFactory;

/**
 * @codeCoverageIgnore
 */
class InstallData implements InstallDataInterface
{
    /**
     * Staff setup factory
     *
     * @var StaffSetupFactory
     */
    private $_staffSetupFactory;
	
	/**
     * Init
     *
     * @param StaffSetupFactory $staffSetupFactory
     */
    public function __construct(
		StaffSetupFactory $staffSetupFactory
	)
    {
        $this->_staffSetupFactory = $staffSetupFactory;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
		/** @var StaffSetup $staffSetup */
        $staffSetup = $this->_staffSetupFactory->create(['setup' => $setup]);
		$setup->startSetup();
		
		$staffSetup->installEntities();

		// add customer attributes
		$attributesInfo = [
            'primary_staff_id' => [
                'label' => 'Primary Staff',
                'type' => 'int',
                'input' => 'select',
                'is_visible' => true,
                'is_required' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
				'is_filterable_in_grid' => false,
				'is_searchable_in_grid' => false,
            ],
		];
		foreach ($attributesInfo as $attributeCode => $attributeParams) {
            $staffSetup->addAttribute('customer', $attributeCode, $attributeParams);
			
			$attribute = $staffSetup->getEavConfig()->getAttribute('customer', $attributeCode);
			$attribute->setData(
				'used_in_forms',
				['adminhtml_customer']
			)
			->setData('is_used_for_customer_segment', true)
			->setData('is_system', 0)
			->setData('is_required', 0)
			->setData('is_user_defined', 1)
			->setData('is_visible', 1)
			->setData('source_model', 'MageB2B\Staff\Model\Config\Source\Staff')
			;
			
			$attribute->save();
		}

		$setup->endSetup();
	}
}