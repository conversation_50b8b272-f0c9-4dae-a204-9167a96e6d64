<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Model\Config\Source;

class Staff extends \Magento\Eav\Model\Entity\Attribute\Source\Table
{
    /** @var \MageB2B\Staff\Model\ResourceModel\Staff\CollectionFactory */
	protected $staffCollectionFactory;
	
	protected $_options;

	public function __construct(
        \Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory $attrOptionCollectionFactory,
        \Magento\Eav\Model\ResourceModel\Entity\Attribute\OptionFactory $attrOptionFactory,
        \MageB2B\Staff\Model\ResourceModel\Staff\CollectionFactory $staffCollectionFactory
    ) {
        parent::__construct($attrOptionCollectionFactory, $attrOptionFactory);
        $this->staffCollectionFactory = $staffCollectionFactory;
    }

    /**
     * @return array
     */
    public function getAllOptions($withEmpty = true, $defaultValues = false)
    {
		if (!$this->_options)
        {
			$staffCollection = $this->staffCollectionFactory->create();
			
			$options = array();
			$options[] = array(
				'label'	=>	__('None'),
				'value'	=>	0,
			);
			if ($staffCollection->count()>0)
            {
				foreach($staffCollection as $staff)
                {
					$options[] = array(
						'label'	=>	$staff->getEmail(),
						'value'	=>	$staff->getStaffId(),
					);
				}
			}
			$this->_options = $options;
		}
		return $this->_options;
    }
}
