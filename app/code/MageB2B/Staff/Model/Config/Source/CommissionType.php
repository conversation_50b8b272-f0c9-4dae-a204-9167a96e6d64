<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Model\Config\Source;

class CommissionType
{	
	const TOTAL_EXCL_TAX = 1;
	const TOTAL_INCL_TAX = 2;
	const DISCOUNT_ON_PRODUCT = 3;

	protected $_commissionTypes;

    /**
     * Options getter
     * @return array
     */
    public function getAllOptions()
    {
		if (!$this->_commissionTypes)
        {
			$options = array();
			$options[] = array(
				'label'	=>	__('On net total price excl. tax'),
				'value'	=>	self::TOTAL_EXCL_TAX,
			);
			
			$options[] = array(
				'label'	=>	__('On net total price incl. tax'),
				'value'	=>	self::TOTAL_INCL_TAX,
			);
			
			$options[] = array(
				'label'	=>	__('As discount: on every product'),
				'value'	=>	self::DISCOUNT_ON_PRODUCT,
			);
			
			$this->_commissionTypes = $options;
		}
		return $this->_commissionTypes;
    }

    /**
     * @return array
     */
    public function toOptionArray()
    {
		return $this->getAllOptions();
	}
}
