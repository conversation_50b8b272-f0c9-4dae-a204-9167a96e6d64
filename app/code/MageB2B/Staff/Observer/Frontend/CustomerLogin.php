<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Observer\Frontend;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class CustomerLogin implements ObserverInterface
{
	/** @var \MageB2B\Staff\Helper\Data */
    protected $_staffHelper;
	
	/** @var \MageB2B\Staff\Helper\Common */
    protected $_commonHelper;
	
	/** @var \Magento\Framework\UrlFactory */
    protected $_urlFactory;

    /** @var \Magento\Framework\Message\ManagerInterface */
    protected $_messageManager;

    /**
     * CustomerLogin constructor.
     * @param \MageB2B\Staff\Helper\Data $staffHelper
     * @param \MageB2B\Staff\Helper\Common $commonHelper
     * @param \Magento\Framework\UrlFactory $urlFactory
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     */
	public function __construct(
		\MageB2B\Staff\Helper\Data $staffHelper,
		\MageB2B\Staff\Helper\Common $commonHelper,
		\Magento\Framework\UrlFactory $urlFactory,
        \Magento\Framework\Message\ManagerInterface $messageManager
	)
	{
		$this->_staffHelper  = $staffHelper;
		$this->_commonHelper = $commonHelper;
		$this->_urlFactory   = $urlFactory;
		$this->_messageManager   = $messageManager;
	}

    /**
     * set internal staff id to work with
     *
     * @param Observer $observer
     */
	public function execute(Observer $observer)
	{
		$customer = $observer->getCustomer();
		$customerSession = $this->_commonHelper->getCustomerSession();
		if ($customerSession->isLoggedIn()) {
			// check if current customer is staff
			$resource = $this->_commonHelper->getResource();
            $select = $resource->getConnection()->select();
            $select->from(array('staff' => $resource->getTableName("customer_salesstaff")));
            $select->join(
                array('c' => $resource->getTableName("customer_entity")),
                'c.email = staff.email',
                array()
            );
            $select->where('c.entity_id = :entity_id');

			$bind = array(
				'entity_id' => $customer->getId(),
			);
			
			$staff = $resource->getConnection()->fetchRow($select, $bind);
			
			if (!empty($staff)) {
				$staffId = $staff['staff_id'];
				$status = $staff['status'];
				// now check whther staff is active? If yes then allow customer login otherwise just logout customer from here
				if ($status == 0) {
					$customerSession->logout()->regenerateId();
                    $this->_messageManager->addErrorMessage(__('Your account is deactivated.'));
					return;
				}
				
				$customerSession->setStaffId($staffId);
                $customerSession->setStaffCustomerId($customer->getId());
				
				// set custom beforeAuthUrl
				if ($loginPostUrlHandle = $this->_staffHelper->getStoreConfig('staff_loginpost_redirect')) {
					// check MageB2B\Sublogin\Observer\CustomerLogin.php for use of Zend_Uri
					$uri = \Zend_Uri::check($loginPostUrlHandle);
					if ($uri) {
						$customerSession->setBeforeAuthUrl($loginPostUrlHandle);
						$customerSession->setAfterAuthUrl($loginPostUrlHandle);
					} else {
						$urlFactory = $this->_urlFactory->create();
						$url = $urlFactory->getUrl($loginPostUrlHandle);
						$customerSession->setBeforeAuthUrl($url);
						$customerSession->setAfterAuthUrl($url);
					}
				}
			} else {
				$customerSession->setStaffId(null);
			}
		} else {
			$customerSession->setStaffId(null);
		}
	}
}