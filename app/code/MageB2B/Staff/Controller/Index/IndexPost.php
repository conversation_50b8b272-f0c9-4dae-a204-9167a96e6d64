<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */

namespace MageB2B\Staff\Controller\Index;

class IndexPost extends \Magento\Customer\Controller\AbstractAccount
{
	private $updateParams = array(
        'firstname',
        'lastname',
        'email',
        'phone',
    );

	/** @var \MageB2B\Staff\Helper\Data */
	private $_staffHelper;

	/** @var \MageB2B\Staff\Helper\Common */
	private $_commonHelper;

	/** @var \Magento\Customer\Model\ResourceModel\CustomerRepository */
	private $customerRepository;

	/** @var \MageB2B\Staff\Model\StaffRepository */
	private $staffRepository;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \MageB2B\Staff\Helper\Data $staffHelper,
        \MageB2B\Staff\Helper\Common $commonHelper,
        \Magento\Customer\Model\ResourceModel\CustomerRepository $customerRepository,
        \MageB2B\Staff\Model\StaffRepository $staffRepository
    ) {
        parent::__construct($context);
        $this->_staffHelper = $staffHelper;
        $this->_commonHelper = $commonHelper;
        $this->customerRepository = $customerRepository;
        $this->staffRepository = $staffRepository;
    }

    /**
     * Index action
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
    	if (!$this->_checkStaffAccess()) {
    		$resultRedirect = $this->resultRedirectFactory->create();
    		return $resultRedirect->setPath('/');
    	}
		$customerSession = $this->_commonHelper->getCustomerSession();

		$resultRedirect = $this->resultRedirectFactory->create();
        $postData = $this->getRequest()->getPost();
		if (!$postData || ! count($postData)) {
            return $resultRedirect->setPath('*/*/');
        }

        $staffModel = $this->_staffHelper->getCurrentStaff();
        $websiteId = $this->_commonHelper->getStoreManager()->getWebsite()->getId();
        foreach ($postData as $key => $value) {
            if (in_array($key, $this->updateParams)) {
                $staffModel->setData($key, $value);
            }
        }
        // If password change was requested then add it to common validation scheme
        if ($this->getRequest()->getParam('change_password')) {
            $currPass   = $this->getRequest()->getPost('current_password');
            $newPass    = $this->getRequest()->getPost('password');
            $confPass   = $this->getRequest()->getPost('confirmation');
            $customer = $customerSession->getCustomer();
            $oldPass = $customer->getPasswordHash();

            if (strpos((string)$oldPass, ':') !== false) {
                list($_salt, $salt) = explode(':', $oldPass);
            } else {
                $salt = false;
            }

            if ($customer->hashPassword($currPass, $salt) == $oldPass) {
                if (strlen($newPass)) {
                    /**
                     * Set entered password and its confirmation - they
                     * will be validated later to match each other and be of right length
                     */
                    $customer->setPassword($newPass);
                    $customer->setPasswordConfirmation($confPass);
                    $staffModel->setPassword($newPass);
                } else {
                    $errors[] = __('New password field cannot be empty.');
                }
            } else {
                $errors[] = __('Invalid current password');
            }
        }
        if ($postData['email'] != $staffModel->getEmail()) {
            try{
                $customerDataModel = $this->customerRepository->get($postData['email'], $websiteId);
                if ($customerDataModel->getId()) {
                    $this->messageManager->addErrorMessage(__('Email already exists. Please choose another one.'));
                    return $resultRedirect->setPath('*/*/index');
                }
            }
            catch (\Exception $e) {}
        }
		try {
            $this->staffRepository->save($staffModel);
			$this->messageManager->addSuccessMessage(__('Account was successfully updated.'));
		} catch (\Exception $e) {
			$this->messageManager->addExceptionMessage($e, $e->getMessage());
		}
		return $resultRedirect->setPath('*/*/');
    }

	 /**
     * Check the permission to run it
     *
     * @return bool
     */
    protected function _isAllowed()
    {
		if ($this->_staffHelper->getCurrentStaff()) {
			return true;
		}
		return false;
    }

	/**
     * check staff access
     */
    private function _checkStaffAccess()
    {
		$customerSession = $this->_commonHelper->getCustomerSession();
        if (!$customerSession->isLoggedIn() || is_null($customerSession->getStaffId()))
        {
            $this->messageManager->addErrorMessage(__('You are not allowed to view this page.'));
			return false;
        }
        return true;
    }
}
