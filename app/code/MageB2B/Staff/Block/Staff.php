<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
namespace MageB2B\Staff\Block;
use Magento\Customer\Model\AccountManagement;

/**
 * Sublogin list
 */
class Staff extends \Magento\Framework\View\Element\Template
{
    /**
     * number of displayed items
     *
     * @var int
     */
	protected $_pageSize = 10;

    /**
     * total count of items
     *
     * @var int
     */
	protected $_totalCount = 0;

    /**
     * list of countries
     *
     * @var
     */
	protected $_countries;

	/** @var \MageB2B\Staff\Helper\Data */
	protected $_staffHelper;

	/** @var \MageB2B\Staff\Helper\Common */
	protected $_commonHelper;

	/** @var \MageB2B\Staff\Model\StaffRepository */
	protected $staffRepository;

	/** @var \Magento\Customer\Model\CustomerFactory */
	protected $_customerFactory;

    /** @var \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory */
    protected $customerCollectionFactory;

	/** @var \Magento\Customer\Model\AddressFactory */
	protected $_customerAddressFactory;

    /** @var \Magento\Customer\Model\ResourceModel\AddressFactory */
    protected $customerAddressResourceFactory;

    /** @var \Magento\Customer\Model\ResourceModel\Address\CollectionFactory */
    protected $customerAddressCollectionFactory;

	/** @var \Magento\Customer\Model\ResourceModel\AddressRepository */
	protected $_customerAddressRepository;

	public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \MageB2B\Staff\Helper\Data $staffHelper,
        \MageB2B\Staff\Helper\Common $commonHelper,
        \MageB2B\Staff\Model\StaffRepository $staffRepository,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory,
        \Magento\Customer\Model\AddressFactory $customerAddressFactory,
        \Magento\Customer\Model\ResourceModel\AddressFactory $customerAddressResourceFactory,
        \Magento\Customer\Model\ResourceModel\Address\CollectionFactory $customerAddressCollectionFactory,
		\Magento\Customer\Model\ResourceModel\AddressRepository $customerAddressRepository,
        array $data = []
    ) {
        $this->_staffHelper               = $staffHelper;
        $this->_commonHelper              = $commonHelper;
        $this->staffRepository            = $staffRepository;
        $this->_customerFactory           = $customerFactory;
        $this->customerCollectionFactory  = $customerCollectionFactory;
        $this->_customerAddressFactory    = $customerAddressFactory;
        $this->customerAddressResourceFactory = $customerAddressResourceFactory;
        $this->customerAddressCollectionFactory = $customerAddressCollectionFactory;
        $this->_customerAddressRepository = $customerAddressRepository;
        parent::__construct($context, $data);
    }

	/**
     * @return \MageB2B\Staff\Model\Staff
     */
    public function getStaff()
    {
        if (!$this->hasData('staff')) {
            $this->setData('staff', $this->_staffHelper->getCurrentStaff());
        }
        return $this->getData('staff');
    }

    /**
     * get the current customer collection
     * depending on the current staff user
     *
     * @return string
     */
	public function getCustomers($additionalAttributes = [])
    {
        $staffId = $this->_commonHelper->getCustomerSession()->getStaffId();
        $staffModel = $this->staffRepository->getById($staffId);
		$customerCollection = $this->getCustomerCollectionWithStaff();
        $customerCollection->addAttributeToSelect($additionalAttributes);
		if (!$staffModel->getAccessAllCustomers()) {
			$customerCollection->getSelect()->where('main_table.staff_id = '.$staffModel->getStaffId());
		}

		// filtering
		$search = $this->getRequest()->getParam('search');
		// check whether billing_address or shipping address exists in search array
		// if yes then get address id based on filtering text and then filter customer by address id (default_billing || default_shipping)
		if(isset($search['billing_address'])) {
			$billingAddressSearchTerms = $search['billing_address'];
			$addressCollection = '';
			$customerAddressIds = array();
			foreach ($billingAddressSearchTerms as $billingAddressSearchTermKey=>$billingAddressSearchTerm) {
				if (trim((string)$billingAddressSearchTerm) != '') {
					if ($addressCollection == '') {
						$addressCollection = $this->customerAddressCollectionFactory->create();
					}
					$addressCollection->addFieldToFilter($billingAddressSearchTermKey, array('like'=>'%'.$billingAddressSearchTerm.'%'));
				}
			}

			if ($addressCollection != '') {
				if ($addressCollection->count()) {
					$customerIdsToFilterBasedOnAddress = array();
					foreach ($addressCollection as $address) {
						$customerAddressIds[$address->getParentId()][] = $address->getId(); // getting customer_id by using getParentId()
						$customerIdsToFilterBasedOnAddress[] = $address->getParentId();
					}

					if (count($customerIdsToFilterBasedOnAddress)>0) {
						$customerCollection->getSelect()->where("e.entity_id IN (".implode(",", $customerIdsToFilterBasedOnAddress).")");
					}
				} else {
					$customerCollection->getSelect()->where("e.entity_id IN (0)");
				}
			}
		}
		// unset billing_address from search array to ignore it in next search process
		unset($search['billing_address']);
		if(isset($search['shipping_address'])) {
			$shippingAddressSearchTerms = $search['shipping_address'];
			$addressCollection = "";
			$customerShippingAddressIds = array();
			foreach($shippingAddressSearchTerms as $shippingAddressSearchTermKey=>$shippingAddressSearchTerm) {
				if (trim((string)$shippingAddressSearchTerm)!="") {
					if ($addressCollection == "") {
						$addressCollection = $this->customerAddressCollectionFactory->create();
					}
					$addressCollection->addFieldToFilter($shippingAddressSearchTermKey, array('like'=>'%'.$shippingAddressSearchTerm.'%'));
				}
			}

			if ($addressCollection != '') {
				if ($addressCollection->count()) {
					$customerIdsToFilterBasedOnAddress = array();
					foreach ($addressCollection as $address) {
						$customerShippingAddressIds[$address->getParentId()][] = $address->getId(); // getting customer_id by using getParentId()
						$customerIdsToFilterBasedOnAddress[] = $address->getParentId();
					}

					if (count($customerIdsToFilterBasedOnAddress) > 0) {
						$customerCollection->getSelect()->where("e.entity_id IN (".implode(",", $customerIdsToFilterBasedOnAddress).")");
					}
				} else {
					$customerCollection->getSelect()->where("e.entity_id IN (0)");
				}
			}
		}
		// unset shipping_address from search array to ignore it in next search process
		unset($search['shipping_address']);

		if (is_array($search) && count($search) > 0) {
			foreach ($search as $fieldId => $fieldValue){
				if (trim((string)$fieldValue) == '') {
					continue;
				}

				if ($customerCollection instanceof \MageB2B\Staff\Model\ResourceModel\Customer\Collection) {
					$customerCollection->addFieldToFilter($fieldId, array('like'=>'%'.$fieldValue.'%'));
				} else {
					if (strpos((string)$fieldId, 'e.') >= 0) {
						/**
						 * This means, this is customer_entity field, so need to use addAttributeToFilter
						 */
						$fieldId = str_replace('e.', '', $fieldId);
						$customerCollection->addAttributeToFilter($fieldId, array('like'=>'%'.$fieldValue.'%'));
					} else {
						$customerCollection->getSelect()->where($fieldId.' LIKE "%'.$fieldValue.'%"');
					}
				}
			}
		}

		// apply ordering
		$sortIndex = $this->_getSortIndex();
		$sortDir = $this->_getSortingDir();
		if (strpos((string)$sortIndex, 'e.') >= 0) {
			/**
			 * This means, this is customer_entity field, so need to use addAttributeToSort
			 */
			$sortIndex = str_replace('e.', '', $sortIndex);
			$customerCollection->addAttributeToSort($sortIndex, strtoupper($sortDir));
		} else {
			$customerCollection->getSelect()->order($sortIndex.' '.strtoupper($sortDir));
		}

		// apply group by
		$customerCollection->getSelect()->group('e.entity_id');

		$customerCollectionClone = clone $customerCollection;
		$this->_setTotalCount($customerCollectionClone->count());

		// apply paging
		$currentPage = $this->_getCurrentPage();
		$pageSize = $this->_getPageSize();
		$customerCollection->getSelect()->limitPage($currentPage, $pageSize);

		// set addresses in customer
		foreach($customerCollection as $customer) {
			if (isset($customerAddressIds[$customer->getEntityId()][0])) {
				$addressId = $customerAddressIds[$customer->getEntityId()][0];
				$customer->setSearchedAddressId($addressId);
			}

			if (isset($customerShippingAddressIds[$customer->getEntityId()][0])) {
				$addressId = $customerShippingAddressIds[$customer->getEntityId()][0];
				$customer->setSearchedShippingAddressId($addressId);
			}
		}

        return $customerCollection;
    }

    /**
     * @return mixed
     */
    protected function getCustomerCollectionWithStaff()
    {
		$customerCollection = $this->customerCollectionFactory->create()
			->addAttributeToSelect('firstname')
			->addAttributeToSelect('lastname')
			->addAttributeToSelect('default_billing')
			->addAttributeToSelect('default_shipping');

		$resource = $this->_commonHelper->getResource();
		$customerCollection->getSelect()->joinLeft(
			array('main_table'=>$resource->getTableName('customer_salesstaff_customer')),
			'e.entity_id = main_table.customer_id',
			array('main_table.staff_id as main_staff_id')
		);

		$customerCollection->getSelect()->joinLeft(
			array('staff'=>$resource->getTableName('customer_salesstaff')),
			'staff.staff_id = main_table.staff_id',
			array('staff.staff_id as staff.staff_id', 'staff.firstname as staff.firstname', 'staff.lastname as staff.lastname', 'staff.email as staff.email', 'staff.phone as staff.phone')
		);

		return $customerCollection;
	}

	/**
     * get current user from session
     *
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomerFromSession()
    {
        return $this->_commonHelper->getCustomerSession()->getCustomer();
    }

    /**
     * pager params
     *
     * @return int|mixed
     */
	public function _getCurrentPage()
	{
		return ($this->getRequest()->getParam('page')) ? $this->getRequest()->getParam('page') : 1;
	}

    /**
     * @param int $totalCount
     */
	protected function _setTotalCount($totalCount = 0)
	{
		$this->_totalCount = $totalCount;
	}

    /**
     * @return int
     */
	public function _getTotalCount()
	{
		return $this->_totalCount;
	}

    /**
     * get page size
     * @return int
     */
	public function _getPageSize()
	{
		return ($this->_pageSize) ? $this->_pageSize : 10;
	}

    /**
     * return page count
     * @param $itemCount
     * @return float|int
     */
	public function _getPageCount($itemCount)
	{
		$pageSize = $this->_getPageSize();
		$pageCount = ceil($itemCount / $pageSize);
		if ($pageCount <= 1) {
			$pageCount = 1;
		}
		return $pageCount;
	}

    /**
     * @return mixed|string
     */
	public function _getSortIndex()
	{
		$sort = $this->getRequest()->getParam('sort');
		if (!$sort) {
			$tableName = $this->_getCustomerEntityTableName();
			$sort = $tableName.'.entity_id';
		}
		return $sort;
	}

	/**
	 * return customer_entity table name (with prefix if exist)
	 */
	public function _getCustomerEntityTableName()
	{
		return 'e';
	}

    /**
     * if $forLink = true|1 that it means we need to return value to use with link.
     * So we need to return desc if current sorting is asc and asc of current sorting is desc
     *
     * @param bool $forLink
     * @return mixed|string
     */
	public function _getSortingDir($forLink = false)
	{
		$dir = $this->getRequest()->getParam('dir');
		if ($forLink) {
			if ($dir == "asc") {
				$dir = "desc";
			} else {
				$dir = "asc";
			}
		}

		if (!$dir) {
            $dir = 'desc';
        }
		return $dir;
	}

    /**
     * @param bool $sortIndex
     * @return string
     */
	public function _getSortingArrow($sortIndex = false)
	{
		if (!$sortIndex) {
			if ($this->_getSortingDir() == "asc") {
				return "&uarr;";
			} else {
				return "&darr;";
			}
		}

		if ($sortIndex == $this->_getSortIndex()) {
			if ($this->_getSortingDir() == "asc") {
				return "&uarr;";
			} else {
				return "&darr;";
			}
		}
	}

	/**
	 * @return string
	 */
	public function _getSortingTitle()
	{
        $sortingTitle = null;
		if ($this->_getSortingDir() == "asc") {
			$sortingTitle = __("Set Descending Direction");
		} else {
			$sortingTitle = __("Set Ascending Direction");
		}
		return $sortingTitle;
	}


    /**
     * @return array
     */
	public function _getStaffFields()
	{
		$staffFields = array();
		// disable this feature if customer can have multiple staff
		if ($this->_staffHelper->canCustomerHaveMultipleStaff()) {
			return $staffFields;
		}

		if ($this->_staffHelper->isSearchCustomersByStaffEnable()) {
			$staffFields['staff.firstname']['label'] = __('Staff Firstname');
			$staffFields['staff.lastname']['label'] = __('Staff Lastname');
			$staffFields['staff.email']['label'] = __('Staff Email');
			$staffFields['staff.phone']['label'] = __('Staff Phone');
		}
		return $staffFields;
	}

    /**
     * return address fields
     *
     * @return array
     */
	protected function _getAddressFields()
	{
		$fields = array();
		$fields['company']['label'] = __('Company');
		$fields['street']['label'] = __('Street');
		$fields['postcode']['label'] = __('Postcode');
		$fields['city']['label'] = __('City');
		return $fields;
	}

    /**
     * @return array
     */
	public function _getBillingAddressFields()
	{
		return $this->_getAddressFields();
	}

    /**
     * @return array
     */
	public function _getShippingAddressFields()
	{
		return $this->_getAddressFields();
	}

	/**
	 * @return \MageB2B\Staff\Helper\Data
	 */
	public function getStaffHelper()
	{
		return $this->_staffHelper;
	}

	/**
	 * @return \Magento\Customer\Model\AddressFactory
	 */
	public function getCustomerAddressFactory()
	{
		return $this->_customerAddressFactory;
	}

	public function loadCustomerAddress($addressId)
    {
        $customerAddress = $this->getCustomerAddressFactory()->create();
        $this->customerAddressResourceFactory->create()->load($customerAddress, $addressId);
    	return $customerAddress;
    }

	/**
     * return menu links for menu
     *
     * @return array
     */
    public function getMenuLinks()
    {
    	$menuLinks = array(
            'staff' => array(
                'id' => 'staff.info',
                'name' => __('My account'),
                'url' => $this->getUrl('salesstaff/index/index'),
            ),
			'customers' => array(
                'id' => 'staff.orders',
                'name' => __('My customers'),
                'url' => $this->getUrl('salesstaff/index/customers'),
            ),
            'orders' => array(
                'id' => 'staff.orders',
                'name' => __('My orders'),
                'url' => $this->getUrl('salesstaff/index/orders'),
            )
        );

        if ($this->_staffHelper->getStoreConfig('enable_creating_new_customer')) {
        	$menuLinks['customer-register'] = array(
        		'id' => 'staff.customer',
        		'name' => __('Register a new customer'),
        		'url' => $this->getUrl('salesstaff/index/register'),
        	);
        }
        return $menuLinks;
    }

    /**
     * @return bool
     */
    public function canDeleteCustomer()
    {
    	return $this->_staffHelper->getStoreConfig('staff_can_delete_customer');
    }

    /**
     * @return string
     */
    public function getDeleteCustomerUrl()
    {
    	return $this->getUrl('salesstaff/index/deleteCustomer');
    }


    /**
     * Get minimum password length
     *
     * @return string
     * @since 100.1.0
     */
    public function getMinimumPasswordLength()
    {
        return $this->_scopeConfig->getValue(AccountManagement::XML_PATH_MINIMUM_PASSWORD_LENGTH);
    }

    /**
     * Get minimum password length
     *
     * @return string
     * @since 100.1.0
     */
    public function getRequiredCharacterClassesNumber()
    {
        return $this->_scopeConfig->getValue(AccountManagement::XML_PATH_REQUIRED_CHARACTER_CLASSES_NUMBER);
    }

}
