<?php
/**
 * @package MageB2B_Staff 1.0.0
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright AIRBYTES GmbH
 * @license https://www.mageb2b.de/en/license-terms
 */
 
namespace MageB2B\Staff\Block\Adminhtml\System\Config\Form\Fieldset;

class RequiredAttributes extends \Magento\Config\Block\System\Config\Form\Fieldset {

	protected $_dummyElement;
    protected $_fieldRenderer;
    protected $_values;
	
    /** @var \MageB2B\Staff\Model\Config\Source\CustomerAttributes */
    protected $customerAttributes;
    
    /** @var \MageB2B\Staff\Model\StaffFactory */
    protected $staffFactory;
    
    /** @var \Magento\Framework\ObjectManagerInterface */
    private $objectManager;
    
	public function __construct(
		\Magento\Backend\Block\Context $context,
        \Magento\Backend\Model\Auth\Session $authSession,
        \Magento\Framework\View\Helper\Js $jsHelper,
		\MageB2B\Staff\Model\Config\Source\CustomerAttributes $customerAttributes,
		\MageB2B\Staff\Model\StaffFactory $staffFactory,
		\Magento\Framework\ObjectManagerInterface $objectManager
    ) {
		parent::__construct($context, $authSession, $jsHelper);
		
		$this->customerAttributes = $customerAttributes;
		$this->staffFactory = $staffFactory;
		$this->objectManager = $objectManager;
	}
	
	public function render(\Magento\Framework\Data\Form\Element\AbstractElement $element)
    {
		$this->setElement($element);
        $html = $this->_getHeaderHtml($element);
		
        $customerAttributes = $this->customerAttributes->getAttributes();
        $staffModel = $this->staffFactory->create();
        $attributeExistForConfig = false;
        foreach ( $customerAttributes as $customerAttribute){
        	if (!($customerAttribute->getIsRequired() && $customerAttribute->getIsVisible())) {
				continue;
			}
			
			$attributeCodeInCamelCase = str_replace("_", "", ucwords($customerAttribute->getAttributeCode(), '_'));
			
			if (method_exists($staffModel, 'get'.$attributeCodeInCamelCase)) {
				continue;
			}

            $attributeExistForConfig = true;
        	$html.= $this->_getFieldHtml($element, $customerAttribute);
		}

		if (!$attributeExistForConfig) {
            $html.= "<tr><td>".__('No attributes exist to show in this area.')."</td></tr>";
        }


        $html .= $this->_getFooterHtml($element);
 
        return $html;
    }
    
    //this creates a dummy element so you can say if your config fields are available on default and website level - you can skip this and add the scope for each element in _getFieldHtml method
    protected function _getDummyElement($elementType)
    {
		
		if (empty($this->_dummyElement)) {
            $this->_dummyElement = new \Magento\Framework\DataObject(['showInDefault' => 1, 'showInWebsite' => 1]);
        }
        return $this->_dummyElement;
    }
    
    //this sets the fields renderer. If you have a custom renderer tou can change this. 
    protected function _getFieldRenderer()
    {
        if (empty($this->_fieldRenderer)) {
            $this->_fieldRenderer = $this->_layout->getBlockSingleton(
                'Magento\Config\Block\System\Config\Form\Field'
            );
        }
        return $this->_fieldRenderer;
    }
    
    //this is usefull in case you need to create a config field with type dropdown or multiselect. For text and texareaa you can skip it.
    protected function _getValues()
    {
        if (empty($this->_values)) {
            $this->_values = array(
                array('label'=> __('No'), 'value'=>0),
                array('label'=> __('Yes'), 'value'=>1),
            );
        }
        return $this->_values;
    }
    
    /**
     * this actually gets the html for a field 
     * @param unknown $fieldset
     * @param \Magento\Eav\Model\Entity\Attribute $customerAttribute
     * @return unknown
     */
    protected function _getFieldHtml($fieldset, $customerAttribute)
    {
        $configData = $this->getConfigData();
        $path = 'staff/default_values/'.$customerAttribute->getAttributeCode(); //this value is composed by the section name, group name and field name. The field name must not be numerical (that's why I added 'group_' in front of it)
        if (isset($configData[$path])) {
            $data = $configData[$path];
            $inherit = false;
        } else {
            $data = ""; //(int)(string)$this->getForm()->getConfigRoot()->descend($path);
            $inherit = true;
        }
        
        $fieldType = $this->getFieldType($customerAttribute->getFrontendInput());		
        $e = $this->_getDummyElement($fieldType); //get the dummy element
 
        $field = $fieldset->addField('staff_default_values_'.$customerAttribute->getAttributeCode(), $fieldType, //this is the type of the element (can be text, textarea, select, multiselect, ...)
            array(
            	'name'          => 'groups[default_values][fields]['.$customerAttribute->getAttributeCode().'][value]', //this is groups[group name][fields][field name][value]
            	'label'         => $customerAttribute->getFrontendLabel(), //this is the label of the element
                'value'         => $data, //this is the current value
                'inherit'       => $inherit,
                'can_use_default_value' => $this->getForm()->canUseDefaultValue($e), //sets if it can be changed on the default level
                'can_use_website_value' => $this->getForm()->canUseWebsiteValue($e), //sets if can be changed on website level
            ))->setRenderer($this->_getFieldRenderer());
            
		if ($fieldType == 'select' || $fieldType == 'multiselect') {
       		if ($customerAttribute->getSourceModel()) {
       			$souceModelClass = '\\'.$customerAttribute->getSourceModel();
       			$souceModel = $this->objectManager->get($souceModelClass);
       			$souceModel->setAttribute($customerAttribute);
       			$options = $souceModel->getAllOptions();
       			$field->setValues($options);
       		} else {
       			$field->setValues(array(
       				array(
       					'label'	=>	__('Yes'),
       					'value'	=>	1,
       				),
       				array(
       					'label'	=>	__('No'),
       					'value'	=>	0,
       				),
       			));
       		}
	   	}
	   
		if ($fieldType == 'date') {
			$field->setFormat('Y-MM-dd');
	   	}
       
       return $field->toHtml();
    }
    
	protected function getFieldType($field)
	{
		switch ($field)
		{
			case "select":
			case "boolean":
				return 'select';
			break;
			case "textarea":
				return 'textarea';
			break;
			case "date":
				return 'date';
			break;
			case "text":
				return 'text';
			break;
		}
		return $field;
	}
}