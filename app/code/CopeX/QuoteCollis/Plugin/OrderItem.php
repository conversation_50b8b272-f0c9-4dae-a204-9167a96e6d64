<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 25.04.18
 * Time: 15:30
 */

namespace CopeX\QuoteCollis\Plugin;

class OrderItem extends CartItemRenderer
{

    public function afterGetProductOptions(\Magento\Sales\Model\Order\Item $item, $result)
    {
        if (isset($result['options'])) {
            foreach ($result['options'] as &$option) {
                $this->enhanceOption($option, $item->getProduct()?$item->getProduct()->getColli():"");
            }
        }
        return $result;
    }
}