<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="copex_import_log" resource="default" engine="innodb" comment="copex_import_log Table">
		<column xsi:type="int" name="log_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="log_id"/>
		</constraint>
		<column name="name" nullable="false" xsi:type="varchar" comment="name" length="255"/>
		<index referenceId="COPEX_IMPORT_LOG_NAME" indexType="btree">
			<column name="name"/>
		</index>
		<column name="output" nullable="true" xsi:type="text" comment="output"/>
		<column name="status" nullable="true" xsi:type="smallint" comment="status" identity="false"/>
		<column name="started_at" nullable="true" xsi:type="timestamp" comment="started_at"/>
		<column name="finished_at" nullable="true" xsi:type="timestamp" comment="finished_at"/>
	</table>
	<table name="copex_import_productlog" resource="default" engine="innodb" comment="copex_import_productlog Table">
		<column xsi:type="int" name="productlog_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="productlog_id"/>
		</constraint>
		<column name="sku" nullable="true" xsi:type="varchar" comment="sku" length="255"/>
		<column name="name" nullable="true" xsi:type="text" comment="name"/>
		<column name="reason" nullable="true" xsi:type="text" comment="reason"/>
		<column name="date" nullable="true" xsi:type="timestamp" comment="date"/>
	</table>
</schema>
