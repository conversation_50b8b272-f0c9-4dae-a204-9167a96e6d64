<?php
/**
 * CopeX_Import
 * NOTICE OF LICENSE
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 * @category    CopeX
 * @package     CopeX_Import
 * @copyright   Copyright © 2012 CopeX (http://copex.io/)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 * @ *
 */

namespace CopeX\Import\Helper;

use Monolog\Logger;

class Import extends AbstractHelper
{

    const ATTRIBUTES_KEY = '@attributes';
    const MEDIA_IMPORT_PATH = '/import';

    protected $_today = null;

    /** @var null|array */
    protected $_websiteIds = null;

    protected $_modelStoreManagerInterface = null;

    /** @var null */
    protected $_fileUploader = null;

    protected $logHelper;

    protected $mediaImportPath;

    protected $classFactory;

    protected $eavAttribute;

    //New uploader for M2
    protected $_uploader;

    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \CopeX\Import\Model\Mapper $mapper,
        \CopeX\Import\Model\Template\FilterFactory $templateFilterFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \CopeX\Import\Helper\Log $logHelper,
        \CopeX\Import\Model\ClassFactory $classFactory,
        \Magento\Eav\Model\ResourceModel\Entity\Attribute $eavAttribute,
        \Magento\CatalogImportExport\Model\Import\Uploader $uploader
    )
    {
        parent::__construct($context, $directoryList, $date, $mapper);
        $this->_modelStoreManagerInterface = $storeManager;
        $this->mediaImportPath = $this->directoryList->getPath(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA) .
            self::MEDIA_IMPORT_PATH;
        $this->templateFilterFactory = $templateFilterFactory;
        $this->logHelper = $logHelper;
        $this->classFactory = $classFactory;
        $this->eavAttribute = $eavAttribute;
        $this->_uploader = $uploader;
    }

    /**
     * @return \CopeX\Import\Helper\Log
     */
    public function getLogHelper()
    {
        return $this->logHelper;
    }

    /**
     * @param array $item
     * @param       $limit
     * @return array|null
     */
    public function getAllWebsites($profile, $item, $limit = null)
    {
        if ($this->_websiteIds === null) {
            $this->_websiteIds = [];
            foreach ($this->_modelStoreManagerInterface->getWebsites() as $website) {
                /** @var $website \Magento\Store\Model\Website */

                $this->_websiteIds[$website->getId()] = $website->getCode();
            }
        }

        if ($limit) {
            return array_slice($this->_websiteIds, 0, $limit);
        }

        return $this->_websiteIds;
    }

    /**
     * @param       $item
     * @param       $field
     * @param array $findReplaces
     * @param bool $trim
     * @return mixed|string
     */
    public function findReplace($profile, $item, $field, $findReplaces = [], $trim = false)
    {
        $value = $this->getFieldValue($item, $field, $profile);
        if (!$value) {
            return '';
        }

        foreach ($findReplaces as $findReplace) {
            $findAttributes = $this->getAttributes($findReplace);
            if (strpos((string)$value, $findAttributes['find']) !== false) {
                $value = str_replace($findAttributes['find'], $findAttributes['replace'], $value);
            }
        }

        if ($trim) {
            return trim((string)$value);
        }

        return $value;
    }


    /**
     * @param $profile
     * @param $item
     * @param $field
     * @return string
     */
    public function trim($profile, $item, $field): string
    {
        return trim(
            (string) $this->getFieldValue($item, $field, (string) $profile),
            "- \xC2\xA0\n"
        );
    }


    /**
     * Parse a price thus it fits the import process
     * @param $profile
     * @param $item
     * @param $field
     * @return float
     */
    public function parsePrice($profile, $item, $field)
    {
        $value = $this->getFieldValue($item, $field, $profile);
        // convert "," to "."
        $value = str_replace(',', '.', $value);

        // remove everything except numbers and dot "."
        $value = preg_replace("/[^0-9\.]/", "", $value);

        // remove all seperators from first part and keep the end
        $value = str_replace('.', '', substr($value, 0, -3)) . substr($value, -3);

        // return float
        return (float)$value;
    }

    /**
     * @param string $item
     * @param null|string $field
     * @param string $allowedTags
     * @return string
     */
    public function stripHtmlTags($profile, $item, $field, $allowedTags = '<p><a><br>')
    {
        $value = $this->getFieldValue($item, $field, $profile);
        $content = trim((string)strip_tags($value, $allowedTags));
        return $content ? $content : '<!--empty-->';
    }

    /**
     * @param        $profile
     * @param        $item
     * @param string $value
     * @return string
     */
    public function getLocalizedValue($profile, $item, $value = '')
    {
        $value = $this->getFieldValue($item, $value, $profile);
        return __($value)->render();
    }

    /**
     * Get a simple HTML comment (can't be added through XML due to XML limitations).
     * @param        $item
     * @param string $comment
     * @return string
     */
    public function getHtmlComment($profile, $item, $comment = '')
    {
        return '<!--' . $comment . '-->';
    }

    /**
     * Allows you to format a field using vsprintf
     * @param $item
     * @param $format
     * @param $fields
     * @return string
     */
    public function formatField($profile, $item, $format, $fields)
    {
        $values = [];
        foreach ($fields as $key => $field) {
            $value = $this->getFieldValue($item, $field, $profile);
            $values[$key] = is_array($value) ? reset($value) : $value;
        }

        $result = vsprintf($format, $values);
        return $result;
    }

    /**
     * @param        $item
     * @param        $field
     * @param int $length
     * @param string $etc
     * @param bool $breakWords
     */
    public function truncate($profile, $item, $field, $length = 80, $etc = '…', $breakWords = true)
    {
        $string = $this->getFieldValue($item, $field, $profile);
        $remainder = '';
        return $this->_helperString->truncate($string, $length, $etc, $remainder, $breakWords);
    }

    /**
     * Get multiple values
     * @param $item
     * @param $values
     * @return array
     */
    public function getValueMultiple($profile, $item, $values)
    {
        return array_values($values);
    }

    /**
     * Get the value of a field but fallback to a default if the value isn't present.
     * @param $item
     * @param $field
     * @param $default
     * @return mixed
     * @deprecated use defaultvalue attribute
     */
    public function getFieldDefault($profile, $item, $field, $default)
    {
        $value = $this->getFieldValue($item, $field, $profile);
        return $value ? $value : $default;
    }

    /**
     * @param $profile
     * @param $item
     * @return mixed
     */
    public function getFieldFallback($profile, $item)
    {
        $fields = func_get_args();
        array_shift($fields);

        $values = $this->getFieldMultiple($profile, $item, $fields);
        foreach ($values as $value) {
            if ($value) {
                return $value;
            }
        }
    }

    /**
     * @param array $item
     * @param array $field
     * @param string $split
     * @return array
     */
    public function getFieldSplit($profile, $item, $field, $split = ' ')
    {
        switch ($split) {
            case '\n': //itemfeed (LF or 0x0A (10) in ASCII)
                $split = "\n";
                break;
            case '\r': //carriage return (CR or 0x0D (13) in ASCII)
                $split = "\r";
                break;
            case '\t': //horizontal tab (HT or 0x09 (9) in ASCII)
                $split = "\t";
                break;
            case '\v': //vertical tab (VT or 0x0B (11) in ASCII) (since PHP 5.2.5)
                $split = "\v";
                break;
            case '\e': //escape (ESC or 0x1B (27) in ASCII) (since PHP 5.4.0)
                $split = "\e";
                break;
            case '\f': //form feed (FF or 0x0C (12) in ASCII) (since PHP 5.2.5)
                $split = "\f";
                break;
        }

        $result = $this->getFieldValue($item, $field, $profile);
        if (!$result) {
            return null;
        }

        return explode($split, $result);
    }

    /**
     * Checks if a field has a value
     * @param $item
     * @param $field
     * @return string
     */
    public function getFieldBoolean($profile, $item, $field)
    {
        $value = $this->getFieldValue($item, $field, $profile);
        return $value ? '1' : '0';
    }

    /**
     * Get multiple rows in one field.
     * @param      $item
     * @param      $fields
     * @param bool $withKeys
     * @return array
     */
    public function getFieldMultiple($profile, $item, $fields, $withKeys = false)
    {
        $parts = [];
        if (!is_array($fields)) {
            $fields = [$fields];
        }
        if (isset($fields[self::ATTRIBUTES_KEY])) {
            $parts = $this->getFieldValue($item, $fields, $profile);
        } else {
            foreach ($fields as $fieldConfig) {
                $values = $this->getFieldValue($item, $fieldConfig, $profile);
                if (!is_array($values)) {
                    $values = [$values];
                }

                foreach ($values as $value) {
                    if ($withKeys && isset($fieldName)) {
                        $parts[$fieldName] = $value;
                    } else {
                        $parts[] = $value;
                    }
                }
            }
        }

        return $parts;
    }

    /**
     * @param          $item
     * @param          $field
     * @param          $limit
     * @param int|null $offset
     * @return array
     */
    public function getFieldLimit($profile, $item, $field, $limit = null, $offset = null)
    {
        $values = $this->getFieldValue($item, $field, $profile);
        $limit = $this->getFieldValue($item, $limit, $profile) ?: null;
        $offset = $this->getFieldValue($item, $offset, $profile) ?: 0;

        if (!is_array($values)) {
            $values = [$values];
        }

        return array_slice($values, $offset, $limit);
    }

    /**
     * Map fields
     * @param array|string $item
     * @param string $field
     * @param array $mapping
     * @return string
     */
    public function getFieldMap($profile, $item, $field, $mapping)
    {
        $values = $this->getFieldValue($item, $field, $profile);
        if (!is_array($values)) {
            $values = [$values];
        }

        foreach ($values as $key => $value) {
            foreach ($mapping as $map) {
                $mapAttributes = $this->getAttributes($map);
                $from = html_entity_decode($mapAttributes['from']);
                if ($from == $value) {
                    $values[$key] = html_entity_decode($mapAttributes['to']);
                }
            }
        }
        if (count($values) == 1) {
            return current($values);
        }
        return $values;
    }

    protected function getAttributes($item)
    {
        return isset($item[self::ATTRIBUTES_KEY]) ? $item[self::ATTRIBUTES_KEY] : [];
    }

    /** @var \CopeX\Import\Model\Template\Filter */
    protected $_filter;

    /**
     * @param $item
     * @param $template
     * @return string
     * @throws \Exception
     */
    public function templateEngine($profile, $item, $template)
    {
        if ($this->_filter === null) {
            $this->filter = $templateFilterFactory->create();
            $this->_filter->setMapper($this->getMapper());
            $this->_filter->setVariables(['helper' => $this]);
        }

        $template = trim((string)$template, "\n ");
        $this->_filter->setitem($item);
        $result = $this->_filter->filter($template);
        return $result;
    }

    /**
     * Count the values of a field
     * @param $profile
     * @param $item
     * @param $countConfig
     * @return int
     */
    public function getFieldCount($profile, $item, $countConfig)
    {
        return count($this->getFieldValue($item, $countConfig, $profile));
    }

    /**
     * Count another field an give it or a list or a value.
     * @param      $item
     * @param      $countConfig
     * @param null $valueConfig
     * @return array|null
     * @internal param $countField
     */
    public function getFieldCounter($profile, $item, $countConfig, $valueConfig = null)
    {
        $count = $this->getFieldCount($profile, $item, $countConfig);
        $value = $this->getFieldValue($item, $valueConfig, $profile);
        $values = [];
        for ($i = 0; $i < $count; $i++) {
            $values[] = is_null($value) ? $i + 1 : (is_array($value) ? $value[$i] : $value);
        }
        return $values;
    }

    public function ifFieldsValue($profile, $item, $fields, $valueField)
    {
        $values = $this->getFieldMultiple($profile, $item, $fields);
        $valid = true;
        foreach ($values as $value) {
            if (is_array($value)) {
                foreach ($value as $valueItem) {
                    if (empty($valueItem)) {
                        $valid = false;
                        break 2;
                    }
                }
            } else {
                if (empty($value)) {
                    $valid = false;
                    break;
                }
            }
        }

        if ($valid) {
            return $this->getFieldValue($item, $fields, $profile);
        }
        return null;
    }

    public function getIfFieldValue($profile, $item, $fields, $then, $default = null)
    {
        $isValid = true;
        foreach ($fields as $field) {
            $attributeCondition = $this->getAttributes($field);
            $operator = "eq";
            if (isset($attributeCondition['operator'])) {
                $operator = $attributeCondition['operator'];
            }
            next($field);
            $fieldValue = $this->getFieldValue($item, current($field), $profile);
            $compareValue = $attributeCondition['value'];
            switch ($operator) {
                case "lt":
                    $isValid = $fieldValue < $compareValue;
                    break;
                case "gt":
                    $isValid = $fieldValue > $compareValue;
                    break;
                case "ne":
                    $isValid = $fieldValue != $compareValue;
                    break;
                case "ge":
                    $isValid = $fieldValue >= $compareValue;
                    break;
                case "le":
                    $isValid = $fieldValue <= $compareValue;
                    break;
                case "eq":
                default:
                    $isValid = $fieldValue == $compareValue;
                    break;
            }
            if (!$isValid) {
                break;
            }
        }
        return $isValid ? $this->getFieldValue($item, $then, $profile)
            : $this->getFieldValue($item, $default, $profile);
    }

    /**
     * @param $profile
     * @param $item
     * @return mixed
     * @deprecated
     */
    public function getMediaAttributeId($profile, $item)
    {
        return $this->getAttributeId($profile, $item, 'media_gallery');
    }

    protected $_attributeMapping = [];

    public function getAttributeId($profile, $item, $attribute, $type = 'catalog_product')
    {
        $attributeCode = is_string($attribute)
            ? $attribute : $this->getFieldValue($item, $attribute, $profile);

        if (!isset($this->_attributeMapping[$attributeCode])) {
            $this->_attributeMapping[$attributeCode] =
                $this->eavAttribute->getIdByCode($type, $attributeCode);
        }
        return $this->_attributeMapping[$attributeCode];
    }

    /**
     * Tries to strip image file names to import
     * @param $profile
     * @param $item
     * @param $field
     * @return mixed|string
     */
    public function getImageName($profile, $item, $field)
    {
        $fieldValue = trim((string)$this->getFieldValue($item, $field, $profile));
        if (!file_exists($this->mediaImportPath . $fieldValue)) {
            $fieldValue = "";
        }
        return $fieldValue;
    }

    public function getMediaImage($profile, $item, $image, $limit = null, $filename = null, $ext = null)
    {
        $images = (array)$this->getFieldValue($item, $image, $profile);
        $images = array_filter($images);
        $filenameBase = $this->getFieldValue($item, $filename, $profile);
        $ext = $this->getFieldValue($item, $ext, $profile);

        if ($limit) {
            $images = array_slice($images, 0, $limit);
        }
        foreach ($images as $key => $image) {
            $image = str_replace(' ', '%20', $image);
            if ($ext == null) {
                $ext = pathinfo($image, PATHINFO_EXTENSION);
            }
            if ($filenameBase !== null) {
                if (count($images) > 1) {
                    $filename = $filenameBase . '-' . ($key + 1) . '.' . $ext;
                } else {
                    $filename = $filenameBase . '.' . $ext;
                }
            } else {
                $filename = basename($image);
            }

            if (!is_file($this->_getUploader()->getTmpDir() . DIRECTORY_SEPARATOR . $filename)) {
                $this->_copyExternalImageFile($image, $filename, $ext);
            }

            if ($filename !== $images[$key]) {
                $images[$key] = '/' . $filename;
            }
        }

        return array_values($images);
    }

    public function getCurrentDate($profile, $item, $format = "")
    {
        if (!$format) {
            $format = 'd-m-Y';
        } else {
            $format = $this->getFieldValue($item, $format, $profile);
        }
        if ($this->_today === null) {
            $currentTimestamp = time();
        }

        return date($format, $currentTimestamp);
    }

    public function timestampToDate($profile, $item, $field, $timezoneFrom = null, $offset = null)
    {
        $values = $this->getFieldValue($item, $field, $profile);
        if (!is_array($values)) {
            $values = [$values];
        }

        if ($timezoneFrom) {
            $timezoneFrom = new DateTimeZone($timezoneFrom);
        }

        foreach ($values as $key => $value) {
            $value = $value ? '@' . $value : 'now';
            $datetime = new DateTime($value, $timezoneFrom);
            $datetime->setTimezone(new DateTimeZone(date_default_timezone_get()));

            if ($offset) {
                $dateInterval = DateInterval::createFromDateString($offset);
                $datetime->add($dateInterval);
            }

            $values[$key] = $datetime->format('c');
        }

        return $values;
    }

    /**
     * Download given file to ImportExport Tmp Dir (usually media/import)
     * Files can be downloaded through FTP by passing the FTP credentials in the url:
     * ********************************
     * @param string $url
     */
    protected $_fileCache = [];

    protected function _copyExternalImageFile($url, $filename = null)
    {
        if (isset($this->_fileCache[$url])) {
            return;
        }

        try {
            $parsedUrl = parse_url($url);
            $this->_fileCache[$url] = true;
            $dir = $this->_getUploader()->getTmpDir();
            if (!is_dir($dir)) {
                mkdir($dir);
            }

            $fileName = $dir . DIRECTORY_SEPARATOR . (!is_null($filename) ? $filename : basename($url));
            $fileHandle = fopen($fileName, 'w+');
            $ch = curl_init();
            /* todo reuse curl handle to increase performance
             * http://stackoverflow.com/questions/3787002/reusing-the-same-curl-handle-big-performance-increase
             * $this->_curlHandles = []
             * $this->_curlHandles[{url_without_file}] = {curlHandle}
             * if (isset($this->_curlHandles[{url_without_file}]) use curl handle
             * else create new curl handle
             */

            $statusOk = -1;
            if (!isset($parsedUrl['path'])) {
                throw new \Exception('No Path detected for URL');
            }

            if (!isset($parsedUrl['scheme'])) {
                throw new \Exception('No URL scheme detected ftp://, http://, etc.');
            }

            switch ($parsedUrl['scheme']) {
                case 'ftp':
                    $statusOk = 226;
                    if (!isset($parsedUrl['user'], $parsedUrl['pass'])) {
                        $this->logHelper->log(__(
                            'Invalid URL scheme detected, please enter FTP credentials'), Logger::ERROR);
                    }
                    $url = $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . $parsedUrl['path'];
                    curl_setopt($ch, CURLOPT_USERPWD, $parsedUrl['user'] . ':' . $parsedUrl['pass']);
                    curl_setopt($ch, CURLOPT_URL, $url);
                    break;
                case 'http':
                case 'https':
                    $statusOk = 200;
                    curl_setopt($ch, CURLOPT_URL, $url);
                    break;
                default:
                    $this->logHelper->log(__(
                        __('Invalid URL scheme detected')), Logger::ERROR);
            }

            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            curl_setopt($ch, CURLOPT_FILE, $fileHandle);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_exec($ch);

            $code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            fclose($fileHandle);

            if ($code !== $statusOk) {
                $this->_fileCache[$url] = $code;
                $this->logHelper->log(__(
                    "Returned status code %s while downloading image %s", $code, $url), Logger::ERROR);
                unlink($fileName);
            }
        } catch (\Exception $e) {
            throw new \Exception('Download of file ' . $url . ' failed: ' . $e->getMessage());
        }
    }

    /**
     * Returns an object for upload a media files
     */
    protected function _getUploader()
    {
        if ($this->_uploader->getTmpDir() === "") {
            $tmpDir = $this->getMediaDir() . '/import';
            $destDir = $this->getMediaDir() . '/catalog/product';

            if (!is_writable($destDir)) {
                @mkdir($destDir, 0777, true);
            }
            if (!is_writable($tmpDir)) {
                @mkdir($tmpDir, 0777, true);
            }

            if (!$this->_uploader->setTmpDir($tmpDir)) {
                throw new \Exception("File directory '{$tmpDir}' is not readable.");
            }
            if (!$this->_uploader->setDestDir($destDir)) {
                throw new \Exception("File directory '{$destDir}' is not writable.");
            }
        }
        return $this->_uploader;
    }

    protected function getMediaDir()
    {
        return $this->directoryList->getPath('media');
    }

    /**
     * @param       $item
     * @param array $key Key to search
     * @param array $sourceModel Source model to search key in
     * @return bool|string
     */
    public function getOptionValue($profile, $item, $key, $sourceModel)
    {
        $key = $this->getFieldValue($item, $key, $profile);
        $sourceModel = $this->getFieldValue($item, $sourceModel, $profile);

        /** @var \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource $sourceModel */
        $sourceModel = $this->classFactory->getModel($sourceModel);

        return $sourceModel->getOptionText($key);
    }
}
