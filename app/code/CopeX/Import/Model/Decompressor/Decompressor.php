<?php
/**
 * CopeX_Import
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category    CopeX
 * @package     CopeX_Import
 * @copyright   Copyright © 2014 CopeX (http://copex.io/)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 * @ *
 *
 */

namespace CopeX\Import\Model\Decompressor;

use CopeX\Import\Model\PreProcessorInterface;
use Monolog\Logger;

abstract class Decompressor implements PreProcessorInterface
{

    protected $logHelper;
    protected $directoryList;

    public function __construct( \Magento\Framework\App\Filesystem\DirectoryList $directoryList, \CopeX\Import\Helper\Log $logger)
    {
        $this->logHelper = $logger;
        $this->directoryList = $directoryList;
    }

    /**
     * Extract a file
     * @param \Magento\Framework\DataObject $object
     * @return mixed
     */
    abstract public function decompress(\Magento\Framework\DataObject $object);


    public function process(\Magento\Framework\DataObject $dataObject)
    {
        $this->decompress($dataObject);
    }

    protected function _getFilePath($folder, $filename)
    {
        return $this->directoryList->getRoot() . DIRECTORY_SEPARATOR . trim($folder, '/') . DIRECTORY_SEPARATOR . $filename;
    }


    /**
     * @param     $message
     * @param int $level
     *
     * @return $this
     */
    protected function log($message, $level = Logger::INFO)
    {
        $this->getLog()->log($message, $level);
        return $this;
    }


    /**
     * @return \CopeX\Import\Helper\Log
     */
    protected function getLog()
    {
        return $this->logHelper;
    }
}
