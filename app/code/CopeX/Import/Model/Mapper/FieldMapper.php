<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 29.05.17
 * Time: 11:11
 */

namespace CopeX\Import\Model\Mapper;


class FieldMapper extends AbstractMapper
{

    const DELIMITER = "/";

    /**
     * @param $item
     * @param $itemConfig
     * @param $attributeValue
     * @param $profileName
     * @return mixed
     */
    public function getValue($item, $itemConfig, $attributeValue, $profileName)
    {
        if (strpos($attributeValue, self::DELIMITER)) {
            $fieldParts = explode(self::DELIMITER, $attributeValue);
            $value = $this->getValueRecursive($item, $fieldParts);
        } else {
            $value = isset($item[$attributeValue]) ? $item[$attributeValue] : "";
        }
        return $value;
    }

    protected function getValueRecursive($inputArray, $fieldParts, $index = 0)
    {
        if (isset($fieldParts[$index]) && $fieldParts[$index] == "" && is_array($inputArray)){ //Want array
            $values = [];
            foreach ($inputArray as $nextValue) {
                $values [] = $this->getValueRecursive($nextValue, $fieldParts, $index +1);
            }
            return $values;
        }

        if (! isset($inputArray[$fieldParts[$index]])) {
            return "";
        }
        $currentInputArray = $inputArray[$fieldParts[$index]];
        if (count($fieldParts) == $index + 1) {//Last Item
            return $currentInputArray;
        };

        return $this->getValueRecursive($currentInputArray, $fieldParts, $index +1);
    }
}