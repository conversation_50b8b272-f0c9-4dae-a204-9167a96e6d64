<?php

namespace CopeX\Import\Rewrite\FireGento\FastSimpleImport\Model;

class Importer extends \FireGento\FastSimpleImport\Model\Importer
{
    protected \Magento\ImportExport\Model\Import $importModel;
    protected \Magento\Framework\Event\Manager $eventManager;
    protected \CopeX\Import\Helper\Log $log;
    protected \FireGento\FastSimpleImport\Service\ImportErrorService $importErrorService;

    public function __construct(
        \Magento\ImportExport\Model\ImportFactory $importModelFactory,
        \FireGento\FastSimpleImport\Service\ImportErrorService $importErrorService,
        \FireGento\FastSimpleImport\Model\Adapters\ImportAdapterFactoryInterface $importAdapterFactory,
        \FireGento\FastSimpleImport\Model\Config $config,
        \Magento\Framework\Event\Manager $eventManger,
        \CopeX\Import\Helper\Log $log
    ) {
        parent::__construct($importModelFactory, $importErrorService, $importAdapterFactory, $config);
        $this->eventManager = $eventManger;
        $this->log = $log;
    }

    public function processImport($dataArray): void
    {
        $this->importModel = $this->createImportModel();
        parent::processImport($dataArray);
    }

    protected function _validateData($dataArray)
    {
        $source = $this->importAdapterFactory->create(
            array(
                'data' => $dataArray,
                'multipleValueSeparator' => $this->getMultipleValueSeparator()
            )
        );
        $this->validationResult = $this->importModel->validateSource($source);
        $this->addToLogTrace($this->importModel);
        $this->log->log($this->getLogTrace());
        return $this->validationResult;
    }

    protected function _importData()
    {
        $this->eventManager->dispatch( "copex_import_run_import_after_validation", ['importer' => $this]);
        $this->importModel->importSource();
        $this->_handleImportResult($this->importModel);
    }

    /**
     * @return \Magento\ImportExport\Model\Import
     */
    public function getImportModel(): \Magento\ImportExport\Model\Import
    {
        return $this->importModel;
    }
}
