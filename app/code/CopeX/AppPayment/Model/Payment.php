<?php
declare(strict_types=1);

namespace CopeX\AppPayment\Model;

use CopeX\SerialNumber\Api\SerialNumberRepositoryInterface;
use Dibs\EasyCheckout\Model\CheckoutContext;
use Dibs\EasyCheckout\Model\Client\DTO\Payment\CreatePaymentCheckout;
use Magento\Framework\DataObject;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Address;
use Magento\Quote\Model\Quote\Item;
use Magento\Sales\Model\Order;
use Magento\Store\Model\StoreManagerInterface;
use CopeX\AppPayment\Api\PaymentInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Magento\Framework\Webapi\Rest\Request;
use Magento\Authorization\Model\UserContextInterface;
use Vivo\SerialNumber\Model\SerialNumberManagement;

class Payment implements PaymentInterface
{

    private \Dibs\EasyCheckout\Model\Dibs\Order $order;
    private CartRepositoryInterface $cartRepository;
    private CheckoutContext $context;
    private \Dibs\EasyCheckout\Helper\Data $nexiEaysyCheckoutHelper;
    /**
     * @var \Magento\Quote\Api\CartManagementInterface
     */
    protected $quoteManagement;
    private \Magento\Checkout\Model\PaymentInformationManagement $paymentInformationManagement;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $_logger;
    private \Psr\Log\LoggerInterface $logger;
    private \Magento\Framework\Event\ManagerInterface $eventManager;
    private \Magento\Framework\Event\ManagerInterface $_eventManager;
    private OrderRepositoryInterface $orderRepository;
    private SearchCriteriaBuilder $searchCriteriaBuilder;
    private SortOrderBuilder $sortOrderBuilder;
    private UserContextInterface $userContext;
    private SerialNumberRepositoryInterface $serialNumberRepository;

    public function __construct(
        \Dibs\EasyCheckout\Model\Dibs\Order $order,
        CartRepositoryInterface $cartRepository,
        CheckoutContext $context,
        \Dibs\EasyCheckout\Helper\Data $nexiEaysyCheckoutHelper,
        \Magento\Quote\Api\CartManagementInterface $quoteManagement,
        \Magento\Checkout\Model\PaymentInformationManagement $paymentInformationManagement,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Framework\Event\ManagerInterface $eventManager,
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        SortOrderBuilder $sortOrderBuilder,
        UserContextInterface $userContext,
        SerialNumberRepositoryInterface $serialNumberRepository,
    ) {
        $this->order = $order;
        $this->cartRepository = $cartRepository;
        $this->context = $context;
        $this->nexiEaysyCheckoutHelper = $nexiEaysyCheckoutHelper;
        $this->quoteManagement = $quoteManagement;
        $this->paymentInformationManagement = $paymentInformationManagement;
        $this->_logger = $logger;
        $this->_eventManager = $eventManager;
        $this->orderRepository = $orderRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->userContext = $userContext;
        $this->serialNumberRepository = $serialNumberRepository;
    }

    /**
     * @throws NoSuchEntityException
     * @throws LocalizedException
     */
    public function initPayment(
        $cartId,
        \Magento\Quote\Api\Data\PaymentInterface $paymentMethod,
        \Magento\Quote\Api\Data\AddressInterface $billingAddress = null
    ): array {
        $paymentMutex = $this->context->getPaymentMutex();
        $quote = $this->cartRepository->get($cartId);
//        $this->order->assignQuote($quote, true);
        $this->paymentInformationManagement->savePaymentInformation($cartId, $paymentMethod, $billingAddress);

        // this will create an api call to dibs and initiaze a new payment
        $integrationType = CreatePaymentCheckout::INTEGRATION_TYPE_HOSTED;
        $payment = $this->order->initNewDibsCheckoutPaymentByQuote($quote, ['integrationType' => $integrationType]);
        $quote->getPayment()->setMethod('dibseasycheckout');
        $quote->setData('dibs_payment_id', $payment->getPaymentId());
        $quote->setData('hash_signature', $this->nexiEaysyCheckoutHelper->generateHashSignatureByQuote($quote));
        $paymentData = (new DataObject())->setDibsPaymentId($payment->getPaymentId())->setCountryId($billingAddress->getCountryId())
            ->setDibsPaymentMethod('dibseasycheckout');
        $quote->getPayment()->getMethodInstance()->assignData($paymentData);

        // Now we create the order from the quote
        try {
            // Lock payment id to prevent double order creation
            $this->cartRepository->save($quote);
            $this->_eventManager->dispatch('checkout_submit_before', ['quote' => $quote]);
            $paymentMutex->lock($payment->getPaymentId());
            $this->_logger->info("creating order.");
            $orderId = $this->quoteManagement->placeOrder($cartId);
            $this->_logger->info("order created." . $orderId);
            //$order->setStatus('canceled');
        } catch (\Exception $e) {
            $this->_logger->info("error in creating order." . $e->getMessage());
            $paymentMutex->release($payment->getPaymentId());
            $this->_logger->error($e);
            throw $e;
        }
        return [
            [
                'orderId'    => $orderId,
                'paymentId'  => $payment->getPaymentId(),
                'paymentUrl' => $payment->getCheckoutUrl(),
            ],
        ];
    }

    /**
     * Get customer orders
     *
     * @return array
     * @throws LocalizedException
     */
    public function getCustomerOrders(): array
    {
        // Get current customer ID from user context
        $customerId = $this->userContext->getUserId();

        if (!$customerId || $this->userContext->getUserType() !== UserContextInterface::USER_TYPE_CUSTOMER) {
            //throw new LocalizedException(__('Customer authentication required'));
        }

        // Build sort order for most recent orders first
        $sortOrder = $this->sortOrderBuilder
            ->setField('created_at')
            ->setDirection('DESC')
            ->create();

        // Build search criteria to get orders for the current customer
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('customer_id', 2)
            ->addSortOrder($sortOrder)
            ->create();

        // Get orders from repository
        $orderSearchResult = $this->orderRepository->getList($searchCriteria);
        $orders = $orderSearchResult->getItems();

        // Format orders for API response
        $formattedOrders = [];
        foreach ($orders as $order) {
            $orderItems = $this->formatOrderItems($order->getItems());
            $totalSavings = $this->calculateOrderSavings($orderItems);

            $formattedOrders[] = [
                'order_id' => $order->getEntityId(),
                'increment_id' => $order->getIncrementId(),
                'status' => $order->getStatus(),
                'state' => $order->getState(),
                'created_at' => $order->getCreatedAt(),
                'updated_at' => $order->getUpdatedAt(),
                'grand_total' => $order->getGrandTotal(),
                'currency_code' => $order->getOrderCurrencyCode(),
                'total_qty_ordered' => $order->getTotalQtyOrdered(),
                'customer_email' => $order->getCustomerEmail(),
                'customer_firstname' => $order->getCustomerFirstname(),
                'customer_lastname' => $order->getCustomerLastname(),
                'total_savings' => $totalSavings,
                'billing_address' => $this->formatAddress($order->getBillingAddress()),
                'shipping_address' => $this->formatAddress($order->getShippingAddress()),
                'items' => $orderItems
            ];
        }

        return [
            'orders' => $formattedOrders,
            'total_count' => $orderSearchResult->getTotalCount()
        ];
    }

    /**
     * Format address for API response
     *
     * @param \Magento\Sales\Api\Data\OrderAddressInterface|null $address
     * @return array|null
     */
    private function formatAddress($address): ?array
    {
        if (!$address) {
            return null;
        }

        return [
            'firstname' => $address->getFirstname(),
            'lastname' => $address->getLastname(),
            'company' => $address->getCompany(),
            'street' => $address->getStreet(),
            'city' => $address->getCity(),
            'region' => $address->getRegion(),
            'postcode' => $address->getPostcode(),
            'country_id' => $address->getCountryId(),
            'telephone' => $address->getTelephone()
        ];
    }

    /**
     * Format order items for API response
     *
     * @param array $items
     * @return array
     */
    private function formatOrderItems(array $items): array
    {
        $formattedItems = [];
        foreach ($items as $item) {
            if ($item->getParentItem()) {
                continue; // Skip child items of configurable products
            }

            $price = (float) $item->getPrice();
            $specialPrice = $item->getSpecialPrice() ? (float) $item->getSpecialPrice() : null;
            $qtyOrdered = (float) $item->getQtyOrdered();

            // Calculate savings for this item
            $itemSavings = 0;
            if ($specialPrice && $specialPrice < $price) {
                $itemSavings = ($price - $specialPrice) * $qtyOrdered;
            }

            $formattedItems[] = [
                'created_at' => $item->getCreatedAt(),
                'item_id' => $item->getItemId(),
                'name' => $item->getName(),
                'sku' => $item->getSku(),
                'qty_ordered' => $qtyOrdered,
                'price' => $price,
                'special_price' => $specialPrice,
                'item_savings' => $itemSavings,
                'row_total' => $item->getRowTotal(),
                'product_type' => $item->getProductType(),
                'voucher_data' =>  $this->getVoucherData($item->getItemId())
            ];
        }

        return $formattedItems;
    }


    private function getVoucherData($salesOrderItemId)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('order_item_id', $salesOrderItemId)
            ->create();
        $items = $this->serialNumberRepository->getList($searchCriteria)->getItems();
        if(count($items) == 1){
            return $items[0]->getData();
        }
        return [];

    }

    /**
     * Calculate total savings for an order based on formatted items
     *
     * @param array $formattedItems
     * @return float
     */
    private function calculateOrderSavings(array $formattedItems): float
    {
        $totalSavings = 0;

        foreach ($formattedItems as $item) {
            if (isset($item['item_savings'])) {
                $totalSavings += (float) $item['item_savings'];
            }
        }

        return round($totalSavings, 2);
    }

}
