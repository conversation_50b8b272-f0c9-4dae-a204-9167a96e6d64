<?php

namespace CopeX\AppPayment\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class SetDibsPaymentId implements ObserverInterface
{
    /**
     * Observer for checkout_submit_all_after
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $event = $observer->getEvent();
        $order = $event->getOrder();
        $quote = $event->getQuote();
        $order->setData('dibs_payment_id', $quote->getData('dibs_payment_id'))->save();
    }
}
