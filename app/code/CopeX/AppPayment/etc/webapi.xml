<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/my-orders/:id" method="GET">
        <service class="Magento\Sales\Api\OrderRepositoryInterface" method="get"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>
    <route url="/V1/my-orders" method="GET">
        <service class="CopeX\AppPayment\Api\PaymentInterface" method="getCustomerOrders"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>
    <route url="/V1/init-payment" method="POST">
        <service class="CopeX\AppPayment\Api\PaymentInterface" method="initPayment"/>
        <resources>
            <resource ref="self"/>
        </resources>
        <data>
            <parameter name="cartId" force="true">%cart_id%</parameter>
        </data>
    </route>
    <route method="GET" url="/V1/my-orders/:orderId/pdf-invoice">
        <service class="Ytec\RestPdfInvoice\Api\PdfInvoiceManagementInterface" method="getByOrderId"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>
</routes>
