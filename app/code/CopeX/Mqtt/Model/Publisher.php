<?php

namespace CopeX\Mqtt\Model;

use Cope<PERSON>\Mqtt\Helper\Config as MqttConfig;
use CopeX\Mqtt\Model\Client\ClientBuilder;

class Publisher
{
    public function __construct(
        private readonly ClientBuilder $builder,
        private readonly MqttConfig $config
    ) {
    }

    public function publishJson(string $topic, array $data, ?int $qos = null, ?bool $retain = null): void
    {
        $this->publish($topic, json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES), $qos, $retain);
    }

    public function publishAndWaitForResponse(
        string $requestTopic,
        string $payload,
        string $responseTopic,
        int $timeoutSeconds = 5
    ): ?string {
        if (! $this->config->isEnabled()) {
            return null;
        }
        $client = $this->builder->build();
        $this->builder->connect($client);
        $responseMessage = null;
        $received = false;
        try {
            $payload = $this->normalizePayload($payload);
            $client->subscribe(
                $responseTopic,
                static function (string $topic, string $message) use (&$responseMessage, &$received, $client): void {
                    $responseMessage = $message;
                    $received = true;
                    $client->interrupt();
                },
                1
            );
            $client->publish($requestTopic, $payload, 1, false);
            $deadline = microtime(true) + $timeoutSeconds;
            while (! $received) {
                $now = microtime(true);
                if ($now >= $deadline) {
                    break;
                }
                $remainingMs = (int) min(500, max(1, ($deadline - $now) * 1000));
                $client->loop(true, $remainingMs);
            }
            if (! $received) {
                $client->interrupt();
            }
        } finally {
            try {
                $client->disconnect();
            } catch (\Throwable $e) {
            }
        }

        return $responseMessage;
    }

    public function publishJsonDirect(
        string $requestTopic,
        array $data,
        string $responseTopic,
        int $timeoutSeconds = 5,
        ?int $qos = null,
        ?bool $retain = null
    ): ?string {
        $payload = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        return $this->publishAndWaitForResponse(
            $requestTopic,
            $payload,
            $responseTopic,
            $timeoutSeconds
        );
    }

    private function normalizePayload(mixed $payload): string
    {
        if (! is_string($payload)) {
            $payload = json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }
        $len = strlen($payload);
        if ($len > 250 * 1024 * 1024) {
            throw new \RuntimeException("MQTT payload too large: {$len} bytes");
        }

        return $payload;
    }
}
