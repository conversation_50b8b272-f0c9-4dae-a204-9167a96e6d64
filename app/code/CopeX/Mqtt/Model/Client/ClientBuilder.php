<?php

namespace CopeX\Mqtt\Model\Client;

use CopeX\Mqtt\Helper\Config as MqttConfig;
use PhpMqtt\Client\Exceptions\ProtocolNotSupportedException;
use PhpMqtt\Client\MqttClient;
use PhpMqtt\Client\ConnectionSettings;

readonly class ClientBuilder
{
    public function __construct(private MqttConfig $config) {}

    public function build()
    {
        $host = $this->config->getHost();
        $port = $this->config->getPort();
        $clientId = $this->config->getClientId();

        $settings = (new ConnectionSettings)
            ->setUsername($this->config->getUsername())
            ->setPassword($this->config->getPassword())
            ->setUseTls($this->config->isTlsEnabled())
            ->setTlsSelfSignedAllowed($this->config->isTlsAllowSelfSigned())
            ->setKeepAliveInterval(30)
            ->setReconnectAutomatically(false)
            ->setLastWillTopic($this->config->getTopicPrefix() . 'lwt/' . $clientId)
            ->setLastWillMessage('offline')
            ->setLastWillQualityOfService($this->config->getQos())
            ->setRetainLastWill(true);

        return $settings;
    }

    public function connect(MqttClient $client): void
    {
        $settings = (new ConnectionSettings)
            ->setUsername($this->config->getUsername())
            ->setPassword($this->config->getPassword())
            ->setUseTls($this->config->isTlsEnabled())
            ->setTlsSelfSignedAllowed($this->config->isTlsAllowSelfSigned())
            ->setKeepAliveInterval(30)
            ->setReconnectAutomatically(true);

        $client->connect($settings, true);
    }
}
