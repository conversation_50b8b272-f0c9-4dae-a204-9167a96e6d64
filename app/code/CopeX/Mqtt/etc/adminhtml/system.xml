<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="copex" translate="label" sortOrder="200">
            <label>CopeX</label>
        </tab>
        <section id="copex_mqtt" translate="label" type="text" sortOrder="777" showInDefault="1" showInWebsite="0" showInStore="0">
            <label>MQTT Connector</label>
            <tab>copex</tab>
            <resource>CopeX_Mqtt::config</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>General</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="client_id" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Client ID</label>
                </field>
                <field id="topic_prefix" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Topic Prefix</label>
                    <comment>e.g. <code>magento/</code> (leave trailing slash if desired)</comment>
                </field>
                <field id="qos" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>QoS</label>
                    <source_model>CopeX\Mqtt\Model\Config\Source\Qos</source_model>
                </field>
                <field id="retain" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Retain</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="topics" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Topics</label>
                <field id="shipping_calculation_request_topic" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Shipping Calculation Request Topic</label>
                    <comment>MQTT topic for shipping cost calculation requests</comment>
                </field>
                <field id="shipping_calculation_response_topic" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Shipping Calculation Response Topic</label>
                    <comment>MQTT topic for shipping cost calculation responses</comment>
                </field>
                <field id="invoices_request_topic" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Invoices Request Topic</label>
                    <comment>MQTT topic for invoices requests</comment>
                </field>
                <field id="invoices_response_topic" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Invoices Response Topic</label>
                    <comment>MQTT topic for invoices responses</comment>
                </field>
                <field id="pdf_invoices_request_topic" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>PDF Invoices Request Topic</label>
                    <comment>MQTT topic for PDF invoices requests</comment>
                </field>
                <field id="pdf_invoices_response_topic" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>PDF Invoices Response Topic</label>
                    <comment>MQTT topic for PDF invoices responses</comment>
                </field>
            </group>
            <group id="connection" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Connection</label>
                <field id="host" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Host</label>
                </field>
                <field id="port" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Port</label>
                    <validate>validate-number</validate>
                </field>
                <field id="username" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Username</label>
                </field>
                <field id="password" translate="label" type="obscure" sortOrder="40" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Password</label>
                </field>
                <field id="tls_enabled" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable TLS</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="tls_allow_self_signed" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>TLS: Allow self-signed</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="test_data" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Test Sample Data</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
