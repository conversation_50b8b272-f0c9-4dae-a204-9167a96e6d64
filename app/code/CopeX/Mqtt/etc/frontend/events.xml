<?xml version="1.0"?>
<!--
/**
 * CopeX MQTT Module - Frontend Events Configuration
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="checkout_cart_save_after">
        <observer name="copex_mqtt_cart_update" instance="CopeX\Mqtt\Observer\CartUpdateObserver" />
    </event>

    <event name="checkout_cart_product_add_after">
        <observer name="copex_mqtt_product_add" instance="CopeX\Mqtt\Observer\CartUpdateObserver" />
    </event>

    <event name="checkout_cart_product_update_after">
        <observer name="copex_mqtt_product_update" instance="CopeX\Mqtt\Observer\CartUpdateObserver" />
    </event>

    <event name="sales_quote_save_after">
        <observer name="copex_mqtt_quote_save" instance="CopeX\Mqtt\Observer\QuoteUpdateObserver" />
    </event>

    <event name="sales_quote_collect_totals_after">
        <observer name="copex_mqtt_quote_totals" instance="CopeX\Mqtt\Observer\QuoteUpdateObserver" />
    </event>

    <event name="layout_load_before">
        <observer name="copex_mqtt_add_cart_shipping_handle" instance="CopeX\Mqtt\Observer\AddCartShippingHandle"/>
    </event>

    <event name="layout_generate_blocks_after">
        <observer name="copex_mqtt_add_customer_invoice_handle" instance="CopeX\Mqtt\Observer\CustomerInvoiceHandle"/>
    </event>
</config>
