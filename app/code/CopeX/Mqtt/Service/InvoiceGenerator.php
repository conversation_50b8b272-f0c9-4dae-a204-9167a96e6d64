<?php
/**
 * CopeX MQTT Module - Invoice Generator Service
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\Mqtt\Service;

use CopeX\Mqtt\Helper\Config as MqttConfig;
use CopeX\Mqtt\Model\Publisher;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\Exception\LocalizedException;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for generating invoices via MQTT
 */
class InvoiceGenerator
{
    /**
     * @var MqttConfig
     */
    private $mqttConfig;

    /**
     * @var Publisher
     */
    private $mqttPublisher;

    /**
     * @var CustomerSession
     */
    private $customerSession;

    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * InvoiceGenerator constructor.
     *
     * @param MqttConfig $mqttConfig
     * @param Publisher $mqttPublisher
     * @param CustomerSession $customerSession
     * @param OrderRepositoryInterface $orderRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        MqttConfig $mqttConfig,
        Publisher $mqttPublisher,
        CustomerSession $customerSession,
        OrderRepositoryInterface $orderRepository,
        LoggerInterface $logger
    ) {
        $this->mqttConfig = $mqttConfig;
        $this->mqttPublisher = $mqttPublisher;
        $this->customerSession = $customerSession;
        $this->orderRepository = $orderRepository;
        $this->logger = $logger;
    }

    /**
     * Generate invoice for order
     *
     * @param int $orderId
     * @return bool
     * @throws LocalizedException
     */
    public function generateInvoice(int $orderId): bool
    {
        try {
            // Validate customer is logged in
            if (!$this->customerSession->isLoggedIn()) {
                throw new LocalizedException(__('Customer must be logged in to generate invoices.'));
            }

            // Get and validate order
            $order = $this->orderRepository->get($orderId);
            
            // Check if order belongs to current customer
            if ($order->getCustomerId() != $this->customerSession->getCustomerId()) {
                throw new LocalizedException(__('You are not authorized to generate invoice for this order.'));
            }

            // Prepare invoice generation data
            $invoiceData = $this->prepareInvoiceData($order);
            
            // Send MQTT request
            $success = $this->sendInvoiceGenerationRequest($invoiceData);
            
            if ($success) {
                $this->logger->info('CopeX MQTT: Invoice generation request sent successfully', [
                    'order_id' => $orderId,
                    'customer_email' => $invoiceData['customer']['email'],
                    'request_id' => $invoiceData['request_id']
                ]);
            }
            
            return $success;
            
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Error generating invoice', [
                'order_id' => $orderId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new LocalizedException(__('Failed to generate invoice: %1', $e->getMessage()));
        }
    }

    /**
     * Generate invoice for current customer
     *
     * @param string $customerEmail
     * @return bool
     * @throws LocalizedException
     */
    public function generateInvoiceForCustomer(string $customerEmail): bool
    {
        try {
            // Validate customer is logged in
            if (!$this->customerSession->isLoggedIn()) {
                throw new LocalizedException(__('Customer must be logged in to generate invoices.'));
            }

            $customer = $this->customerSession->getCustomer();
            
            // Validate email matches current customer
            if ($customer->getEmail() !== $customerEmail) {
                throw new LocalizedException(__('Email does not match current customer.'));
            }

            // Prepare invoice generation data for customer
            $invoiceData = $this->prepareCustomerInvoiceData($customer);
            
            // Send MQTT request
            $success = $this->sendInvoiceGenerationRequest($invoiceData);
            
            if ($success) {
                $this->logger->info('CopeX MQTT: Customer invoice generation request sent successfully', [
                    'customer_id' => $customer->getId(),
                    'customer_email' => $customerEmail,
                    'request_id' => $invoiceData['request_id']
                ]);
            }
            
            return $success;
            
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Error generating customer invoice', [
                'customer_email' => $customerEmail,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            throw new LocalizedException(__('Failed to generate invoice: %1', $e->getMessage()));
        }
    }

    /**
     * Prepare invoice generation data for order
     *
     * @param \Magento\Sales\Model\Order $order
     * @return array
     */
    private function prepareInvoiceData($order): array
    {
        $customer = $this->customerSession->getCustomer();
        $requestId = uniqid('invoice_gen_', true);
        
        return [
            'request_id' => $requestId,
            'timestamp' => time(),
            'order_id' => $order->getId(),
            'order_increment_id' => $order->getIncrementId(),
            'customer' => [
                'customer_id' => $customer->getId(),
                'email' => $customer->getEmail(),
                'name' => $customer->getFirstname() . ' ' . $customer->getLastname()
            ],
            'order_data' => [
                'grand_total' => $order->getGrandTotal(),
                'currency' => $order->getOrderCurrencyCode(),
                'created_at' => $order->getCreatedAt(),
                'status' => $order->getStatus()
            ],
            'requested_at' => date('Y-m-d H:i:s'),
            'source' => 'magento_invoice_generate'
        ];
    }

    /**
     * Prepare invoice generation data for customer
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @return array
     */
    private function prepareCustomerInvoiceData($customer): array
    {
        $requestId = uniqid('customer_invoice_gen_', true);
        
        return [
            'request_id' => $requestId,
            'timestamp' => time(),
            'customer' => [
                'customer_id' => $customer->getId(),
                'email' => $customer->getEmail(),
                'name' => $customer->getFirstname() . ' ' . $customer->getLastname(),
                'created_at' => $customer->getCreatedAt()
            ],
            'requested_at' => date('Y-m-d H:i:s'),
            'source' => 'magento_customer_invoice_generate'
        ];
    }

    /**
     * Send invoice generation request via MQTT
     *
     * @param array $invoiceData
     * @return bool
     */
    private function sendInvoiceGenerationRequest(array $invoiceData): bool
    {
        try {
            // Get the invoice request topic from configuration
            $topic = $this->mqttConfig->getInvoicesRequestTopic();
            
            // Send JSON data directly to the configured MQTT topic
            $this->mqttPublisher->publishJsonDirect(
                $topic,
                $invoiceData,
                1, // QoS level 1 for guaranteed delivery
                false // Don't retain the message
            );
            
            return true;
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Failed to send invoice generation request', [
                'error' => $e->getMessage(),
                'data' => $invoiceData
            ]);
            
            return false;
        }
    }
}
