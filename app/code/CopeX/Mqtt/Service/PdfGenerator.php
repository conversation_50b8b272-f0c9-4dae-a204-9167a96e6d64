<?php
/**
 * CopeX MQTT Module - Invoice Generator Service
 *
 * @category  CopeX
 *
 * @package   CopeX_Mqtt
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\Mqtt\Service;

use CopeX\Mqtt\Helper\Config as MqttConfig;
use CopeX\Mqtt\Model\Publisher;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for generating invoices via MQTT
 */
class PdfGenerator
{
    private MqttConfig $mqttConfig;

    private Publisher $mqttPublisher;

    private CustomerSession $customerSession;

    private OrderRepositoryInterface $orderRepository;

    private LoggerInterface $logger;

    /**
     * InvoiceGenerator constructor.
     */
    public function __construct(
        MqttConfig $mqttConfig,
        Publisher $mqttPublisher,
        CustomerSession $customerSession,
        OrderRepositoryInterface $orderRepository,
        LoggerInterface $logger
    ) {
        $this->mqttConfig = $mqttConfig;
        $this->mqttPublisher = $mqttPublisher;
        $this->customerSession = $customerSession;
        $this->orderRepository = $orderRepository;
        $this->logger = $logger;
    }

    public function generatePdfInvoice(string $document): string
    {
        try {
            $pdfData = $this->preparePdfData($document);
            $mqttResponse = $this->sendPdfRequest($pdfData);
            if (! $mqttResponse) {
                return '';
            }
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Failed to send Pdf generation request', [
                'error' => $e->getMessage(),
                'data' => $pdfData,
            ]);

            return '';
        }

        return $mqttResponse;
    }

    private function sendPdfRequest(array $pdfData): ?string
    {
        return $this->mqttPublisher->publishJsonDirect(
            $this->mqttConfig->getPdfInvoicesRequestTopic(),
            $pdfData,
            $this->mqttConfig->getPdfInvoicesResponseTopic(),
            0,
            false
        );
    }

    private function preparePdfData($document): array
    {
        return [
            'DokumentName' => $document,
        ];
    }
}
