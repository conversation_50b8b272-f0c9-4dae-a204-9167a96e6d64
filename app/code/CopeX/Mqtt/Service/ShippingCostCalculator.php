<?php
/**
 * CopeX MQTT Module - Shipping Cost Calculator Service
 *
 * @category  CopeX
 *
 * @package   CopeX_Mqtt
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\Mqtt\Service;

use CopeX\Mqtt\Helper\Config as MqttConfig;
use CopeX\Mqtt\Model\Publisher;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service for calculating shipping costs via MQTT
 */
class ShippingCostCalculator
{
    private Publisher $mqttPublisher;

    private CheckoutSession $checkoutSession;

    private CustomerSession $customerSession;

    private StoreManagerInterface $storeManager;

    private LoggerInterface $logger;

    private MqttConfig $config;

    public function __construct(
        Publisher $mqttPublisher,
        CheckoutSession $checkoutSession,
        CustomerSession $customerSession,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger,
        MqttConfig $config
    ) {
        $this->mqttPublisher = $mqttPublisher;
        $this->checkoutSession = $checkoutSession;
        $this->customerSession = $customerSession;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
        $this->config = $config;
    }

    /**
     * @param $quote
     */
    public function calculateShippingCost($quote, array $shippingAddress = []): bool|string
    {
        try {
            $calculationData = $this->prepareCalculationData($quote, $shippingAddress);
            $mqttResponse = $this->sendShippingCalculationRequest($calculationData);
            if ($mqttResponse) {
                return $mqttResponse;
            }
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Error calculating shipping cost', [
                'error' => $e->getMessage(),
                'quote_id' => $quote->getId(),
                'trace' => $e->getTraceAsString(),
            ]);

            return false;
        }

        return false;
    }

    /**
     * Calculate shipping cost for current quote
     *
     * @return bool
     */
    public function calculateForCurrentQuote(array $shippingAddress = []): bool|string
    {
        try {
            $quote = $this->checkoutSession->getQuote();
            if (! $quote || ! $quote->getId()) {
                $this->logger->warning('CopeX MQTT: No active quote found for shipping calculation');
                return false;
            }

            return $this->calculateShippingCost($quote, $shippingAddress);
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Error calculating shipping for current quote', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * @param $quote
     *
     * @return array<array>
     */
    private function prepareCalculationData($quote, array $shippingAddress = []): array
    {
        if (empty($shippingAddress) && $quote->getShippingAddress()) {
            $addr = $quote->getShippingAddress();
            $shippingAddress = [
                'country_id' => $addr->getCountryId(),
                'firstname' => $addr->getFirstname(),
                'lastname' => $addr->getLastname(),
                'company' => $addr->getCompany(),
            ];
        }

        $customerName = trim(
            (string) ($quote->getCustomerFirstname() ?: '') . ' ' .
            (string) ($quote->getCustomerLastname() ?: '')
        );

        if ($customerName === '' && ! empty($shippingAddress)) {
            $customerName = trim(
                (string) ($shippingAddress['firstname'] ?? '') . ' ' .
                (string) ($shippingAddress['lastname'] ?? '')
            );
        }

        if ($customerName === '' && ! empty($shippingAddress['company'])) {
            $customerName = (string) $shippingAddress['company'];
        }

        $preisliste = $this->getCustomerPriceList($quote) ?? 'Standard';

        $lieferlandIso2 = $shippingAddress['country_id'] ?? $quote->getStore()->getConfig('general/country/default');

        $positionen = [];
        foreach ($quote->getAllVisibleItems() as $item) {
            $product = $item->getProduct();

            $ean = (string) ($product->getData('ean') ?: $product->getSku());

            $qty = (float) $item->getQty();
            $qty = $qty > 0 ? $qty : 1.0;

            $rowNetto = (float) $item->getRowTotal();
            $rowBrutto = (float) ($item->getRowTotalInclTax() ?: $rowNetto + (float) $item->getTaxAmount());

            $unitNetto = $rowNetto / $qty;
            $unitBrutto = $rowBrutto / $qty;

            $positionen[] = [
                'ean' => $ean,
                'bezeichnung' => (string) $product->getName(),
                'stk' => (string) ($item->getQty() * 1),
                'preis_netto' => $this->formatPrice($unitNetto),
                'preis_brutto' => $this->formatPrice($unitBrutto),
            ];
        }

        return [
            'order' => [
                'quote_id' => (int) $quote->getId(),
                'kunde' => [
                    'name' => $customerName !== '' ? $customerName : 'Gast',
                    'preisliste' => $preisliste,
                    'lieferland' => [
                        'iso2' => (string) $lieferlandIso2,
                    ],
                ],
                'positionen' => $positionen,
            ],
        ];
    }

    private function formatPrice(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * @param $quote
     */
    private function getCustomerPriceList($quote): ?string
    {
        try {
            $groupId = (int) $quote->getCustomerGroupId();
            if ($groupId) {
                $map = [
                    1 => 'Retail',
                    2 => 'B2B',
                ];
                if (isset($map[$groupId])) {
                    return $map[$groupId];
                }
            }
        } catch (\Throwable $e) {
        }
        return null;
    }

    /**
     * Send shipping calculation request via MQTT
     */
    private function sendShippingCalculationRequest(array $calculationData): string
    {
        return $this->mqttPublisher->publishJsonDirect(
            $this->config->getShippingCalculationRequestTopic(),
            $calculationData,
            $this->config->getShippingCalculationResponseTopic(),
            10,
            0,
            false
        );
    }
}
