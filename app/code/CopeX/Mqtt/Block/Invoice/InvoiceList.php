<?php
/**
 * CopeX MQTT Module - Invoice List Block
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\Mqtt\Block\Invoice;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;

/**
 * Block for invoice list page
 */
class InvoiceList extends Template
{
    /**
     * @var CustomerSession
     */
    private $customerSession;

    /**
     * @var OrderCollectionFactory
     */
    private $orderCollectionFactory;

    /**
     * @var array|null
     */
    private $invoiceData = null;

    /**
     * @var bool
     */
    private $hasInvoiceData = false;

    /**
     * InvoiceList constructor.
     *
     * @param Context $context
     * @param CustomerSession $customerSession
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param array $data
     */
    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        OrderCollectionFactory $orderCollectionFactory,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->customerSession = $customerSession;
        $this->orderCollectionFactory = $orderCollectionFactory;
    }

    /**
     * Get customer orders
     *
     * @return \Magento\Sales\Model\ResourceModel\Order\Collection
     */
    public function getCustomerOrders()
    {
        $customerId = $this->customerSession->getCustomerId();

        if (!$customerId) {
            return null;
        }

        $orders = $this->orderCollectionFactory->create()
            ->addFieldToFilter('customer_id', $customerId)
            ->addFieldToFilter('status', ['in' => ['complete', 'processing']])
            ->setOrder('created_at', 'desc')
            ->setPageSize(50);

        return $orders;
    }

    /**
     * Get AJAX URL for invoice generation
     *
     * @return string
     */
    public function getGenerateAjaxUrl(): string
    {
        return $this->getUrl('copex_mqtt/invoice/list');
    }

    /**
     * Format price
     *
     * @param float $price
     * @param string $currency
     * @return string
     */
    public function formatPrice($price, $currency = null): string
    {
        return $this->_storeManager->getStore()->getCurrentCurrency()->format($price);
    }

    /**
     * Check if customer is logged in
     *
     * @return bool
     */
    public function isCustomerLoggedIn(): bool
    {
        return $this->customerSession->isLoggedIn();
    }

    /**
     * Get customer email
     *
     * @return string
     */
    public function getCustomerEmail(): string
    {
        if ($this->customerSession->isLoggedIn()) {
            return $this->customerSession->getCustomer()->getEmail();
        }
        return '';
    }


    /**
     * Get URL for PDF download
     *
     * @return string
     */
    public function getDownloadPdfUrl(): string
    {
        return $this->getUrl('copex_mqtt/invoice/pdf');
    }

    /**
     * Set invoice data from controller
     *
     * @param array $invoiceData
     * @return $this
     */
    public function setInvoiceData(array $invoiceData)
    {
        $this->invoiceData = $invoiceData;
        return $this;
    }

    /**
     * Get invoice data
     *
     * @return array|null
     */
    public function getInvoiceData()
    {
        return $this->invoiceData;
    }

    /**
     * Set has invoice data flag
     *
     * @param bool $hasData
     * @return $this
     */
    public function setHasInvoiceData(bool $hasData)
    {
        $this->hasInvoiceData = $hasData;
        return $this;
    }

    /**
     * Check if invoice data is available
     *
     * @return bool
     */
    public function hasInvoiceData(): bool
    {
        return $this->hasInvoiceData && !empty($this->invoiceData);
    }

    /**
     * Get invoice data as JSON for JavaScript
     *
     * @return string
     */
    public function getInvoiceDataJson(): string
    {
        if ($this->hasInvoiceData()) {
            return json_encode($this->invoiceData, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP);
        }
        return '[]';
    }

    /**
     * Get invoice count
     *
     * @return int
     */
    public function getInvoiceCount(): int
    {
        if ($this->hasInvoiceData()) {
            return count($this->invoiceData);
        }
        return 0;
    }
}
