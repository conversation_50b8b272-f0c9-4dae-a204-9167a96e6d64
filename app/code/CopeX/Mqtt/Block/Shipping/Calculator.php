<?php
/**
 * CopeX MQTT Module - Shipping Calculator Block
 *
 * @category  CopeX
 *
 * @package   CopeX_Mqtt
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\Mqtt\Block\Shipping;

use CopeX\Mqtt\Helper\Config as MqttConfig;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Directory\Model\Config\Source\Country;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

/**
 * Block for shipping cost calculator
 */
class Calculator extends Template
{
    private MqttConfig $mqttConfig;
    private CheckoutSession $checkoutSession;
    private Country $countrySource;

    /**
     * Calculator constructor.
     */
    public function __construct(
        Context $context,
        MqttConfig $mqttConfig,
        CheckoutSession $checkoutSession,
        Country $countrySource,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->mqttConfig = $mqttConfig;
        $this->checkoutSession = $checkoutSession;
        $this->countrySource = $countrySource;
    }

    /**
     * Check if MQTT shipping calculation is enabled
     */
    public function isEnabled(): bool
    {
        return $this->mqttConfig->isEnabled();
    }

    /**
     * Get shipping calculation URL
     */
    public function getCalculationUrl(): string
    {
        return $this->getUrl('copex_mqtt/shipping/calculate');
    }

    /**
     * Get current quote
     */
    public function getQuote(): ?\Magento\Quote\Model\Quote
    {
        try {
            return $this->checkoutSession->getQuote();
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Check if quote has items
     */
    public function hasItems(): bool
    {
        $quote = $this->getQuote();
        return $quote && $quote->getItemsCount() > 0;
    }

    /**
     * Get quote items count
     */
    public function getItemsCount(): int
    {
        $quote = $this->getQuote();
        return $quote ? (int) $quote->getItemsCount() : 0;
    }

    /**
     * Get quote subtotal
     */
    public function getSubtotal(): float
    {
        $quote = $this->getQuote();
        return $quote ? (float) $quote->getSubtotal() : 0.0;
    }

    /**
     * Get formatted subtotal
     */
    public function getFormattedSubtotal(): string
    {
        $quote = $this->getQuote();
        if (! $quote) {
            return '';
        }

        return $quote->getCurrency()->getStoreCurrencyCode() . ' ' . $this->getSubtotal();
    }

    /**
     * Get shipping address data as JSON
     */
    public function getShippingAddressJson(): string
    {
        $quote = $this->getQuote();
        if (! $quote || ! $quote->getShippingAddress()) {
            return json_encode([]);
        }

        $address = $quote->getShippingAddress();
        $addressData = [
            'country_id' => $address->getCountryId(),
            'region' => $address->getRegion(),
            'region_id' => $address->getRegionId(),
            'postcode' => $address->getPostcode(),
            'city' => $address->getCity(),
            'street' => $address->getStreet(),
            'company' => $address->getCompany(),
        ];

        return json_encode($addressData);
    }

    /**
     * Get form key
     */
    public function getFormKey(): string
    {
        return $this->getFormKey();
    }

    /**
     * Check if should show calculator
     */
    public function shouldShow(): bool
    {
        return $this->isEnabled() && $this->hasItems();
    }

    /**
     * Check if running in Hyva mode
     */
    public function isHyvaMode(): bool
    {
        return (bool) $this->getData('hyva_mode') || $this->isHyvaTheme();
    }

    /**
     * Get MQTT configuration for frontend
     */
    public function getMqttConfig(): array
    {
        return [
            'enabled' => $this->mqttConfig->isEnabled(),
            'calculation_url' => $this->getCalculationUrl(),
        ];
    }

    /**
     * Get MQTT configuration as JSON
     */
    public function getMqttConfigJson(): string
    {
        return json_encode($this->getMqttConfig());
    }

    /**
     * Get sorted countries for dropdown
     */
        public function getSortedCountries(): array
        {
            $countries = $this->countrySource->toOptionArray();

            // Remove the first empty option if it exists
            if (isset($countries[0]) && $countries[0]['value'] === '') {
                array_shift($countries);
            }

            // Sort countries by label
            usort($countries, static function ($a, $b) {
                return strcmp($a['label'], $b['label']);
            });

            // Move AT to the top if it exists
            usort($countries, static function ($a, $b) {
                if ($a['value'] === 'AT') {
                    return -1; // AT always first
                }
                if ($b['value'] === 'AT') {
                    return 1; // AT always first
                }
                return strcmp($a['label'], $b['label']); // normal label sort
            });

            return $countries;
        }

    /**
     * Detect if Hyva theme is active
     */
    private function isHyvaTheme(): bool
    {
        try {
            // Check if Hyva modules are loaded
            $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
            $moduleManager = $objectManager->get(\Magento\Framework\Module\Manager::class);

            return $moduleManager->isEnabled('Hyva_Theme') ||
                   $moduleManager->isEnabled('Hyva_GraphqlViewModel');
        } catch (\Exception $e) {
            return false;
        }
    }
}
