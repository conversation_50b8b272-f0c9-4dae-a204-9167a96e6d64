<?php
/**
 * CopeX MQTT Module - Hyva Invoice List Template
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 * @var \CopeX\Mqtt\Block\Invoice\InvoiceList $block
 */
?>

<!-- Hidden form key for AJAX requests -->
<input type="hidden" name="form_key" value="<?= $block->escapeHtmlAttr($block->getFormKey()) ?>" />

<div class="max-w-4xl mx-auto" x-data="hyvaInvoiceGenerator()" x-init="init()">
    <!-- Invoice Grid Section -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-xl font-semibold text-gray-900 flex items-center">
                <svg class="w-6 h-6 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <?= $block->escapeHtml(__('My Invoices')) ?>
            </h2>
        </div>

        <div class="p-6">
            <!-- Invoice Grid -->
            <div x-show="showInvoiceGrid"
                 x-transition:enter="transition ease-out duration-200"
                 x-transition:enter-start="opacity-0 transform translate-y-1"
                 x-transition:enter-end="opacity-100 transform translate-y-0"
                 class="overflow-x-auto">

                <div x-show="sortedInvoices.length > 0">
                    <table class="min-w-full divide-y divide-gray-200 invoice-grid-table">
                        <thead class="bg-gray-50">
                            <tr>
                                <!-- Invoice Number Column (Sortable) -->
                                <th @click="sortBy('Nummer')"
                                    class="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none">
                                    <div class="flex items-center space-x-1">
                                        <span><?= $block->escapeHtml(__('Invoice Number')) ?></span>
                                        <div class="flex flex-col mx-2">
                                            <svg :class="{'text-blue-600': sortField === 'Nummer' && sortDirection === 'asc', 'text-gray-400': !(sortField === 'Nummer' && sortDirection === 'asc')}"
                                                 class="w-3 h-3 -mb-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg :class="{'text-blue-600': sortField === 'Nummer' && sortDirection === 'desc', 'text-gray-400': !(sortField === 'Nummer' && sortDirection === 'desc')}"
                                                 class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </th>

                                <!-- Date Column (Sortable) -->
                                <th @click="sortBy('Datum')"
                                    class="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none">
                                    <div class="flex items-center space-x-1">
                                        <span><?= $block->escapeHtml(__('Date')) ?></span>
                                        <div class="flex flex-col mx-2">
                                            <svg :class="{'text-blue-600': sortField === 'Datum' && sortDirection === 'asc', 'text-gray-400': !(sortField === 'Datum' && sortDirection === 'asc')}"
                                                 class="w-3 h-3 -mb-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg :class="{'text-blue-600': sortField === 'Datum' && sortDirection === 'desc', 'text-gray-400': !(sortField === 'Datum' && sortDirection === 'desc')}"
                                                 class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </th>

                                <!-- Amount Column (Sortable) -->
                                <th @click="sortBy('SummeBrutto')"
                                    class="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none">
                                    <div class="flex items-center space-x-1">
                                        <span><?= $block->escapeHtml(__('Amount')) ?></span>
                                        <div class="flex flex-col mx-2">
                                            <svg :class="{'text-blue-600': sortField === 'SummeBrutto' && sortDirection === 'asc', 'text-gray-400': !(sortField === 'SummeBrutto' && sortDirection === 'asc')}"
                                                 class="w-3 h-3 -mb-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg :class="{'text-blue-600': sortField === 'SummeBrutto' && sortDirection === 'desc', 'text-gray-400': !(sortField === 'SummeBrutto' && sortDirection === 'desc')}"
                                                 class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </th>

                                <!-- Status Column (Sortable) -->
                                <th @click="sortBy('status')"
                                    class="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 select-none">
                                    <div class="flex items-center space-x-1">
                                        <span><?= $block->escapeHtml(__('Status')) ?></span>
                                        <div class="flex flex-col mx-2">
                                            <svg :class="{'text-blue-600': sortField === 'status' && sortDirection === 'asc', 'text-gray-400': !(sortField === 'status' && sortDirection === 'asc')}"
                                                 class="w-3 h-3 -mb-1" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                            <svg :class="{'text-blue-600': sortField === 'status' && sortDirection === 'desc', 'text-gray-400': !(sortField === 'status' && sortDirection === 'desc')}"
                                                 class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                            </svg>
                                        </div>
                                    </div>
                                </th>

                                <!-- Action Column (Not Sortable) -->
                                <th class="px-6 py-3 text-left text-xs font-bold text-gray-500 uppercase tracking-wider">
                                    <?= $block->escapeHtml(__('Action')) ?>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <template x-for="invoice in sortedInvoices" :key="invoice.Nummer">
                                <tr class="hover:bg-gray-50 transition-colors duration-150">
                                    <!-- Invoice Number -->
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="invoice.Nummer"></td>

                                    <!-- Date -->
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500" x-text="formatDate(invoice.Datum)"></td>

                                    <!-- Amount -->
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" x-text="formatCurrency(invoice.SummeBrutto)"></td>

                                    <!-- Status -->
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span :class="getStatusClass(invoice)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                                            <span x-text="getStatusText(invoice)"></span>
                                        </span>
                                    </td>

                                    <!-- Action -->
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex space-x-2">
                                            <template x-for="(document, index) in (invoice.Dokumente || [])" :key="index">
                                                <button @click="downloadPdfDocument(document, invoice)"
                                                        :value="document.DokumenteName"
                                                        :disabled="isDownloading"
                                                        class="bg-blue-600 text-white px-3 py-1.5 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200 text-xs font-medium">
                                                    <svg class="w-4 h-4 inline-block mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                                    </svg>
                                                    <?= $block->escapeHtml(__('PDF')) ?>
                                                </button>
                                            </template>
                                        </div>
                                    </td>
                                </tr>
                            </template>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div x-show="showMessage"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         class="fixed inset-0 z-50 overflow-y-auto"
         style="display: none;">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 transition-opacity" @click="hideMessage()">
                <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
            </div>

            <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full sm:mx-0 sm:h-10 sm:w-10"
                             :class="messageType === 'success' ? 'bg-green-100' : 'bg-red-100'">
                            <svg x-show="messageType === 'success'" class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <svg x-show="messageType === 'error'" class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" x-text="messageTitle"></h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500" x-text="messageText"></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button @click="hideMessage()"
                            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                        <?= $block->escapeHtml(__('OK')) ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function hyvaInvoiceGenerator() {
    return {
        // Form data
        customerEmail: '<?= $block->escapeJs($block->getCustomerEmail()) ?>',
        selectedOrderId: '',

        // Loading states
        isGenerating: false,
        isLoadingInvoices: false,
        isDownloading: false,

        // Invoice grid data
        invoices: [],
        showInvoiceGrid: false,

        // Sorting
        sortField: 'Nummer',
        sortDirection: 'desc',

        // Message state
        showMessage: false,
        messageType: 'success',
        messageTitle: '',
        messageText: '',

        // Configuration
        config: {
            generateUrl: '<?= $block->escapeJs($block->getUrl('copex_mqtt/invoice/list')) ?>',
            downloadPdfUrl: '<?= method_exists($block, 'getDownloadPdfUrl') ? $block->escapeJs($block->getDownloadPdfUrl()) : $block->escapeJs($block->getUrl('copex_mqtt/invoice/pdf')) ?>',
            formKey: '<?= $block->escapeJs($block->getFormKey()) ?>'
        },

        // Initialize
        init() {
            this.loadControllerData();
        },

        // Computed property for sorted invoices
        get sortedInvoices() {
            if (!this.invoices || this.invoices.length === 0) {
                return [];
            }

            return [...this.invoices].sort((a, b) => {
                let aValue, bValue;

                switch (this.sortField) {
                    case 'Nummer':
                        aValue = parseInt(a.Nummer) || 0;
                        bValue = parseInt(b.Nummer) || 0;
                        break;
                    case 'Datum':
                        aValue = new Date(a.Datum);
                        bValue = new Date(b.Datum);
                        break;
                    case 'SummeBrutto':
                        aValue = parseFloat(a.SummeBrutto) || 0;
                        bValue = parseFloat(b.SummeBrutto) || 0;
                        break;
                    case 'status':
                        aValue = this.getStatusText(a);
                        bValue = this.getStatusText(b);
                        break;
                    default:
                        aValue = a[this.sortField];
                        bValue = b[this.sortField];
                }

                if (this.sortDirection === 'asc') {
                    return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
                } else {
                    return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
                }
            });
        },

        // Sort by field
        sortBy(field) {
            if (this.sortField === field) {
                // Toggle direction if same field
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                // Set new field and default to ascending
                this.sortField = field;
                this.sortDirection = 'asc';
            }
        },

        // Load data from controller
        loadControllerData() {
            try {
                // Get invoice data from PHP block
                let controllerData = <?= $block->getInvoiceData() ?>;

                if (controllerData && Array.isArray(controllerData) && controllerData.length > 0) {
                    this.invoices = controllerData;
                    this.showInvoiceGrid = true;

                    // Show success message
                    this.showSuccessMessage(
                        'Invoices Loaded',
                        'Successfully loaded ' + controllerData.length + ' invoice(s) from the system.'
                    );
                }
            } catch (error) {
                console.error('CopeX MQTT: Error loading controller data:', error);
            }
        },

        // Download PDF document with specific DokumenteName
        async downloadPdfDocument(document, invoice) {
            if (this.isDownloading) return;

            this.isDownloading = true;

            try {
                // Get fresh form key from the page
                const formKey = this.getFreshFormKey();

                const formData = new FormData();
                formData.append('form_key', formKey);
                formData.append('document_name', document.DokumenteName || '');
                formData.append('invoice_number', invoice.Nummer || '');

                // Send AJAX request to PDF controller
                const response = await fetch(this.config.downloadPdfUrl, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const result = await response.json();
                if (!result.success) {
                    this.showErrorMessage(result.message);
                }
            } catch (error) {
                this.handleError(error, result.message);
            } finally {
                this.isDownloading = false;
            }
        },

        // Handle response
        handleResponse(response, type) {
            if (response.success) {
                this.showSuccessMessage(type + ' Generated', response.message);
            } else {
                this.showErrorMessage(type + ' Error', response.message || 'Failed to generate ' + type.toLowerCase());
            }
        },

        // Handle error
        handleError(error, type) {
            this.showErrorMessage(type + ' Error', 'An unexpected error occurred while generating ' + type.toLowerCase() + '.');
        },

        // Show success message
        showSuccessMessage(title, text) {
            this.messageType = 'success';
            this.messageTitle = title;
            this.messageText = text;
            this.showMessage = true;
        },

        // Show error message
        showErrorMessage(title, text) {
            this.messageType = 'error';
            this.messageTitle = title;
            this.messageText = text;
            this.showMessage = true;
        },

        // Hide message
        hideMessage() {
            this.showMessage = false;
        },

        // Download PDF fallback (for invoices without documents)
        async downloadPdfFallback(invoice) {
            if (this.isDownloading) return;

            this.isDownloading = true;

            try {
                // Get fresh form key from the page
                const formKey = this.getFreshFormKey();

                const formData = new FormData();
                formData.append('form_key', formKey);
                formData.append('invoice_number', invoice.Nummer);
                formData.append('document_name', '');
                formData.append('fallback_request', 'true');

                // Send AJAX request to PDF controller
                const response = await fetch(this.config.downloadPdfUrl, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    this.showSuccessMessage(result.message);
                } else {
                    this.showErrorMessage(result.message);
                }

            } catch (error) {
                this.handleError(error, 'PDF Download');
            } finally {
                this.isDownloading = false;
            }
        },

        // Get fresh form key from the page
        getFreshFormKey() {
            // Try to get form key from a hidden input on the page
            const formKeyInput = document.querySelector('input[name="form_key"]');
            if (formKeyInput && formKeyInput.value) {
                return formKeyInput.value;
            }

            // Try to get form key from meta tag
            const formKeyMeta = document.querySelector('meta[name="form_key"]');
            if (formKeyMeta && formKeyMeta.content) {
                return formKeyMeta.content;
            }

            // Try to get form key from window object
            if (window.FORM_KEY) {
                return window.FORM_KEY;
            }

            // Fallback to config form key
            return this.config.formKey;
        },

        // Format date in German format (DD.MM.YYYY)
        formatDate(dateString) {
            try {
                const date = new Date(dateString);
                const day = String(date.getDate()).padStart(2, '0');
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const year = date.getFullYear();
                return `${day}.${month}.${year}`;
            } catch (error) {
                return dateString;
            }
        },

        // Format currency in EUR format
        formatCurrency(amount) {
            try {
                const numAmount = parseFloat(amount) || 0;
                return `${numAmount.toFixed(2)} €`;
            } catch (error) {
                return `${amount} €`;
            }
        },

        // Get status text
        getStatusText(invoice) {
            if (invoice.Bezahlt) {
                return 'Paid';
            } else if (invoice.Abgeschlossen) {
                return 'Completed';
            } else {
                return 'Open';
            }
        },

        // Get status CSS class
        getStatusClass(invoice) {
            if (invoice.Bezahlt) {
                return 'bg-green-100 text-green-800';
            } else if (invoice.Abgeschlossen) {
                return 'bg-blue-100 text-blue-800';
            } else {
                return 'bg-yellow-100 text-yellow-800';
            }
        }
    };
}
</script>
