<?php
/**
 * CopeX MQTT Module - Shipping Calculator Template (Hyva Compatible)
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 * @var \CopeX\Mqtt\Block\Shipping\Calculator $block
 */

if (!$block->shouldShow()) {
    return;
}
?>

<div id="mqtt-shipping-calculator"
     class="border border-gray-200 rounded-lg p-6 my-6 bg-gray-50 order-1"
     x-data="shippingCalculator()"
     x-init="init()">
    <!-- Calculator Header -->
    <div class="mb-6">
        <h3 class="text-xl font-semibold text-gray-900 mb-2">
            <?= $block->escapeHtml(__('Calculate Shipping Costs')) ?>
        </h3>
    </div>

    <!-- Shipping Form -->
    <form x-ref="form" @submit.prevent="calculateShipping()" class="space-y-4">
        <?= $block->getBlockHtml('formkey') ?>
        <div>
            <label for="country_id" class="block text-sm font-medium text-gray-700 mb-1">
                <?= $block->escapeHtml(__('Country')) ?> <span class="text-red-500">*</span>
            </label>
            <select x-model="formData.country_id"
                    @change="onAddressChange()"
                    id="country_id"
                    name="country_id"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required>
                <option value=""><?= $block->escapeHtml(__('Please select country')) ?></option>
                <?php foreach ($block->getSortedCountries() as $country): ?>
                    <option value="<?= $block->escapeHtmlAttr($country['value']) ?>">
                        <?= $block->escapeHtml($country['label']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="mt-6">
            <button type="submit"
                    :disabled="isCalculating || !isFormValid()"
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200">
                <span x-show="!isCalculating"><?= $block->escapeHtml(__('Calculate Shipping')) ?></span>
                <span x-show="isCalculating" class="flex items-center justify-center">
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <?= $block->escapeHtml(__('Calculating...')) ?>
                </span>
            </button>
        </div>
    </form>

    <!-- Success Result -->
    <div x-show="showResult"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h4 class="text-sm font-medium text-green-800"><?= $block->escapeHtml(__('Shipping Calculation')) ?></h4>
                <div class="mt-1 text-sm text-green-700" x-text="resultMessage"></div>

                <!-- Delivery Cost Message -->
                <div x-show="deliveryCostMessage"
                     class="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div class="flex items-center">
                        <svg class="h-4 w-4 text-blue-500 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                        <span class="text-sm font-medium text-blue-800" x-text="deliveryCostMessage"></span>
                    </div>
                </div>

                <div class="mt-2 text-xs text-green-600" x-show="requestId">
                    <strong><?= $block->escapeHtml(__('Request ID:')) ?></strong> <span x-text="requestId"></span><br>
                    <em><?= $block->escapeHtml(__('You will receive shipping cost information shortly.')) ?></em>
                </div>
            </div>
        </div>
    </div>

    <!-- Error Result -->
    <div x-show="showError"
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0 transform scale-95"
         x-transition:enter-end="opacity-100 transform scale-100"
         class="mt-6 bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <h4 class="text-sm font-medium text-red-800"><?= $block->escapeHtml(__('Error')) ?></h4>
                <div class="mt-1 text-sm text-red-700" x-text="errorMessage"></div>
            </div>
        </div>
    </div>
</div>

<script>
function shippingCalculator() {
    return {
        config: <?= $block->getMqttConfigJson() ?>,
        existingAddress: <?= $block->getShippingAddressJson() ?>,

        formData: {
            country_id: ''
        },

        isCalculating: false,
        showResult: false,
        showError: false,
        resultMessage: '',
        errorMessage: '',
        requestId: '',
        deliveryCostMessage: '',
        debounceTimer: null,

        init() {
            this.populateExistingAddress();
        },
        populateExistingAddress() {
            if (this.existingAddress && Object.keys(this.existingAddress).length > 0) {
                this.formData.country_id = this.existingAddress.country_id || '';
            }
        },

        isFormValid() {
            return this.formData.country_id;
        },

        onAddressChange() {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                if (this.isFormValid()) {
                    this.autoCalculate();
                }
            }, 1000);
        },

        autoCalculate() {
            if (this.isCalculating) return;;
            this.performCalculation(true);
        },

        calculateShipping() {
            if (this.isCalculating || !this.isFormValid()) return;
            this.performCalculation(false);
        },

        async performCalculation(silent = false) {
            this.isCalculating = true;
            if (!silent) this.hideMessages();
            const formEl = this.$refs.form;
            const formData = new FormData(formEl);
            try {
                const response = await fetch(this.config.calculation_url, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });
                const ct = response.headers.get('content-type') || '';
                if (!ct.includes('application/json')) {
                    const text = await response.text();
                    throw new Error('Non-JSON response: ' + text.slice(0, 300));
                }
                const result = await response.json();
                this.handleResponse(result, silent);
            } catch (error) {
                this.handleError(error, silent);
            } finally {
                this.isCalculating = false;
            }
        },

        // Handle successful response
        handleResponse(response, silent) {
            if (response.success) {
                if (!silent) {
                    this.showSuccessResult(response);
                }
                this.$dispatch('shipping-calculation-success', response);
            } else {
                const message = response.message || '<?= $block->escapeJs(__('Failed to calculate shipping costs.')) ?>';
                if (!silent) {
                    this.showErrorResult(message);
                }
                this.$dispatch('shipping-calculation-error', response);
            }
        },

        // Handle error response
        handleError(error, silent) {
            let message = '<?= $block->escapeJs(__('An error occurred while calculating shipping costs. Please try again.')) ?>';
            if (error.name === 'TypeError') {
                message = '<?= $block->escapeJs(__('Network error. Please check your connection and try again.')) ?>';
            }
            if (!silent) {
                this.showErrorResult(message);
            }
            this.$dispatch('shipping-calculation-error', { error: error.message });
        },

        // Show success result
        showSuccessResult(response) {
            this.resultMessage = response.message || '<?= $block->escapeJs(__('Shipping calculation request sent successfully.')) ?>';
            this.deliveryCostMessage = response.delivery_cost_message || '';
            this.showResult = true;
            this.showError = false;

            // Log delivery cost message for debugging
            if (this.deliveryCostMessage) {
                console.log('CopeX MQTT: Delivery cost message received:', this.deliveryCostMessage);
            }
        },

        // Show error result
        showErrorResult(message) {
            this.errorMessage = message;
            this.showError = true;
            this.showResult = false;
        },

        // Hide all messages
        hideMessages() {
            this.showResult = false;
            this.showError = false;
            this.resultMessage = '';
            this.errorMessage = '';
            this.requestId = '';
            this.deliveryCostMessage = '';
        },

        // Reset form
        resetForm() {
            this.formData = {
                country_id: ''
            };
            this.hideMessages();
        },

        // Set address programmatically
        setAddress(address) {
            if (address.country_id) this.formData.country_id = address.country_id;
        }
    };
}
</script>


