<?xml version="1.0"?>
<!--
/**
 * CopeX MQTT Module - Hyva Cart Page Layout
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="cart.summary">
            <block class="CopeX\Mqtt\Block\Shipping\Calculator"
                   name="copex.mqtt.shipping.calculator.hyva"
                   template="CopeX_Mqtt::shipping/calculator.phtml"
                   after="-">
                <arguments>
                    <argument name="hyva_mode" xsi:type="boolean">true</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>
