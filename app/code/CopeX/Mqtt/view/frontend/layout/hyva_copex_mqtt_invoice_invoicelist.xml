<?xml version="1.0"?>
<!--
/**
 * CopeX MQTT Module - Hyva Invoice List Page Layout
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Invoice List</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="CopeX\Mqtt\Block\Invoice\InvoiceList" name="copex_mqtt_invoice_list_hyva" template="CopeX_Mqtt::invoice/list_hyva.phtml"/>
        </referenceContainer>
    </body>
</page>
