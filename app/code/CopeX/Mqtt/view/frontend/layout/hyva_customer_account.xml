<?xml version="1.0"?>
<!--
/**
 * CopeX MQTT Module - Customer Account Layout
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Customer\Block\Account\Link"
                   name="customer-account-navigation-invoice-list-link"
                   ifconfig="copex_mqtt/general/enabled">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">My Invoices</argument>
                    <argument name="path" xsi:type="string">copex_mqtt/invoice/invoicelist</argument>
                    <argument name="sortOrder" xsi:type="number">950</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
