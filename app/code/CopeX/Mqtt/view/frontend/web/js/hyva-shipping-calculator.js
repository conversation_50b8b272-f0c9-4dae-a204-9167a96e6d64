/**
 * CopeX MQTT Module - Hyva Shipping Calculator Alpine.js Component
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

/**
 * Alpine.js component for shipping cost calculation
 * Compatible with Hyva theme architecture
 */
function hyvaShippingCalculator() {
    return {
        // Configuration
        config: {},
        existingAddress: {},

        formData: {
            country_id: '',
            postcode: '',
            city: '',
            region: ''
        },

        isCalculating: false,
        showResult: false,
        showError: false,
        resultMessage: '',
        errorMessage: '',
        requestId: '',
        debounceTimer: null,

        /**
         * Initialize the component
         * @param {Object} config - Configuration object
         * @param {Object} existingAddress - Pre-filled address data
         */
        init(config = {}, existingAddress = {}) {
            this.config = {
                enabled: true,
                calculation_url: '/copex_mqtt/shipping/calculate'
            };
            this.existingAddress = existingAddress;
            this.populateExistingAddress();
            this.setupEventListeners();
        },

        /**
         * Setup event listeners for Hyva integration
         */
        setupEventListeners() {
            document.addEventListener('cart-updated', () => {
                if (this.isFormValid()) {
                    this.autoCalculate();
                }
            });

            // Listen for address updates from checkout
            document.addEventListener('checkout-address-updated', (event) => {
                if (event.detail && event.detail.address) {
                    this.updateAddressFromEvent(event.detail.address);
                }
            });
        },

        /**
         * Populate form with existing address data
         */
        populateExistingAddress() {
            if (this.existingAddress && Object.keys(this.existingAddress).length > 0) {
                this.formData.country_id = this.existingAddress.country_id || '';
                this.formData.postcode = this.existingAddress.postcode || '';
                this.formData.city = this.existingAddress.city || '';
                this.formData.region = this.existingAddress.region || '';
            }
        },

        /**
         * Update address from external event
         * @param {Object} address - Address data from event
         */
        updateAddressFromEvent(address) {
            if (address.country_id) this.formData.country_id = address.country_id;
            if (address.postcode) this.formData.postcode = address.postcode;
            if (address.city) this.formData.city = address.city;
            if (address.region) this.formData.region = address.region;
            if (this.isFormValid()) {
                this.autoCalculate();
            }
        },

        /**
         * Check if form has required data
         * @returns {boolean}
         */
        isFormValid() {
            return !!(this.formData.country_id && this.formData.postcode);
        },

        /**
         * Handle address field changes with debouncing
         */
        onAddressChange() {
            clearTimeout(this.debounceTimer);
            this.debounceTimer = setTimeout(() => {
                if (this.isFormValid()) {
                    this.autoCalculate();
                }
            }, 1000); // 1 second debounce
        },

        /**
         * Auto-calculate shipping (silent mode)
         */
        autoCalculate() {
            if (this.isCalculating) return;
            this.performCalculation(true);
        },

        /**
         * Manual calculation triggered by user
         */
        calculateShipping() {
            if (this.isCalculating || !this.isFormValid()) {
                if (!this.isFormValid()) {
                    this.showErrorResult('Please fill in all required fields.');
                }
                return;
            }

            this.performCalculation(false);
        },

        /**
         * Perform the actual AJAX calculation
         * @param {boolean} silent - Whether to show UI feedback
         */
        async performCalculation(silent = false) {
            this.isCalculating = true;
            if (!silent) {
                this.hideMessages();
            }
            try {
                const formData = new FormData();
                formData.append('form_key', this.config.form_key);
                formData.append('country_id', this.formData.country_id);
                formData.append('postcode', this.formData.postcode);
                formData.append('city', this.formData.city);
                formData.append('region', this.formData.region);
                const response = await fetch(this.config.calculation_url, {
                    method: 'POST',
                    body: formData,
                    credentials: 'include',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                });
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const result = await response.json();
                this.handleResponse(result, silent);
            } catch (error) {
                this.handleError(error, silent);
            } finally {
                this.isCalculating = false;
            }
        },

        /**
         * Handle successful API response
         * @param {Object} response - API response
         * @param {boolean} silent - Whether to show UI feedback
         */
        handleResponse(response, silent) {
            if (response.success) {
                if (!silent) {
                    this.showSuccessResult(response);
                }
                // Dispatch Hyva-compatible event
                this.$dispatch('shipping-calculation-success', {
                    detail: response
                });
                window.dispatchEvent(new CustomEvent('copex-mqtt-shipping-success', {
                    detail: response
                }));

            } else {
                const message = response.message || 'Failed to calculate shipping costs.';
                if (!silent) {
                    this.showErrorResult(message);
                }
                this.$dispatch('shipping-calculation-error', {
                    detail: response
                });
                window.dispatchEvent(new CustomEvent('copex-mqtt-shipping-error', {
                    detail: response
                }));
            }
        },

        /**
         * Handle API errors
         * @param {Error} error - Error object
         * @param {boolean} silent - Whether to show UI feedback
         */
        handleError(error, silent) {
            let message = 'An error occurred while calculating shipping costs. Please try again.';
            if (error.name === 'TypeError' || error.message.includes('fetch')) {
                message = 'Network error. Please check your connection and try again.';
            } else if (error.message.includes('HTTP 5')) {
                message = 'Server error. Please try again later.';
            }
            if (!silent) {
                this.showErrorResult(message);
            }
            this.$dispatch('shipping-calculation-error', {
                detail: { error: error.message }
            });
            window.dispatchEvent(new CustomEvent('copex-mqtt-shipping-error', {
                detail: { error: error.message }
            }));
        },

        /**
         * Show success message
         * @param {Object} response - Success response
         */
        showSuccessResult(response) {
            this.resultMessage = response.message || 'Shipping calculation request sent successfully.';
            this.requestId = response.request_id || '';
            this.showResult = true;
            this.showError = false;
        },

        /**
         * Show error message
         * @param {string} message - Error message
         */
        showErrorResult(message) {
            this.errorMessage = message;
            this.showError = true;
            this.showResult = false;
        },

        /**
         * Hide all messages
         */
        hideMessages() {
            this.showResult = false;
            this.showError = false;
            this.resultMessage = '';
            this.errorMessage = '';
            this.requestId = '';
        },

        /**
         * Reset form to initial state
         */
        resetForm() {
            this.formData = {
                country_id: '',
                postcode: '',
                city: '',
                region: ''
            };
            this.hideMessages();
        },

        /**
         * Set address data programmatically
         * @param {Object} address - Address data
         */
        setAddress(address) {
            if (address.country_id) this.formData.country_id = address.country_id;
            if (address.postcode) this.formData.postcode = address.postcode;
            if (address.city) this.formData.city = address.city;
            if (address.region) this.formData.region = address.region;

            // Trigger auto-calculation if form becomes valid
            if (this.isFormValid()) {
                this.autoCalculate();
            }
        },

        /**
         * Get current form data
         * @returns {Object} Current form data
         */
        getFormData() {
            return { ...this.formData };
        },

        /**
         * Check if calculator is currently processing
         * @returns {boolean}
         */
        isProcessing() {
            return this.isCalculating;
        }
    };
}

// Make the component globally available for Hyva
window.hyvaShippingCalculator = hyvaShippingCalculator;

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
    module.exports = hyvaShippingCalculator;
}
