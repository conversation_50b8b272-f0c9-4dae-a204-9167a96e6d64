<?php
/**
 * CopeX MQTT Module - Shipping Cost Calculation Controller
 *
 * @category  CopeX
 *
 * @package   CopeX_Mqtt
 *
 * <AUTHOR> Team
 *
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\Mqtt\Controller\Shipping;

use CopeX\Mqtt\Service\ShippingCostCalculator;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Pricing\Helper\Data as PricingHelper;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Controller for handling shipping cost calculation requests
 */
class Calculate extends Action implements HttpPostActionInterface
{
    private JsonFactory $resultJsonFactory;
    private Validator $formKeyValidator;
    private ShippingCostCalculator $shippingCalculator;
    private LoggerInterface $logger;
    private PricingHelper $pricingHelper;
    private StoreManagerInterface $storeManager;

    /**
     * Calculate constructor.
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        Validator $formKeyValidator,
        ShippingCostCalculator $shippingCalculator,
        LoggerInterface $logger,
        PricingHelper $pricingHelper,
        StoreManagerInterface $storeManager
    ) {
        parent::__construct($context);
        $this->resultJsonFactory = $resultJsonFactory;
        $this->formKeyValidator = $formKeyValidator;
        $this->shippingCalculator = $shippingCalculator;
        $this->logger = $logger;
        $this->pricingHelper = $pricingHelper;
        $this->storeManager = $storeManager;
    }

    public function execute()
    {
        $result = $this->resultJsonFactory->create();

        // Validate request method
        if (! $this->getRequest()->isPost()) {
            return $result->setData([
                'success' => false,
                'message' => __('Invalid request method.'),
            ]);
        }

        // Validate form key
        if (! $this->formKeyValidator->validate($this->getRequest())) {
            return $result->setData([
                'success' => false,
                'message' => __('Invalid form key.'),
            ]);
        }

        try {
            $shippingAddress = $this->getShippingAddressFromRequest();
            $success = $this->shippingCalculator->calculateForCurrentQuote($shippingAddress);

            if ($success) {
                // Parse the JSON response and extract preis_brutto
                $deliveryCostMessage = $this->parseDeliveryCostFromResponse($success);

                return $result->setData([
                    'success' => true,
                    'message' => $deliveryCostMessage,
                    'data' => [
                        'status' => 'processing',
                        'message' => __('Calculating shipping costs...'),
                        'delivery_cost_message' => $deliveryCostMessage,
                    ],
                ]);
            }
            return $result->setData([
                'success' => false,
                'message' => __('Failed to send shipping cost calculation request.'),
            ]);
        } catch (LocalizedException $e) {
            $this->logger->error('CopeX MQTT: Shipping calculation error', [
                'error' => $e->getMessage(),
            ]);

            return $result->setData([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Unexpected error in shipping calculation', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $result->setData([
                'success' => false,
                'message' => __('An unexpected error occurred while calculating shipping costs.'),
            ]);
        }
    }

    /**
     * Extract shipping address data from request
     */
    private function getShippingAddressFromRequest(): array
    {
        $request = $this->getRequest();

        return [
            'country_id' => $request->getParam('country_id', ''),
            'region' => $request->getParam('region', ''),
            'region_id' => $request->getParam('region_id', ''),
            'postcode' => $request->getParam('postcode', ''),
            'city' => $request->getParam('city', ''),
            'street' => $request->getParam('street', []),
            'company' => $request->getParam('company', ''),
        ];
    }

    /**
     * Parse delivery cost from MQTT response and format as currency message
     *
     * @param string $response
     * @return string
     */
    private function parseDeliveryCostFromResponse(string $response): string
    {
        try {
            // Decode the JSON response
            $responseData = json_decode($response, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->logger->warning('CopeX MQTT: Invalid JSON response for delivery cost parsing', [
                    'response' => $response,
                    'json_error' => json_last_error_msg()
                ]);
                return '';
            }

            // Extract preis_brutto value
            $preisBrutto = $this->extractPreisBrutto($responseData);

            if ($preisBrutto !== null && $preisBrutto > 0) {
                // Format the price as currency
                $formattedPrice = $this->formatCurrency($preisBrutto);

                // Create the delivery cost message
                $deliveryMessage = __('Delivery costs: %1', $formattedPrice)->render();

                $this->logger->info('CopeX MQTT: Delivery cost message created', [
                    'preis_brutto' => $preisBrutto,
                    'formatted_price' => $formattedPrice,
                    'message' => $deliveryMessage
                ]);

                return $deliveryMessage;
            } else {
                $this->logger->info('CopeX MQTT: No valid preis_brutto found in response', [
                    'response_data' => $responseData
                ]);
                return '';
            }

        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Error parsing delivery cost from response', [
                'response' => $response,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }

    /**
     * Extract preis_brutto value from response data
     *
     * @param array $responseData
     * @return float|null
     */
    private function extractPreisBrutto(array $responseData): ?float
    {
        // Direct access to preis_brutto
        if (isset($responseData['preis_brutto']) && is_numeric($responseData['preis_brutto'])) {
            return (float) $responseData['preis_brutto'];
        }

        // Check if it's nested in data array
        if (isset($responseData['data']['preis_brutto']) && is_numeric($responseData['data']['preis_brutto'])) {
            return (float) $responseData['data']['preis_brutto'];
        }

        // Check if it's nested in result array
        if (isset($responseData['result']['preis_brutto']) && is_numeric($responseData['result']['preis_brutto'])) {
            return (float) $responseData['result']['preis_brutto'];
        }

        // Check if it's nested in shipping array
        if (isset($responseData['shipping']['preis_brutto']) && is_numeric($responseData['shipping']['preis_brutto'])) {
            return (float) $responseData['shipping']['preis_brutto'];
        }

        // Recursive search for preis_brutto in nested arrays
        return $this->searchPreisBruttoRecursive($responseData);
    }

    /**
     * Recursively search for preis_brutto in nested arrays
     *
     * @param array $data
     * @return float|null
     */
    private function searchPreisBruttoRecursive(array $data): ?float
    {
        foreach ($data as $key => $value) {
            if ($key === 'preis_brutto' && is_numeric($value)) {
                return (float) $value;
            }

            if (is_array($value)) {
                $result = $this->searchPreisBruttoRecursive($value);
                if ($result !== null) {
                    return $result;
                }
            }
        }

        return null;
    }

    /**
     * Format price as currency
     *
     * @param float $price
     * @return string
     */
    private function formatCurrency(float $price): string
    {
        try {
            // Get current store currency
            $store = $this->storeManager->getStore();
            $currencyCode = $store->getCurrentCurrencyCode();

            // Format the price using Magento's pricing helper
            $formattedPrice = $this->pricingHelper->currency($price, true, false);

            $this->logger->debug('CopeX MQTT: Currency formatting', [
                'original_price' => $price,
                'currency_code' => $currencyCode,
                'formatted_price' => $formattedPrice
            ]);

            return $formattedPrice;

        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Error formatting currency', [
                'price' => $price,
                'error' => $e->getMessage()
            ]);

            // Fallback formatting
            return number_format($price, 2) . ' €';
        }
    }
}
