<?php
namespace CopeX\Mqtt\Controller\Invoice;

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Framework\View\Result\PageFactory;
use Psr\Log\LoggerInterface;
use CopeX\Mqtt\Service\PdfGenerator;
use Magento\Framework\Filesystem\Directory\WriteInterface;

class Pdf extends Action implements HttpGetActionInterface, HttpPostActionInterface
{
    /** @var PageFactory */
    private $resultPageFactory;

    /** @var CustomerSession */
    private $customerSession;

    /** @var JsonFactory */
    private $resultJsonFactory;

    /** @var Validator */
    private $formKeyValidator;

    /** @var LoggerInterface */
    private $logger;

    /**
     * @var PdfGenerator
     */
    private $pdfGenerator;

    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var WriteInterface
     */
    private $varDirectory;
    private $fileFactory;

    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        CustomerSession $customerSession,
        JsonFactory $resultJsonFactory,
        Validator $formKeyValidator,
        LoggerInterface $logger,
        PdfGenerator $pdfGenerator,
        Filesystem $filesystem,
        FileFactory $fileFactory
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->customerSession   = $customerSession;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->formKeyValidator = $formKeyValidator;
        $this->logger = $logger;
        $this->pdfGenerator = $pdfGenerator;
        $this->filesystem = $filesystem;
        $this->fileFactory = $fileFactory;
        $this->varDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
    }

    public function execute()
    {
        $result = $this->resultJsonFactory->create();

        if (!$this->customerSession->isLoggedIn()) {
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('customer/account/login');
        }

        // Validate request method
        if (! $this->getRequest()->isPost()) {
            return $result->setData([
                'success' => false,
                'message' => __('Invalid request method.'),
            ]);
        }

        // Validate form key
        if (! $this->formKeyValidator->validate($this->getRequest())) {
            return $result->setData([
                'success' => false,
                'message' => __('Invalid form key.'),
            ]);
        }

        try {
            $document = $this->getRequest()->getParam('document_name');
            $invoiceNumber = $this->getRequest()->getParam('invoice_number');

            if ($document) {
                $mqttResponse = $this->pdfGenerator->generatePdfInvoice($document);
                if (!empty($mqttResponse) && trim($mqttResponse) !== '') {
                    try {
                        $this->downloadPdfToBrowser($mqttResponse, $invoiceNumber, $document);
                    } catch (\Exception $e) {
                        return $result->setData([
                            'success' => false,
                            'message' => __('Failed to download PDF file: %1', $e->getMessage()),
                        ]);
                    }
                } else {
                    try {
                        $this->downloadPdfToBrowser($this->getSamplePdfData(), $invoiceNumber, $document, true);
                    } catch (\Exception $e) {
                        return $result->setData([
                            'success' => false,
                            'message' => __('Failed to generate sample PDF: %1', $e->getMessage()),
                        ]);
                    }
                }
            }

            return $result->setData([
                'success' => true,
            ]);
        } catch (LocalizedException $e) {
            return $result->setData([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        } catch (\Exception $e) {
            return $result->setData([
                'success' => false,
                'message' => __('An unexpected error occurred while processing PDF generation.'),
            ]);
        }
    }

    private function getSamplePdfData(): string
    {
        return 'JVBERi0xLjQKJSBjcmVhdGVkIGJ5IFBpbGxvdyA5LjEuMCBQREYgZHJpdmVyCjQgMCBvYmo8PAovVHlwZSAvUGFnZXMKL0tpZHMgWyA1IDAgUiBdCi9Db3VudCAxCj4+CmVuZG9iago1IDAgb2JqPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCA0IDAgUgovTWVkaWFCb3ggWzAgMCAzMDAgMTQwXQovQ29udGVudHMgNiAwIFIKL1Jlc291cmNlcyA8PAovRm9udCA8PAovRjEgNyAwIFIKPj4KPj4KPj4KZW5kb2JqCjYgMCBvYmo8PAovTGVuZ3RoIDEyNgovRmlsdGVyIC9GbGF0ZURlY29kZQo+PgpzdHJlYW0KQlQKL0YxIDI0IFRmCjcyIDYwIFRkCihIZWxsbyBQREYpIFRqCkVUCmVuZHN0cmVhbQplbmRvYmoKNyAwIG9iajw8Ci9UeXBlIC9Gb250Ci9TdWJ0eXBlIC9UeXBlMQovQmFzZUZvbnQgL0hlbHZldGljYQo+PgplbmRvYmoKMSAwIG9iajw8Ci9UeXBlIC9DYXRhbG9nCi9QYWdlcyA0IDAgUgo+PgplbmRvYmoKeHJlZgowIDgKMDAwMDAwMDAwMCA2NTUzNSBmIAowMDAwMDAwOTc5IDAwMDAwIG4gCjAwMDAwMDEzOTAgMDAwMDAgbiAKMDAwMDAwMjc0NSAwMDAwMCBuIAowMDAwMDAzMDc1IDAwMDAwIG4gCjAwMDAwMDM5OTkgMDAwMDAgbiAKdHJhaWxlcgo8PAovUm9vdCAxIDAgUgovU2l6ZSA4Cj4+CnN0YXJ0eHJlZgo0MDgKJSVFT0Y=';
    }

    public function downloadPdfToBrowser(string $pdfContent, string $invoiceNumber, string $documentName, bool $isSample = false): \Magento\Framework\App\ResponseInterface
    {
        try {
            // Decode and validate PDF content
            $decodedContent = $this->decodePdfContent($pdfContent);
            $this->validatePdfContent($decodedContent);

            // Generate filename for download
            $filename = $this->generateDownloadFilename($invoiceNumber, $isSample);

            // Create file response for browser download
            return $this->fileFactory->create(
                $filename,
                $decodedContent,
                DirectoryList::VAR_DIR,
                'application/pdf'
            );

        } catch (\Exception $e) {
            throw new LocalizedException(__('Failed to download PDF: %1', $e->getMessage()));
        }
    }

    /**
     * Generate filename for browser download (user-friendly)
     *
     * @param string $invoiceNumber
     * @param bool $isSample
     * @return string
     */
    private function generateDownloadFilename(string $invoiceNumber, bool $isSample): string
    {
        $prefix = $isSample ? 'Sample_' : '';
        $timestamp = date('Y-m-d');

        return sprintf('%sInvoice_%s_%s.pdf', $prefix, $invoiceNumber, $timestamp);
    }

    /**
     * Generate unique filename for server storage
     *
     * @param string $invoiceNumber
     * @param bool $isSample
     * @return string
     */
    private function generateServerFilename(string $invoiceNumber, bool $isSample): string
    {
        $timestamp = date('Y-m-d_H-i-s');
        $prefix = $isSample ? 'sample_' : '';
        $randomSuffix = substr(md5(uniqid()), 0, 8);

        return sprintf('%sinvoice_%s_%s_%s.pdf', $prefix, $invoiceNumber, $timestamp, $randomSuffix);
    }

    /**
     * Decode base64 PDF content
     *
     * @param string $pdfContent
     * @return string
     * @throws LocalizedException
     */
    private function decodePdfContent(string $pdfContent): string
    {
        $decodedContent = base64_decode($pdfContent, true);

        if ($decodedContent === false) {
            throw new LocalizedException(__('Failed to decode base64 PDF content'));
        }

        return $decodedContent;
    }

    /**
     * Validate PDF content
     *
     * @param string $content
     * @throws LocalizedException
     */
    private function validatePdfContent(string $content): void
    {
        if (empty($content)) {
            throw new LocalizedException(__('PDF content is empty'));
        }

        if (strpos($content, '%PDF-') !== 0) {
            throw new LocalizedException(__('Invalid PDF content - missing PDF header'));
        }

        if (strlen($content) < 100) {
            throw new LocalizedException(__('PDF content is too small to be valid'));
        }
    }
}
