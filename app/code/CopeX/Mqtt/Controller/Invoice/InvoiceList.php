<?php
/**
 * CopeX MQTT Module - Invoice List Frontend Controller
 *
 * @category  CopeX
 * @package   CopeX_Mqtt
 * <AUTHOR> Team
 * @copyright Copyright (c) CopeX (https://copex.io/)
 */

namespace CopeX\Mqtt\Controller\Invoice;

use CopeX\Mqtt\Service\InvoiceGenerator;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Controller\Result\RedirectFactory;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\View\Result\PageFactory;
use Psr\Log\LoggerInterface;
use CopeX\Mqtt\Helper\Config;

/**
 * Frontend controller for invoice list page and MQTT requests
 */
class InvoiceList extends Action implements HttpGetActionInterface, HttpPostActionInterface
{
    /**
     * @var PageFactory
     */
    private $resultPageFactory;

    /**
     * @var CustomerSession
     */
    private $customerSession;

    /**
     * @var RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @var JsonFactory
     */
    private $resultJsonFactory;

    /**
     * @var Validator
     */
    private $formKeyValidator;

    /**
     * @var InvoiceGenerator
     */
    private $invoiceGenerator;

    /**
     * @var LoggerInterface
     */
    private $logger;

    private $config;

    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        CustomerSession $customerSession,
        RedirectFactory $resultRedirectFactory,
        JsonFactory $resultJsonFactory,
        Validator $formKeyValidator,
        InvoiceGenerator $invoiceGenerator,
        LoggerInterface $logger,
        Config $config
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->customerSession = $customerSession;
        $this->resultRedirectFactory = $resultRedirectFactory;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->formKeyValidator = $formKeyValidator;
        $this->invoiceGenerator = $invoiceGenerator;
        $this->logger = $logger;
        $this->config = $config;
    }

    /**
     * @return mixed
     */
    public function execute()
    {
        if (!$this->customerSession->isLoggedIn()) {
            $resultRedirect = $this->resultRedirectFactory->create();
            return $resultRedirect->setPath('customer/account/login');
        }

        $resultPage = $this->resultPageFactory->create();

        // Set page title
        $resultPage->getConfig()->getTitle()->set(__('Invoice List'));

        // Add breadcrumbs
        if ($breadcrumbs = $resultPage->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbs->addCrumb('home', [
                'label' => __('Home'),
                'title' => __('Home'),
                'link' => $this->_url->getUrl('')
            ]);
            $breadcrumbs->addCrumb('account', [
                'label' => __('My Account'),
                'title' => __('My Account'),
                'link' => $this->_url->getUrl('customer/account')
            ]);
            $breadcrumbs->addCrumb('invoice_list', [
                'label' => __('Invoice List'),
                'title' => __('Invoice List')
            ]);
        }

        try {
            $customerEmail = $this->customerSession->getCustomer()->getEmail();
            if (!empty($customerEmail)) {
                $success = $this->invoiceGenerator->generateInvoiceForCustomer($customerEmail);
                if (empty($success)) {
                    $success = '';
                }
                $this->passInvoiceDataToTemplate($resultPage, $success);
            }
        } catch (LocalizedException $e) {
            $this->logger->error('CopeX MQTT: Invoice generation request error', [
                'error' => $e->getMessage()
            ]);
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Unexpected error in invoice generation request', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }

        return $resultPage;
    }

    /**
     * @param \Magento\Framework\View\Result\Page $resultPage
     * @param string $invoiceDataF
     * @return void
     */
    private function passInvoiceDataToTemplate(\Magento\Framework\View\Result\Page $resultPage, string $invoiceData): void
    {
        try {
            $blocks = $resultPage->getLayout()->getAllBlocks();
            foreach ($blocks as $block) {
                if ($block instanceof \CopeX\Mqtt\Block\Invoice\InvoiceList) {
                    if ($this->config->isTestDataEnabled()) {
                        $block->setInvoiceData($this->getSampleInvoiceData());
                    } else {
                        $block->setInvoiceData($invoiceData);
                    }
                    break;
                }
            }
        } catch (\Exception $e) {
            $this->logger->error('CopeX MQTT: Failed to pass invoice data to template', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * @return string
     */
    private function getSampleInvoiceData(): string
    {
        return "[{\"Nummer\":2025031060,\"Typ\":\"AR\",\"KundNr\":468165,\"Kunde\":{\"Idnr\":468165,\"LandNr\":43,\"Land\":null,\"Name\":\"TONKOV\",\"Vorname\":\"Grigor\",\"Firma\":\"\",\"Adresse\":\"Riedmarkstr. 14\",\"PLZ\":\"4209\",\"Ort\":\"Engerwitzdorf\",\"Tel\":\"06766063036\",\"Mobil\":null,\"Email\":\"<EMAIL>\",\"Settings\":null},\"KundNr2\":5670,\"Lieferadresse\":null,\"MitarbeiterNr\":2,\"Mitarbeiter\":null,\"Summe\":0,\"SummeBrutto\":0,\"SummeMWST\":0,\"Positionen\":[{\"Id\":3102341,\"Nummer\":2025031060,\"Typ\":\"AR\",\"ArtNr\":1653,\"Artikel\":null,\"Stk\":1,\"Preis_Netto\":0,\"Preis_Brutto\":0,\"MWST\":0,\"Bezeichnung\":\"Versandkostenpauschale DE\",\"Beschreibung\":null,\"MWSTSatz\":0},{\"Id\":3102342,\"Nummer\":2025031060,\"Typ\":\"AR\",\"ArtNr\":328,\"Artikel\":null,\"Stk\":1,\"Preis_Netto\":0,\"Preis_Brutto\":0,\"MWST\":0,\"Bezeichnung\":\"TEST\",\"Beschreibung\":null,\"MWSTSatz\":0},{\"Id\":3102343,\"Nummer\":2025031060,\"Typ\":\"AR\",\"ArtNr\":10007,\"Artikel\":null,\"Stk\":2,\"Preis_Netto\":0,\"Preis_Brutto\":0,\"MWST\":0,\"Bezeichnung\":\"Pros Pro BLACKOUT 200 m 1.24\",\"Beschreibung\":null,\"MWSTSatz\":0}],\"Datum\":\"2025-08-12T11:01:16\",\"Eigenschaften\":null,\"Zustellzeit\":null,\"ZustellzeitBestaetigt\":null,\"ZahlungsMethode\":\"<EMAIL>\",\"ZahlungsBedingung\":\"PayPal\",\"TransportMethode\":\"Versand mit GLS\",\"Abgeschlossen\":true,\"Ausgedruckt\":true,\"Bezahlt\":false,\"Notiz\":\"TEST Rechnung - Nicht löschen bitte!\",\"Woher\":\"AU2024031342\",\"Wohin\":null,\"Dokumente\":[{\"Id\":6,\"DokumenteName\":\"Z:\\\\IntraSell_PDF_AR\\\\Vorgang_AR2025031060.pdf\",\"TabelleName\":\"buchVorgang\",\"FilterString\":\"AR-2025031060\",\"ErstelltAm\":null}]}]";
    }
}
