<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 10.01.18
 * Time: 19:02
 */

namespace CopeX\Winline\Model\Frontend;


use Magento\Framework\App\CacheInterface;
use Magento\Framework\Serialize\Serializer\Json as Serializer;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Eav\Model\Entity\Attribute\Source\BooleanFactory;
use Magento\Framework\App\ObjectManager;

class File extends \Magento\Eav\Model\Entity\Attribute\Frontend\AbstractFrontend
{

    protected $mediaBase;
    protected $mediaBaseAbsolute;
    protected $fileBase = "files/";
    protected $linkTemplate = "<a href='%s'  target='_blank' download title='%s'>%s</a>";
    protected $glue = "</br>";

    /**
     * File constructor.
     * @param BooleanFactory                $attrBooleanFactory
     * @param StoreManagerInterface|null    $storeManager
     * @param \Magento\Framework\Filesystem $filesystem
     */
    public function __construct(
        BooleanFactory $attrBooleanFactory,
        StoreManagerInterface $storeManager = null,
        \Magento\Framework\Filesystem $filesystem
    )
    {
        $this->mediaBaseAbsolute = $filesystem->getDirectoryRead(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA)->getAbsolutePath();
        $storeManager = $storeManager ?: ObjectManager::getInstance()->get(StoreManagerInterface::class);
        $this->mediaBase = $storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
        parent::__construct($attrBooleanFactory);
    }

    public function getValue(\Magento\Framework\DataObject $object)
    {
        $returnValue = $value = parent::getValue($object);
        if ($value) {
            $files = explode(";", $value);
            $parts = [];
            foreach ($files as $file) {
                $file = trim($file);
                if ($this->fileExists($file)) {
                    $path = $this->getPath($file);
                    $name = $this->getName($file);
                    $parts []= sprintf($this->linkTemplate, $path, $name, $name);
                }
            }
            $returnValue = implode($this->glue, $parts);

        }
        return $returnValue;
    }

    public function getPath($file)
    {
        return $this->mediaBase . $this->fileBase . $file;
    }

    public function getName($name){
        return $name;
    }

    public function fileExists($file)
    {
        return file_exists($this->mediaBaseAbsolute . $this->fileBase . $file);
    }
}