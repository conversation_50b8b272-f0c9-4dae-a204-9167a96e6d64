<?php
/**
 * Created by PhpStorm.
 * User: pointi
 * Date: 01.06.18
 * Time: 07:01
 */

namespace CopeX\Winline\Helper\Import;

use Magento\Framework\Exception\LocalizedException;

class Category extends \CopeX\Winline\Helper\Data
{
    protected $categoryCache;
    protected $errors;

    public function initCategories()
    {
        $this->categoryCollection = $this->categoryCollectionFactory->create();
        try {
            $this->categoryCollection->addAttributeToSelect('category_number');
        } catch (LocalizedException $e) {
        }
        /* @var $collection \Magento\Catalog\Model\ResourceModel\Category\Collection */
        foreach ($this->categoryCollection as $category) {
            if ($category->hasCategoryNumber()) {
                $this->categoryCache[$category->getCategoryNumber()] = $category->getId();
            }
        }
    }

    /**
     * @param $transport
     * @throws \Exception
     */
    public function importCategories($transport)
    {
        $items = $transport->getItems();
        $item = reset($items);
        $groupCode = $this->cutTrailingCategories($item['Artikeluntergruppencode'], "-");
        $groupCode = str_replace("-", "/", $groupCode);
        if (isset($this->categoryCache[$groupCode])) {
            $category = $this->categoryCollection->getItemById($this->categoryCache[$groupCode]);
            $category->setName($item['Artikeluntergruppenbezeichnung']);
            if ($item['Grafikfile']) {
                $mediaAttribute = ['image', 'small_image', 'thumbnail'];
                $categoryFolder = $this->directoryList->getPath(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA) .
                                  '/catalog/category/';
                if (!file_exists($categoryFolder)) {
                    mkdir($categoryFolder, 0777);
                }
                if (file_exists($this->mediaImportPath . $item['Grafikfile'])) {
                    copy($this->mediaImportPath . $item['Grafikfile'], $categoryFolder . $item['Grafikfile']);
                    try {
                        $category->setImage($item['Grafikfile'], $mediaAttribute, true, false)
                            ->save(); // make sure image will be in pub/media/catalog/category/
                    } catch (\Exception $e) {
                        $this->errors [] = "Category " . $category->getID() . ": " . $e->getMessage();
                    }
                }
            }

            try {
                $this->categoryResource->saveAttribute($category, 'name');
            } catch (\Exception $e) {
                $this->errors [] = "Category " . $category->getID() . ": " . $e->getMessage();
            }
        }
        $transport->addItem(['skip' => 1]);
        return;
    }

    /**
     * @throws \Exception
     */
    public function successMessage()
    {
        if ($this->errors) {
            throw new \Exception(implode("\n", $this->errors));
        }
        throw new \Exception(__("Category name import successful. Imported %1 categories.",
            count($this->categoryCache)));
    }

}