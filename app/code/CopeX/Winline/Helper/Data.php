<?php

namespace CopeX\Winline\Helper;

use Cope<PERSON>\Import\Helper\AbstractHelper;
use CopeX\Import\Helper\Log;
use CopeX\Import\Model\Mapper;
use Magento\Directory\Model\ResourceModel\Country\CollectionFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Indexer\Model\IndexerFactory;
use Magento\Store\Model\StoreManagerInterface;

class Data extends AbstractHelper
{

    const CONNECTION_WIDTH = 'connection_width';
    const FILTER_FINENESS = 'filter_fineness';
    const MEDIUM = 'medium';

    /**
     * @var IndexerFactory
     */
    protected $indexerFactory;
    /**
     * @var Log $logger
     */

    protected $_countryCollectionFactory, $storeManager;
    protected $categoryCollectionFactory;
    protected $categoryCollection;
    protected $categoryResource;
    protected $mediaImportPath;
    protected $mediaPath;
    protected $_processedSkus;
    protected $resourceConnection;
    /**
     * @var \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory
     */
    protected $productCollectionFactory;
    /**
     * @var \Magento\Catalog\Model\ResourceModel\ProductFactory
     */
    protected $productFactory;
    /**
     * @var \Magento\Catalog\Model\Product\OptionFactory
     */
    protected $optionFactory;
    protected $driverFile;
    protected $categoryProductRelation;
    protected $categoryProductRelationToRestore;
    protected $_processedCategories;
    protected $_currentDatetime = null;
    protected $countryArray = null;
    /**
     * @var \Magento\Catalog\Model\Product\Url
     */
    protected $url;

    /**
     * Data constructor.
     * @param IndexerFactory                                                  $indexerFactory
     * @param Context                                                         $context
     * @param DirectoryList                                                   $directoryList
     * @param \Magento\Framework\Filesystem\Driver\File                       $driverFile
     * @param DateTime                                                        $date
     * @param CollectionFactory                                               $countryCollectionFactory
     * @param Mapper                                                          $mapper
     * @param StoreManagerInterface                                           $storeManager
     * @param \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollectionFactory
     * @param \Magento\Catalog\Model\ResourceModel\CategoryFactory            $categoryFactory
     * @param ResourceConnection                                              $resourceConnection
     * @param \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory  $productCollectionFactory
     * @param \Magento\Catalog\Model\ResourceModel\ProductFactory             $productFactory
     * @param \Magento\Catalog\Model\Product\OptionFactory                    $optionFactory
     * @param \Magento\Catalog\Model\Product\Url                              $url
     */
    public function __construct(
        IndexerFactory $indexerFactory,
        Context $context,
        DirectoryList $directoryList,
        \Magento\Framework\Filesystem\Driver\File $driverFile,
        DateTime $date,
        CollectionFactory $countryCollectionFactory,
        Mapper $mapper,
        StoreManagerInterface $storeManager,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollectionFactory,
        \Magento\Catalog\Model\ResourceModel\CategoryFactory $categoryFactory,
        ResourceConnection $resourceConnection,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Catalog\Model\ResourceModel\ProductFactory $productFactory,
        \Magento\Catalog\Model\Product\OptionFactory $optionFactory,
        \Magento\Catalog\Model\Product\Url $url
    ) {
        parent::__construct($context, $directoryList, $date, $mapper);
        $this->indexerFactory = $indexerFactory;
        $this->_countryCollectionFactory = $countryCollectionFactory;
        $this->storeManager = $storeManager;
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        $this->categoryResource = $categoryFactory->create();
        $this->resourceConnection = $resourceConnection;
        $this->driverFile = $driverFile;
        try {
            $this->mediaPath = $this->directoryList->getPath(\Magento\Framework\App\Filesystem\DirectoryList::MEDIA);
            $this->mediaImportPath = $this->mediaPath .
                                     "/import/";
        } catch (FileSystemException $e) {
        }
        $this->productCollectionFactory = $productCollectionFactory;
        $this->productFactory = $productFactory;
        $this->optionFactory = $optionFactory;
        $this->url = $url;
    }

    /**
     * @param $transport
     * @throws \Exception
     */
    public function processBefore($transport)
    {
        $lineCount = $this->getLineCount($transport);
        $this->checkProductCountDifference($lineCount);
        $this->getCurrentDatetime();
        $this->categoryProductRelation = $this->getCategoryProductRelation();
        $this->truncateCategoryProductRelation();
        $this->truncateProductImages();
    }

    /**
     * @param $transport
     * @return int
     * @throws \Exception
     */
    public function getLineCount($transport)
    {
        $sourceAdapter = $transport->getAdapter();
        $lineCount = 0;
        while ($sourceAdapter->valid()) {
            $lineCount++;
            $sourceAdapter->next();
        }
        $sourceAdapter->rewind();
        if (!$lineCount) {
            throw new \Exception (__("File is empty!"));
        }
        return $lineCount;
    }

    /**
     * @param $lineCount
     * @throws \Exception
     */
    public function checkProductCountDifference($lineCount)
    {
        $productCollection = $this->productCollectionFactory->create();
        $this->addImportedFilter($productCollection);
        $productCollection->addAttributeToFilter('status', 1);
        $totalCount = $productCollection->getSize();
        if ($totalCount === 0 || 100 / $totalCount * $lineCount < 80) {
            throw new \Exception ("More than 20% of products would be deleted. Aborting!");
        }
    }

    public function getCurrentDatetime()
    {
        if ($this->_currentDatetime === null) {
            $this->_currentDatetime = $this->date->date('Y-m-d H:i:s');
        }
        return $this->_currentDatetime;
    }

    protected function getCategoryProductRelation()
    {
        $connection = $this->resourceConnection->getConnection(ResourceConnection::DEFAULT_CONNECTION);
        $tableName = $connection->getTableName('catalog_category_product');
        $relation = $connection->fetchAll("select * from " . $tableName);
        foreach ($relation as $oldKey => $entry) {
            $key = $entry['category_id'] . "-" . $entry['product_id'];
            $relation[$key] = $entry;
            unset($relation[$oldKey]);
        }
        return $relation;
    }

    public function truncateCategoryProductRelation()
    {
        $connection = $this->resourceConnection->getConnection(ResourceConnection::DEFAULT_CONNECTION);
        $categoryProductTable = $connection->getTableName('catalog_category_product');
        $productTableVarchar = $connection->getTableName('catalog_product_entity_varchar');
        $categoryTableVarchar = $connection->getTableName('catalog_category_entity_varchar');
        /** @var \Magento\Catalog\Model\ResourceModel\Product $product */
        $product = $this->productFactory->create();
        $categoryNumber = $this->categoryResource->getAttribute('category_number');
        $importAttribute = $product->getAttribute('copex_import');
        $query = "select distinct cp.product_id from {$categoryProductTable} cp join {$productTableVarchar} v on cp.product_id = v.entity_id and v.attribute_id = {$importAttribute->getId()}";
        $categoryQuery = "select distinct cp.category_id from {$categoryProductTable} cp join {$categoryTableVarchar} v on cp.category_id = v.entity_id and v.attribute_id = {$categoryNumber->getId()} and value is not null and value != ''";
        $allIds = $connection->fetchAssoc($query);
        $categoryIds = $connection->fetchAssoc($categoryQuery);
        if (count($allIds)) {
            $connection->delete($categoryProductTable,
                'product_id in (' . implode(",", array_keys($allIds)) . ') AND category_id in (' .
                implode(",", array_keys($categoryIds)) . ")");
            $this->categoryProductRelationToRestore = $this->getCategoryProductRelation();
        }
    }

    public function truncateProductImages()
    {
        /** @var \Magento\Catalog\Model\ResourceModel\Product\Collection $productCollection */
        $productCollection = $this->productCollectionFactory->create();
        $this->addImportedFilter($productCollection);
        $importedProductIds = $productCollection->getAllIds();
        $connection = $this->resourceConnection->getConnection(ResourceConnection::DEFAULT_CONNECTION);
        $galleryTable = $connection->getTableName('catalog_product_entity_media_gallery');
        $galleryTableValue = $connection->getTableName('catalog_product_entity_media_gallery_value');
        $galleryValueToEntity = $connection->getTableName('catalog_product_entity_media_gallery_value_to_entity');

        try {
            $entityWhere = $importedProductIds ? ("entity_id in ('" . implode("','", $importedProductIds) . "')") : "";
            $connection->delete($galleryTableValue, $entityWhere);
            $connection->delete($galleryValueToEntity, $entityWhere);
            $connection->delete($galleryTable, "value_id not in (select value_id from " . $galleryTableValue . ")");
            $imagesNotToDelete = $connection->fetchAssoc("select value from " . $galleryTable);
            $dir = $this->mediaPath . "/catalog/product/";
            $files = $this->driverFile->readDirectoryRecursively($dir);
            foreach ($files as $file) {
                if (strpos((string)$file, "/cache/") !== false ||
                    array_key_exists(substr($file, strlen($dir) - 1), $imagesNotToDelete)) {
                    continue;
                }
                if ($this->driverFile->isFile($file)) {
                    $this->driverFile->deleteFile($file);
                }
            }
        } catch (\Exception $exception) {
        }
    }

    protected function addImportedFilter($collection)
    {
        $collection->addAttributeToFilter('copex_import', ['notnull' => true]);
    }

    /**
     * Tries to find a country by its international localized name
     * @param $profile
     * @param $item
     * @param $field
     * @return mixed|string
     */
    public function getCountryIDByLocalizedName($profile, $item, $field)
    {
        $field = trim((string)$this->getFieldValue($item, $field, $profile));
        $field = mb_convert_case($field, MB_CASE_TITLE);
        $countries = $this->getCountries();
        $countryKey = array_search($field, array_column($countries, 'label'));
        if ($countries[$countryKey]['value'] != null) {
            return $countries[$countryKey]['value'];
        }
        return $field;
    }

    /**
     * Retrieve list of countries in array option
     * @return array
     */
    public function getCountries()
    {
        if (!$this->countryArray) {
            $this->countryArray = $this->getCountryCollection()->toOptionArray();
        }
        return $this->countryArray;
    }

    public function getCountryCollection()
    {
        $collection = $this->_countryCollectionFactory->create()->loadByStore();
        return $collection;
    }

    /**
     * @param $profile
     * @param $item
     * @param $field
     * @return string
     */
    public function cutEmptyCategories($profile, $item, $field)
    {
        $field = $this->getFieldValue($item, $field, $profile);
        return $this->cutTrailingCategories($field);
    }

    protected function cutTrailingCategories($field, $delimiter = "/")
    {
        $categories = explode($delimiter, $field);
        foreach ($categories as $key => $category) {
            if (intval($category) <= 0) {
                $categories = array_slice($categories, 0, $key);
                break;
            }
        }
        return implode($delimiter, $categories);
    }

    /**
     * Tries to strip image file names to import
     * @param $profile
     * @param $item
     * @param $field
     * @return mixed|string
     */
    public function getImageName($profile, $item, $field)
    {
        $fieldValue = trim((string)$this->getFieldValue($item, $field, $profile));
        if (!file_exists($this->mediaImportPath . $fieldValue)) {
            $fieldValue = "";
        }
        return $fieldValue;
    }

    public function afterProductImport($transport)
    {
        $this->updateProductsFromCategories();
        $this->reIndexAfterProductImport();
        $this->deactivateOrphanProducts($transport);
        $this->deleteNotImportedCategories($transport);
    }

    public function updateProductsFromCategories()
    {
        $newRelations = $this->getCategoryProductRelation();
        $connection = $this->resourceConnection->getConnection(ResourceConnection::DEFAULT_CONNECTION);
        $tableName = $connection->getTableName('catalog_category_product');
        if ($this->categoryProductRelation) {
            $oldAssociations = array_intersect_key($this->categoryProductRelation, $newRelations);
            $connection->insertOnDuplicate($tableName, $oldAssociations, ['position']);
        }
        if ($this->categoryProductRelationToRestore) {
            $connection->insertOnDuplicate($tableName, $this->categoryProductRelationToRestore, ['position']);
        }
    }

    protected function reIndexAfterProductImport()
    {
        $this->reindexIndex('catalog_category_product');
        $this->reindexIndex('catalog_product_category');
    }

    public function reindexIndex($indexerName)
    {
        $indexer = $this->indexerFactory->create();
        $indexer->load($indexerName);
        $indexer->reindexAll();
    }

    /**
     * deactivate all products which are not imported
     */
    protected function deactivateOrphanProducts($transport)
    {
        $collection = $this->productCollectionFactory->create();
        $collection->addAttributeToFilter('sku', ['nin' => $this->_processedSkus]);
        $this->addImportedFilter($collection);
        $collection->walk([$this, 'deactivateItem']);
    }

    public function deleteNotImportedCategories($transport)
    {
        /** @var \Magento\Catalog\Model\ResourceModel\Category\Collection $categoryCollection * */
        $categoryCollection = $this->categoryCollectionFactory->create();
        $categoryCollection->addFieldToFilter('category_number', ["nin" => array_keys($this->_processedCategories)]);
        if ($categoryCollection->count() && $categoryCollection->count() < 50) {
            $categoryCollection->delete();
        }
    }

    /**
     * helper to remember processed skus which will diffed against database after import
     * @param $transport
     */
    public function fieldmapAfter($transport)
    {
        $item = $transport->getItems();
        $item = reset($item);
        $this->_processedSkus[] = $item['sku'];
        if ($item['categories']) {
            $categories = array_slice(explode("/", $item['categories']), 1);
            for ($i = 1; $i <= sizeof($categories); $i++) {
                $this->_processedCategories [implode("/", array_slice($categories, 0, $i))] = true;
            }
        }
    }

    public function deactivateItem($item)
    {
        if ($item->getData('copex_import')) {
            $item->setStatus(2);
            $product = $this->productFactory->create();
            $product->saveAttribute($item, 'status');
        }
    }

    protected function cleanString($string)
    {
        $search = ["Ä", "Ö", "Ü", "ä", "ö", "ü", "ß", "´", " "];
        $replace = ["AE", "OE", "UE", "ae", "oe", "ue", "ss", "", "-"];
        $string = str_replace($search, $replace, $string);
        return preg_replace('/[^A-Za-z0-9\-\_\.]/', '', $string); // Removes special chars.
    }

    public function getUrlKey($profile, $item, $value)
    {
        $value = $this->mapper->mapField($item, $value, $profile);
        return $this->url->formatUrlKey($value);
    }

}
