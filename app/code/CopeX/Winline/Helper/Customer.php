<?php

namespace CopeX\Winline\Helper;

use Cope<PERSON>\Import\Helper\AbstractHelper;
use Magento\Customer\Model\AccountManagement;
use Magento\Customer\Model\ResourceModel\Address\CollectionFactory as AddressCollectionFactory;
use Magento\Framework\DataObject;
use Magento\Customer\Model\AddressFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\App\Area;
use Magento\Framework\App\State;
use Magento\Framework\App\AreaList;
use Monolog\Logger;

class Customer extends AbstractHelper
{

    const ACCOUNT_NUMBER = 'account_number';
    const EMAIL = 'email';

    const DEFAULT_ACCOUNT_NUMBER = 200000;

    protected $customerAddresses = [];
    protected $customersBilling = [];
    protected $staffIds = [];
    protected $customer, $addressFactory;
    protected $customerDataBeforeImport = [];
    protected $addressResource;

    protected $dataHelper;

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @var \Zend\Validator\EmailAddress
     */
    protected $emailValidator;

    /** @var \MageB2B\Staff\Model\ResourceModel\Staff\CollectionFactory */
    protected $staffCollectionFactory;
    protected $addressCollectionFactory;
    protected $customerResource;
    protected $customerCollectionFactory;
    protected $addressRegistry;
    protected $logHelper;
    protected $staffEmails;
    protected $addressCache;
    /**
     * @var State
     */
    protected $state;
    /**
     * @var AreaList
     */
    protected $areaList;
    protected $tempModel;
    protected $tempResourceModel;
    protected $tempFactory;
    protected $staffCache;
    /**
     * Skip invalid email addresses and combines addresses with same email
     * @var array
     */
    protected $emailAddresses = [];
    protected $processedEmailAddresses = [];
    protected $search = ["Ä", "Ö", "Ü", "ä", "ö", "ü", "ß"];
    protected $replace = ["Ae", "Oe", "Ue", "ae", "oe", "ue", "ss"];
    private $customerAccountManagement;
    private $euCountries = [];
    /**
     * @param        $value
     * @param string $attribute
     * @return \Magento\Customer\Model\ResourceModel\Customer\Collection
     */
    private $customerCache = [];
    private $customerAccountNumberCache = [];
    /**
     * @var \Magento\Directory\Api\CountryInformationAcquirerInterface
     */
    private $countryInformationAcquirer;
    /**
     * @var \Magento\Directory\Api\Data\CountryInformationInterface[]
     */
    private $validCountries;
    private \Magento\Framework\Registry $registry;

    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \CopeX\Import\Model\Mapper $mapper,
        \Magento\Customer\Model\Customer $customer,
        \Magento\Customer\Model\ResourceModel\Customer $customerResource,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory,
        AddressFactory $addressFactory,
        AccountManagement $customerAccountManagement,
        AddressCollectionFactory $addressCollectionFactory,
        \Magento\Customer\Model\ResourceModel\Address $addressResource,
        \Magento\Customer\Model\AddressRegistry $addressRegistry,
        Data $dataHelper,
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        \MageB2B\Staff\Model\ResourceModel\Staff\CollectionFactory $staffCollectionFactory,
        \CopeX\Import\Helper\Log $logHelper,
        \CopeX\RegistrationHandler\Model\Temp $tempModel,
        \CopeX\RegistrationHandler\Model\ResourceModel\Temp $tempResourceModel,
        \CopeX\RegistrationHandler\Model\TempFactory $tempFactory,
        State $state,
        AreaList $areaList,
        \Magento\Directory\Api\CountryInformationAcquirerInterface $countryInformationAcquirer,
        \Magento\Framework\Registry $registry
    ) {
        parent::__construct($context, $directoryList, $date, $mapper);
        $this->customer = $customer;
        $this->customerResource = $customerResource;
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->customerAccountManagement = $customerAccountManagement;
        $this->addressFactory = $addressFactory;
        $this->addressResource = $addressResource;
        $this->addressRegistry = $addressRegistry;
        $this->addressCollectionFactory = $addressCollectionFactory;
        $this->dataHelper = $dataHelper;
        $this->resourceConnection = $resourceConnection;
        $this->emailValidator = new \Zend\Validator\EmailAddress();
        $this->staffCollectionFactory = $staffCollectionFactory;
        $this->logHelper = $logHelper;
        $this->staffEmails = $this->staffCollectionFactory->create()->emailOptionHash();
        $this->tempModel = $tempModel;
        $this->tempResourceModel = $tempResourceModel;
        $this->tempFactory = $tempFactory;
        $this->state = $state;
        $this->areaList = $areaList;
        $this->countryInformationAcquirer = $countryInformationAcquirer;
        $this->registry = $registry;
    }

    /**
     * @param $transport
     * @throws \Exception
     */
    public function processBefore($transport)
    {
        $this->checkCustomerCount($transport);
        $this->cacheCustomerDataBeforeImport();
        $this->cacheCustomerAddresses($transport);
        foreach ($this->countryInformationAcquirer->getCountriesInfo() as $countryInformation) {
            $this->validCountries [] = $countryInformation->getTwoLetterAbbreviation();
        }
    }

    /**
     * caches the customer status code, and if status_code changes trigger the sendpassword mail
     * @param $transport
     */
    public function cacheCustomerDataBeforeImport()
    {
        $customerCollection = $this->customerCollectionFactory->create();
        $customerCollection->addAttributeToFilter('status_code', ['notnull' => true]);
        foreach ($customerCollection as $customer) {
            $this->customerDataBeforeImport[$customer->getEmail()] = [
                'status_code' => $customer->getStatusCode(),
                'firstname'   => $customer->getFirstname(),
                'lastname'    => $customer->getLastname(),
            ];
        }
    }

    public function importAfter()
    {
        $this->initCustomerCache();
        $this->setBillingAddresses();
        $this->addAdditionalAddresses();
        $this->assignStaffIds();
        $this->deleteNotImportedCustomers();
        $this->reIndexCustomerGrid();
        $this->checkStatusCodeChanges();
    }

    private function initCustomerCache()
    {
        if (!$this->customerCache) {
            $customerCollection = $this->customerCollectionFactory->create();
            $customerCollection->addAttributeToSelect('*');
            $customerCollection->addAttributeToFilter('account_number', ['notnull' => true]);
            foreach ($customerCollection as $customer) {
                $this->customerCache[$customer->getEmail()] = $customer;
                $this->customerAccountNumberCache[$customer->getAccountNumber()] = $customer->getEmail();
            }
        }
    }

    /**
     * checks if customers which are imported had a status_code 0 before import
     * @throws \Exception
     */
    protected function checkStatusCodeChanges()
    {
        //load frontend translation
        try {
            $this->state->getAreaCode();
        } catch (LocalizedException $e) {
            $this->state->setAreaCode(Area::AREA_FRONTEND);
        }
        $this->areaList->getArea(Area::AREA_FRONTEND)->load(Area::PART_TRANSLATE);

        $this->initCustomerCache();
        foreach ($this->customerCache as $email => $entity) {
            //if status_code is not 3 we can skip the customer
            if ($entity->getData('status_code') == 3) {
                //otherwise the status_code is changed after import and we send a mail here to the customer
                //check if the customer was on status_code 0 before import
                if (isset($this->customerDataBeforeImport[$email])) {
                    $oldStatusCode = $this->customerDataBeforeImport[$email]['status_code'];
                    if ($oldStatusCode < 3) {
                        try {
                            /** @var $entity \Magento\Customer\Model\Customer */
                            $tempModel = $this->tempFactory->create();
                            $this->tempResourceModel->load($tempModel, $email, self::EMAIL);
                            if ($tempModel->getId()) {
                                $entity->setFirstname($tempModel->getFirstname());
                                $entity->setLastname($tempModel->getLastname());
                                $entity->setPasswordHash($tempModel->getPassword());
                                $this->customerResource->save($entity);
                                $this->tempResourceModel->delete($tempModel);
                            }

                            $entity->sendNewAccountEmail('confirmed');

                            $this->logHelper->log('Willkommens-Mail für ' . $entity->getEmail() . ' gesendet.',
                                Logger::NOTICE);
                        } catch (LocalizedException $e) {
                            $this->logHelper->log($e->getMessage(), Logger::CRITICAL);
                        }
                    }
                }
            }
        }
    }

    public function fieldMapAfter($transport)
    {
        $this->skipInvalidEmails($transport);
        $this->skipInvalidCountries($transport);
        $this->cacheStaffAssignment($transport);

        if (!$transport->getSkip()) {
            $this->skipPersonalNames($transport);
            $this->addAddressId($transport);
        }
    }

    protected function skipInvalidCountries($transport)
    {
        $items = $transport->getItems();
        foreach ($items as $key => $item) {
            if (isset($item['_address_country_id']) && $item['_address_country_id'] &&
                !in_array($item['_address_country_id'], $this->validCountries)) {
                $this->logHelper->log("Invalid country '" . $item['_address_country_id'] . "' for customer with id: " . $item['account_number']);
                $transport->setSkip(true);
            }
        }
    }

    protected function skipInvalidEmails($transport)
    {
        $items = $transport->getItems();

        foreach ($items as $key => $item) {
            $email = $item[self::EMAIL];
            if (in_array($email, $this->staffEmails)) {
                $this->logHelper->log("Staff email found: " . $email . " Skipping " . self::ACCOUNT_NUMBER . ": " .
                                      $item[self::ACCOUNT_NUMBER], Logger::WARNING);
                $transport->setSkip(true);
            } else {
                if ($email && $this->emailValidator->isValid($email)) {
                    if (isset($this->emailAddresses[$email])) { //Skip row to combine addresses
                        $transport->setSkip(true);
                    }
                    $this->emailAddresses[$email][] = $item;
                    $items[$key] = $item;
                    $transport->setItems($items);
                } else {
                    if ($email) {
                        $this->logHelper->log("Invalid Email: " . $email, Logger::WARNING);
                    }
                    $transport->setSkip(true);
                    break;
                }
            }
        }
    }

    protected function cacheStaffAssignment($transport)
    {
        $items = $transport->getItems();

        foreach ($items as $key => $item) {
            $this->staffIds[$item['sales_rep']] [strtolower($item[self::EMAIL])] = true;
        }
    }

    protected function skipPersonalNames($transport)
    {
        $items = $transport->getItems();
        foreach ($items as $key => $item) {
            if (isset($this->customerDataBeforeImport[$item[self::EMAIL]])) {
                $items[$key]['firstname'] = $this->customerDataBeforeImport[$item[self::EMAIL]]['firstname'];
                $items[$key]['lastname'] = $this->customerDataBeforeImport[$item[self::EMAIL]]['lastname'];
            }
        }
        $transport->setItems($items);
    }

    protected function addAddressId($transport)
    {
        $this->initAddressCache();
        $items = $transport->getItems();
        foreach ($items as $key => $item) {
            $cacheKey = $this->getAddressCacheKey($item, '_address_');
            if (isset($this->addressCache[$cacheKey])) {
                $items[$key]['_address__entity_id'] = $this->addressCache [$cacheKey];
            }
        }

        $transport->setItems($items);
    }

    protected function initAddressCache()
    {
        if (!$this->addressCache) {
            $addressAttributes = [
                'firstname',
                'lastname',
                'street',
                'city',
                self::ACCOUNT_NUMBER,
                'parent_id',
                self::EMAIL,
            ];
            $addressesCollection = $this->addressCollectionFactory->create()
                ->addAttributeToSelect($addressAttributes)
                ->joinField("email", "customer_entity", self::EMAIL, "entity_id = parent_id");
            foreach ($addressesCollection as $addressId => $address) {
                $cacheKey = $this->getAddressCacheKey($address);
                if (!isset($this->addressCache[$cacheKey])) {
                    $this->addressCache [$cacheKey] = $addressId;
                }
            }
        }
    }

    protected function getAddressCacheKey($address, $prefix = "")
    {
        $cacheKey = $address[$prefix . "account_number"];
        $cacheKey .= "-" . strtolower($address["email"]);
        return $cacheKey;
    }

    public function importEntitiesBefore($transport)
    {
        $this->rememberProcessed($transport);
    }

    /**
     * helper to remember processed customerIds which will diffed against database after import
     * @param $transport
     */
    public function rememberProcessed($transport)
    {
        $entities = $transport->getData('entities');
        foreach ($entities as $entity) {
            $this->processedEmailAddresses[] = $entity[self::EMAIL];
        }
    }

    public function cacheCustomerAddresses($transport)
    {
        $sourceAdapter = $transport->getData('adapter');
        while ($sourceAdapter->valid()) {
            $currentRow = $sourceAdapter->current();
            $this->customerAddresses [$currentRow['Kontonummer']] = $currentRow;
            if ($currentRow['Rechnungsempfanger'] != "") {
                $this->customersBilling [] = $currentRow;
            }
            $sourceAdapter->next();
        }
        $sourceAdapter->rewind();
    }

    public function isEUCountry($profile, $item, $field)
    {
        if (!$this->euCountries) {
            $countries = $this
                ->scopeConfig
                ->getValue(
                    'general/country/eu_countries'
                );
            $this->euCountries = explode(",", $countries);
        }
        $country = $this->getFieldValue($item, $field, $profile);

        return in_array($country, $this->euCountries);
    }

    /**
     * @param $lineCount
     * @throws \Exception
     */
    protected function checkCustomerCount($transport)
    {
        $lineCount = $this->dataHelper->getLineCount($transport);
        $customerCountCollection = $this->customerCollectionFactory->create();
        $customerCount = $customerCountCollection->addAttributeToFilter(self::EMAIL, ['nin' => $this->staffEmails])
            ->addAttributeToFilter('imported', ['eq' => 1])
            ->getSize();
        if ($customerCount && 100 / $customerCount * $lineCount < 80) {
            throw new \Exception("More than 20% of customers would be deleted. Aborting!");
        }
    }

    protected function deleteNotImportedCustomers()
    {
        $this->registry->register('isSecureArea', true, true);
        $collection = $this->customerCollectionFactory->create();
        $collection->addAttributeToFilter(
            [
                ['attribute' => self::EMAIL, 'nin' => $this->processedEmailAddresses],
                ['attribute' => self::ACCOUNT_NUMBER, "null" => true],
            ],
            null, "left");

        $collection->addAttributeToFilter(self::EMAIL, ['nin' => $this->staffEmails]);
        $collection->addAttributeToFilter('imported', ['eq' => 1]);
        if ($collection->count()) {
            $collection->walk('delete');
            $this->logHelper->log("Customers deleted: " . $collection->count(), Logger::NOTICE);
        }
        $this->registry->register('isSecureArea', false, true);
    }

    protected function assignStaffIds()
    {
        $connection = $this->resourceConnection->getConnection();
        $tableCustomerSalesStaffCustomer = $this->resourceConnection->getTableName('customer_salesstaff_customer'); //gives table name with prefix
        foreach ($this->staffIds as $staffId => $accountNumbers) {
            $customers = $this->getCustomersByEmail(array_filter(array_keys($accountNumbers)));
            $customerIds = [];
            $staffEntityId = $this->getStaffEntityId($staffId);
            if ($staffEntityId) {
                foreach ($customers as $customer) {
                    $customerIds[] = $customer->getId() . ',' . $staffEntityId;
                }
            }

            if ($customerIds) {
                $query = 'INSERT INTO ' . $tableCustomerSalesStaffCustomer . ' (customer_id, staff_id) VALUES (' .
                         implode('),(', $customerIds) . ') ON DUPLICATE KEY UPDATE
                customer_id=VALUES(customer_id),
                staff_id=VALUES(staff_id)';
                try {
                    $connection->query($query);
                } catch (\Exception $exception) {
                    $this->logHelper->log($exception->getMessage(), Logger::ERROR);
                }
            }
        }
    }

    private function getCustomersByEmail($values)
    {
        $this->initCustomerCache();
        if (!is_array($values)) {
            $values = [$values];
        }
        $result = [];
        if (count($values) > 0) {
            $result = array_intersect_key($this->customerCache, array_flip($values));
        }
        return $result;
    }

    protected function getStaffEntityId($staffId)
    {
        if (!$this->staffCache) {
            $staffCollection = $this->staffCollectionFactory->create();
            foreach ($staffCollection as $staffMember) {
                $this->staffCache[$staffMember->getOwnStaffId()] = $staffMember->getId();
            }
        }
        return isset($this->staffCache[$staffId]) ? $this->staffCache[$staffId] : null;
    }

    /**
     * Add the additional addresses if a email is set multiple times
     */
    protected function addAdditionalAddresses()
    {
        $additionalAddresses = array_filter($this->emailAddresses, function ($items) {
            return count($items) > 1;
        });
        $customers = $this->getCustomersByEmail(array_keys($additionalAddresses));
        foreach ($customers as $customer) {
            foreach ($this->emailAddresses[$customer->getEmail()] as $i => $address) {
                if ($i == 0) {
                    continue;
                }
                $data = [];
                foreach ($address as $key => $value) {
                    if (strpos($key, '_address') === 0) {
                        $data[str_replace("_address_", "", $key)] = $value;
                    }
                }

                $dataObject = new DataObject($data);
                $additionalAddress = $this->getAddressFromCustomer($customer, $dataObject);
                foreach ($dataObject->toArray() as $key => $value) {
                    $additionalAddress->setData($key, $value);
                }
                $additionalAddress->setAccountNumber($address[self::ACCOUNT_NUMBER]);
                $this->addressResource->save($additionalAddress);
            }
        }
    }

    /**
     * @param $customer
     * @param $item
     * @return \Magento\Customer\Model\Address
     */
    protected function getAddressFromCustomer($customer, $item)
    {
        $newAddress = false;
        foreach ($customer->getAddresses() as $address) {
            if ($this->isSameAddress($address, $item)) {
                $newAddress = $address;
                break;
            }
        }
        if (!$newAddress) {
            $newAddress = $this->addressFactory->create();
            $newAddress->setData($item->getData());
            $newAddress->setCustomerId($customer->getId());
        }
        return $newAddress;
    }

    protected function isSameAddress($address1, $address2)
    {
        $street1 = is_array($address1->getStreet()) ? implode(" ", $address1->getStreet()) : $address1->getStreet();
        $street2 = is_array($address2->getStreet()) ? implode(" ", $address2->getStreet()) : $address2->getStreet();
        return trim($address1->getFirstname()) == trim($address2->getFirstname()) &&
               trim($address1->getLastname()) == trim($address2->getLastname()) &&
               trim($street1) == trim($street2) &&
               trim($address1->getCity()) == trim($address2->getCity());
    }

    /**
     * Adds a billing address
     */
    protected function setBillingAddresses()
    {
        foreach ($this->customersBilling as $customerData) {
            $customer = $this->getCustomerByAccountNumber($customerData['Kontonummer']);
            if ($customer && $customer->getId()) {
                $accountNumber = $customer->getInvoiceRecipient();
                $billingCustomer = $this->getCustomerByAccountNumber($accountNumber);
                if ($billingCustomer && $billingCustomer->getId() && $billingCustomer->getDefaultBilling()) {
                    $billingAddress = $this->addressRegistry->retrieve($billingCustomer->getDefaultBilling());
                    $newBillingAddress = $this->getAddressFromCustomer($customer, $billingAddress);
                    if (!$newBillingAddress->getId()) {
                        $this->addressResource->save($newBillingAddress);
                    }
                    $this->setDefaultBilling($customer->getId(), $newBillingAddress->getId());
                    $this->saveCustomerAttributes($customer, $billingCustomer, ['winline_customer_group']);
                }
            }
        }
    }

    private function getCustomerByAccountNumber($accountNumber)
    {
        $this->initCustomerCache();
        $result = null;
        if (isset($this->customerAccountNumberCache[$accountNumber])) {
            $email = $this->customerAccountNumberCache[$accountNumber];
            $result = $this->customerCache[$email] ?? null;
        }
        return $result;
    }

    private function setDefaultBilling($customerId, $addressId)
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('customer_entity');
        $query = "UPDATE " . $tableName . " SET default_billing = " . $addressId .
                 " WHERE entity_id = " . $customerId;
        $connection->query($query);
    }

    private function saveCustomerAttributes($customerToSave, $customerToCopy, $attributes = [])
    {
        foreach ($attributes as $attribute) {
            $customerToSave->setData($attribute, $customerToCopy->getData($attribute));
            $this->customerResource->saveAttribute($customerToSave, $attribute);
        }
    }

    protected function getEmail($item)
    {
        $email = implode("", array_slice(explode(";", $item['EMailAdresse']), 0, 1));
        return trim(str_replace($this->search, $this->replace, strtolower($email)), " \xC2\xA0\n");
    }

    protected function reIndexCustomerGrid()
    {
        $this->dataHelper->reindexIndex('customer_grid');
    }

    public function getYes($profile, $item)
    {
        return (string)__('Yes');
    }

    public function getNo($profile, $item)
    {
        return (string)__('No');
    }

}
