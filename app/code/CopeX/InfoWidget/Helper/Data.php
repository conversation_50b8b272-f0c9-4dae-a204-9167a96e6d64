<?php

declare(strict_types=1);

namespace CopeX\InfoWidget\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Catalog\Model\Layer\Resolver;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\LayoutInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;

class Data extends AbstractHelper
{
    const EMPTY_DEFAULT_MSG = 'catalog/empty_category_message_tab/empty_category_message';

    private LayoutInterface $layout;

    private FilterProvider $filterProvider;

    private Resolver $layerResolver;

    private CategoryRepositoryInterface $categoryRepository;

    private Context $context;

    private $currentCategory = null;

    public function __construct(
        CategoryRepositoryInterface $categoryRepository,
        Resolver $layerResolver,
        CategoryFactory $categoryFactory,
        LayoutInterface $layout,
        FilterProvider $filterProvider,
        Context $context
    ) {
        $this->categoryRepository = $categoryRepository;
        $this->layerResolver = $layerResolver;
        $this->categoryFactory = $categoryFactory;
        $this->layout = $layout;
        $this->filterProvider = $filterProvider;
        parent::__construct($context);
    }

    public function contentRenderer()
    {
        $html = false;
        $currentCategoryId = $this->layerResolver->get()->getCurrentCategory()->getId();
        if ($currentCategoryId) {
            $content = trim((string)(string)$this->getCategoryExtensionAttribute($currentCategoryId));
            if ($content && $content != "") {
                try {
                    $html = $this->filterProvider->getBlockFilter()->filter($content);
                } catch (\Exception $e) {
                    return false;
                }
            }
        }

        return $html;
    }

    public function getCategoryExtensionAttribute($categoryId): ?string
    {
        try {
            $category = $this->categoryRepository->get($categoryId);
        } catch (NoSuchEntityException $e) {
            return null;
        }
        $extensionAttributes = $category->getExtensionAttributes();
        if ($extensionAttributes) {
            return $extensionAttributes->getEmptyCategoryMessage();
        }

        return null;
    }

    public function getEmptyDefaultMsg()
    {
        return $this->scopeConfig->getValue(self::EMPTY_DEFAULT_MSG);
    }
}
