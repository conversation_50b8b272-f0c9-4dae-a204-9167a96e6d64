<?php

namespace CopeX\ProductViewBreadcrumbs\Service\Breadcrumb;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;

class FirstCategoryFinder implements BreadcrumbCategoryFinderInterface
{
    private CollectionFactory $categoryCollectionFactory;

    private StoreManagerInterface $storeManager;

    private LoggerInterface $logger;

    public function __construct(
        CollectionFactory $categoryCollectionFactory,
        StoreManagerInterface $storeManager,
        LoggerInterface $logger
    ) {
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        $this->storeManager = $storeManager;
        $this->logger = $logger;
    }

    /**
     * Finds first category in product in correct store
     */
    public function getCategory(ProductInterface $product): Category | null
    {
        $productCategories = $product->getAvailableInCategories();

        if (! $productCategories || ! is_array($productCategories)) {
            return null;
        }

        return $this->getFirstCategoryForStore($productCategories, $product->getStoreId());
    }

    /**
     * @param $categoryIds
     * @param $storeId
     */
    private function getFirstCategoryForStore($categoryIds, $storeId): Category
    {
        $collection = $this->categoryCollectionFactory->create();

        try {
            $rootCategoryId = $this->storeManager->getStore($storeId)->getRootCategoryId();
            $collection
                ->addAttributeToSelect('*')
                ->addIsActiveFilter()
                ->addFieldToFilter('entity_id', $categoryIds)
                ->addFieldToFilter('path', ['like' => '%/' . $rootCategoryId . '/%'])
                ->addAttributeToFilter('include_in_menu', ['eq' => 1])
                ->addAttributeToSort('level', 'DESC');
        } catch (\Exception $e) {
            $this->logger->notice($e->getMessage());
        }

        return $collection->getFirstItem();
    }
}
