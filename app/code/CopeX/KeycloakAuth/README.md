# Keycloak Authentication for Magento 2

This module provides integration between Magento 2 and Keycloak for customer authentication.

## Overview

The CopeX KeycloakAuth module allows Magento 2 to authenticate users via Keycloak tokens. When a user authenticates with Keycloak, the frontend application can pass the access token to Magento, which will:

1. Validate the token with Keycloak
2. Find or create a customer account based on the email in the token
3. Log the customer in

## Installaion

### Via Composer

```bash
composer require copex/module-keycloak-auth
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento cache:clean
```

### Manual Installation

1. Copy the module files to `app/code/CopeX/KeycloakAuth/`
2. Run the following commands:
```bash
bin/magento setup:upgrade
bin/magento setup:di:compile
bin/magento cache:clean
```

## Configuration

1. Navigate to Stores > Configuration > Services > Keycloak
2. Configure the following settings:
   - Server URL (e.g., `https://keycloak.example.com/auth`)
   - Realm
   - Client ID
   - Client Secret

## Usage

### REST API

The module exposes a REST API endpoint for authentication:

```
POST /V1/keycloak/authenticate
```

Request body:
```json
{
  "token": "eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiw..."
}
```

Response:
```json
{
  "success": true,
  "message": "Authentication successful",
  "customer_id": 123,
  "api_token": "asdf1234qwer5678zxcv..."
}
```

### JavaScript Example

```javascript
// After obtaining a token from Keycloak
const keycloakToken = keycloak.token;

// Send to Magento
fetch('/rest/V1/keycloak/authenticate', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    token: keycloakToken
  })
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    // User is authenticated, reload page or redirect
    window.location.reload();
  } else {
    // Handle error
    console.error('Authentication failed:', data.message);
  }
});
```

## API Token for REST Communication

When authenticating with Keycloak, the module also generates a Magento API token for the customer. This token can be used for subsequent REST API calls to Magento endpoints that require customer authentication.

### Response with API Token

```json
{
  "success": true,
  "message": "Authentication successful",
  "customer_id": 123,
  "api_token": "asdf1234qwer5678zxcv..."
}
```

### Using the API Token

After receiving the token, you can use it for authenticated REST API requests:

```javascript
// After successful Keycloak authentication
const magentoApiToken = response.api_token;

// Use the token for subsequent API calls
fetch('/rest/V1/customers/me', {
  method: 'GET',
  headers: {
    'Authorization': 'Bearer ' + magentoApiToken,
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(customerData => {
  console.log('Customer data:', customerData);
});
```

This allows your frontend application to make authenticated requests to Magento's
## How It Works

1. The frontend application authenticates the user with Keycloak and obtains an access token
2. The token is sent to Magento's REST API endpoint
3. Magento validates the token with Keycloak's introspection endpoint
4. If valid, Magento extracts user information (email, name) from the token
5. Magento finds or creates a customer account based on the email
6. The customer is logged in and the session is created
7. The API returns success with the customer ID

## Requirements

- Magento 2.3.x or higher
- PHP 7.2 or higher
- Keycloak server (tested with version 15.0+)

## Troubleshooting

- Ensure the Keycloak client has the correct permissions and client secret
- Check that the token contains the user's email address
- Verify network connectivity between Magento and the Keycloak server
- Check Magento logs for detailed error messages

## License

This module is licensed under the MIT License - see the [LICENSE.md](LICENSE.md) file for details.

Copyright (c) 2023 CopeX

## Author

Roman Hutterer
[CopeX E-Commerce Solutions](https://copex.io)
