<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\DashboardSales\Observer\Backend\Sales;

class PrepareAmountExpression implements \Magento\Framework\Event\ObserverInterface
{

    /**
     * Execute observer
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(
        \Magento\Framework\Event\Observer $observer
    ) {
        $expressionTransferObject = $observer->getExpressionObject();
        $arguments = $expressionTransferObject->getArguments();
        $arguments = array_map(function ($item) {
            return new \Zend_Db_Expr(str_replace(["total_invoiced","_invoiced"], ["grand_total","_amount"], $item->__toString()));
        }, $arguments);
        $expressionTransferObject->setArguments($arguments);
    }
}

