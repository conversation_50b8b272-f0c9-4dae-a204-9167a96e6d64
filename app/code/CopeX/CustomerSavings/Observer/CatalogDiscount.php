<?php

declare(strict_types=1);

namespace CopeX\CustomerSavings\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Psr\Log\LoggerInterface;

class CatalogDiscount implements ObserverInterface
{
    protected LoggerInterface $logger;

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    public function execute(Observer $observer): void
    {
        try {
            $discountValue = 0;
            $order = $observer->getEvent()->getOrder();
            $orderItems = $order->getItems();
            foreach ($orderItems as $item) {
                $regularPrice = (float) $item['original_price'];
                $finalPrice = (float) $item['price_incl_tax'];
                if ($finalPrice < $regularPrice) {
                    $discount = $regularPrice - $finalPrice;
                    $discountValue += $discount;
                }
            }
            if ($discountValue > 0) {
                $order->setCatalogPriceRuleDiscount($discountValue);
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
