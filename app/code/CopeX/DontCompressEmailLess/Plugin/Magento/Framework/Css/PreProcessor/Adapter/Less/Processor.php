<?php

namespace CopeX\DontCompressEmailLess\Plugin\Magento\Framework\Css\PreProcessor\Adapter\Less;

use Magento\Framework\App\State;
use Magento\Framework\Css\PreProcessor\File\Temporary;
use Magento\Framework\Phrase;
use Magento\Framework\View\Asset\ContentProcessorException;
use Magento\Framework\View\Asset\File;
use Magento\Framework\View\Asset\Source;
use Psr\Log\LoggerInterface;

class Processor
{

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var Source
     */
    private $assetSource;

    /**
     * @var Temporary
     */
    private $temporaryFile;

    /**
     * Constructor
     * @param LoggerInterface $logger
     * @param State           $appState
     * @param Source          $assetSource
     * @param Temporary       $temporaryFile
     */
    public function __construct(
        LoggerInterface $logger,
        Source $assetSource,
        Temporary $temporaryFile
    ) {
        $this->logger = $logger;
        $this->assetSource = $assetSource;
        $this->temporaryFile = $temporaryFile;
    }

    /**
     * @param \Magento\Framework\Css\PreProcessor\Adapter\Less\Processor $subject
     * @param \Closure                                                   $proceed
     * @param File                                                       $asset
     * @return mixed|string
     * @throws ContentProcessorException
     */
    public function aroundProcessContent(
        \Magento\Framework\Css\PreProcessor\Adapter\Less\Processor $subject,
        \Closure $proceed,
        File $asset
    ) {
        $path = $asset->getPath();
        if (strpos($path, "email.less") > 0) {
            try {
                $parser = new \Less_Parser(
                    [
                        'relativeUrls' => false,
                        'compress'     => false,
                    ]
                );

                $content = $this->assetSource->getContent($asset);

                if (trim($content) === '') {
                    return '';
                }

                $tmpFilePath = $this->temporaryFile->createFile($path, $content);

                gc_disable();
                $parser->parseFile($tmpFilePath, '');
                $content = $parser->getCss();
                gc_enable();

                if (trim($content) === '') {
                    $this->logger->warning('Parsed less file is empty: ' . $path);
                    return '';
                } else {
                    return $content;
                }
            } catch (\Exception $e) {
                throw new ContentProcessorException(new Phrase($e->getMessage()));
            }
        } else {
            //Your plugin code
            $result = $proceed($asset);
            return $result;
        }
    }
}
