<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="MageB2B\Staff\Observer\EavCollectionAbstractLoadBefore">
        <plugin disabled="false" name="CopeX_B2BExtend_Plugin_MageB2B_Staff_Observer_EavCollectionAbstractLoadBefore"
                sortOrder="10" type="CopeX\B2BExtend\Plugin\MageB2B\Staff\Observer\EavCollectionAbstractLoadBefore"/>
    </type>


    <type name="CopeX\ForceBillingAddress\Observer\Checkout\SubmitBefore">
        <plugin disabled="false"
                name="CopeX_B2BExtend_Plugin_CopeX_ForceBillingAddress_Observer_Checkout_SubmitBefore"
                sortOrder="10"
                type="CopeX\B2BExtend\Plugin\CopeX\ForceBillingAddress\Observer\Checkout\SubmitBefore"/>
    </type>

    <type name="Magento\Sales\Block\Order\History">
        <plugin disabled="false" name="CopeX_B2BExtend_Plugin_Magento_Sales_Block_Order_History"
                sortOrder="10" type="CopeX\B2BExtend\Plugin\Magento\Sales\Block\Order\History"/>
    </type>


    <type name="CopeX\Winline\Observer\Checkout\SubmitBefore">
        <plugin disabled="false"
                name="CopeX_B2BExtend_Plugin_CopeX_Winline_Checkout_BeforeSubmit"
                sortOrder="10"
                type="CopeX\B2BExtend\Plugin\CopeX\Winline\SubmitBefore"/>
    </type>
</config>