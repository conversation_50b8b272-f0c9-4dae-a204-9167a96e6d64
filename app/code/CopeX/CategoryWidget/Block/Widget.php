<?php

declare(strict_types=1);

namespace CopeX\CategoryWidget\Block;

use Magento\Catalog\Helper\Image;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Framework\View\Element\Template;
use Magento\Widget\Block\BlockInterface;

class Widget extends Template implements BlockInterface
{
    protected $_template = 'CopeX_CategoryWidget::widget.phtml';

    protected CategoryFactory $categoryFactory;
    private Image $imageHelper;

    /**
     * Widget constructor.
     */
    public function __construct(
        Template\Context $context,
        CategoryFactory $categoryFactory,
        Image $imageHelper,
        array $data = []
    ) {
        $this->categoryFactory = $categoryFactory;
        $this->imageHelper = $imageHelper;
        parent::__construct($context, $data);
    }

    public function getCategoryList(): array
    {
        $categoryIds = explode(',', $this->getData('category_ids'));
        $categories = [];
        foreach ($categoryIds as $categoryId) {
            $category = $this->categoryFactory->create()->load($categoryId);
            if ($category->getId()) {
                $categories[] = $category;
            }
        }

        return $categories;
    }

    public function getCategorySlideImage($item): string
    {
        $categoryImageUrl = $item->getImageUrl();
        if (! $categoryImageUrl || $categoryImageUrl === '') {
            $resultUrl = $this->imageHelper->getDefaultPlaceholderUrl('image');
        } else {
            $resultUrl = $categoryImageUrl;
        }

        return $resultUrl;
    }
}
