<?php

declare(strict_types=1);

namespace CopeX\OptionFeatures\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product\Option\Value;
use Magento\Catalog\Pricing\Price\CalculateCustomOptionCatalogRule;
use Magento\Framework\DataObject;
use Magento\Framework\Event\ManagerInterface;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\ObjectManagerInterface;
use MageWorx\OptionBase\Helper\Data as BaseHelper;
use MageWorx\OptionBase\Helper\Price as BasePriceHelper;
use MageWorx\OptionFeatures\Model\Price as BasePrice;
use MageWorx\OptionFeatures\Model\ResourceModel\BundleSelected;

class Price
{
    protected ProductRepositoryInterface $productRepository;

    protected DataObject $specialPriceModel;

    protected DataObject $tierPriceModel;

    protected BaseHelper $baseHelper;

    protected BasePriceHelper $basePriceHelper;

    protected ManagerInterface $eventManager;

    protected BundleSelected $bundleSelected;

    protected ObjectManagerInterface $objectManager;

    public function __construct(
        ProductRepositoryInterface $productRepository,
        DataObject $specialPriceModel,
        DataObject $tierPriceModel,
        ManagerInterface $eventManager,
        BaseHelper $baseHelper,
        BasePriceHelper $basePriceHelper,
        ObjectManagerInterface $objectManager,
        BundleSelected $bundleSelected
    ) {
        $this->productRepository = $productRepository;
        $this->specialPriceModel = $specialPriceModel;
        $this->tierPriceModel = $tierPriceModel;
        $this->baseHelper = $baseHelper;
        $this->eventManager = $eventManager;
        $this->basePriceHelper = $basePriceHelper;
        $this->objectManager = $objectManager;
        $this->bundleSelected = $bundleSelected;
    }

    public function aroundGetPrice(BasePrice $subject, \Closure $proceed, $option, $value)
    {
        if (! $this->isValidPriceModels()) {
            return $this->getValueCost($value);
        }
        $originalProduct = $option->getProduct();
        $infoBuyRequest = $this->baseHelper->getInfoBuyRequest($originalProduct);

        if (! $this->isOriginalValueValid($originalProduct, $value)) {
            return $this->getValueCost($value);
        }
        $valueQty = $this->getValueQty($option, $value, $infoBuyRequest);
        $productQty = $this->getProductQuantity($infoBuyRequest);
        $originalValue = $this->getOriginalValue($originalProduct, $value);
        $price = $this->calculatePrice($option, $originalProduct, $originalValue, $valueQty, $productQty);

        return $this->applyCatalogRule($option, $price, $originalValue);
    }

    private function isValidPriceModels(): bool
    {
        return $this->specialPriceModel instanceof AbstractModel &&
            $this->tierPriceModel instanceof AbstractModel;
    }

    private function isOriginalValueValid($originalProduct, $value)
    {
        return $this->getOriginalValue($originalProduct, $value);
    }

    private function getValueCost($value)
    {
        return $value->getCost() ?: 0;
    }

    private function getProductQuantity($infoBuyRequest)
    {
        return ! empty($infoBuyRequest['qty']) ? $infoBuyRequest['qty'] : 1;
    }

    private function getOriginalValue($originalProduct, $value)
    {
        $originalProductOptions = $originalProduct->getData('options');
        foreach ($originalProductOptions as $originalProductOption) {
            $values = $originalProductOption->getValues();
            if (! empty($values[$value->getOptionTypeId()])) {
                return $values[$value->getOptionTypeId()];
            }
        }

        return null;
    }

    private function calculatePrice($option, $originalProduct, $originalValue, $valueQty, $productQty)
    {
        $specialPrice = $this->specialPriceModel->getActualSpecialPrice($originalValue);
        $tierPrices = $this->tierPriceModel->getSuitableTierPrices($originalValue);
        $totalQty = $option->getData('one_time') ? $valueQty : $productQty * $valueQty;
        $suitableTierPrice = $this->getSuitableTierPrice($tierPrices, $totalQty);
        if ($suitableTierPrice && ($suitableTierPrice < $specialPrice || $specialPrice === null)) {
            return $suitableTierPrice;
        }
        if ($specialPrice !== null) {
            return $specialPrice;
        }

        return $this->calculateDefaultPrice($originalProduct, $originalValue, $totalQty);
    }

    private function getSuitableTierPrice($tierPrices, $totalQty)
    {
        if (isset($tierPrices[$totalQty])) {
            return $tierPrices[$totalQty]['price'] ?? null;
        }
        $suitableTierPrice = null;
        $suitableTierPriceQty = null;
        foreach ($tierPrices as $tierPriceItemQty => $tierPriceItem) {
            if ($suitableTierPriceQty < $tierPriceItemQty && $totalQty >= $tierPriceItemQty) {
                $suitableTierPrice = $tierPriceItem['price'] ?? null;
                $suitableTierPriceQty = $tierPriceItemQty;
            }
        }

        return $suitableTierPrice;
    }

    private function calculateDefaultPrice($originalProduct, $originalValue, $totalQty)
    {
        if ($originalValue->getPriceType() === 'percent') {
            $productFinalPrice = $originalProduct->getPriceModel()->getBasePrice($originalProduct, $totalQty);
            $originalProduct->setFinalPrice($productFinalPrice);
            $this->eventManager->dispatch(
                'catalog_product_get_final_price',
                ['product' => $originalProduct, 'qty' => $totalQty]
            );
            $productFinalPrice = $originalProduct->getData('final_price');
            return $productFinalPrice * ($originalValue->getCost() ?: 0) / 100;
        }

        return $originalValue->getCost() ?: 0;
    }

    private function applyCatalogRule($option, $price, $originalValue)
    {
        if ($originalValue->getPriceType() !== Value::TYPE_PERCENT &&
            $this->baseHelper->checkModuleVersion('104.0.2-p1', '', '>=', '<', 'Magento_Catalog')) {
            $calculateCustomOptionCatalogRule = $this->objectManager->get(CalculateCustomOptionCatalogRule::class);
            $catalogPriceValue = $calculateCustomOptionCatalogRule->execute($option->getProduct(), (float) $price, false);

            return $catalogPriceValue ? $catalogPriceValue : $price;
        }

        return $price;
    }
}
