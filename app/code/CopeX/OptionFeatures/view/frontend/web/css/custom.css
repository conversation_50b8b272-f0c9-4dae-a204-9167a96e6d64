
.main-option-img {
    cursor: pointer;
    transition: 0.3s;
    display: block;
    margin-left: auto;
    margin-right: auto
}

.main-option-img:hover {opacity: 0.7;}

.modal-img-container {
    display: none;
    position: fixed;
    z-index: 50;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: #FFFFFF;
}

.modal-content-img {
    margin-right: auto;
    margin-left: auto;
    margin-bottom: auto;
    margin-top: 100px;
    padding: 20px;
    display: block;
    -webkit-animation-name: zoom;
    -webkit-animation-duration: 0.6s;
    animation-name: zoom;
    animation-duration: 0.6s;
}

.modal-content-close-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 40px;
    cursor: pointer;
}

.out {
    animation-name: zoom-out;
    animation-duration: 0.6s;
}

@-webkit-keyframes zoom {
    from {-webkit-transform:scale(1)}
    to {-webkit-transform:scale(2)}
}

@keyframes zoom {
    from {transform:scale(0.4)}
    to {transform:scale(1)}
}

@keyframes zoom-out {
    from {transform:scale(1)}
    to {transform:scale(0)}
}


@media only screen and (min-width: 768px){
    .modal-content-img {
        height: 85vh;
    }
}
