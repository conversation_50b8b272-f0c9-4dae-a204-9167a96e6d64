<?xml version="1.0"?>
<!--
/**
 * Copyright © 2018 MageWorx. All rights reserved.
 * See LICENSE.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="mageworx" sortOrder="2001">
            <label>MageWorx</label>
        </tab>
        <section id="mageworx_apo" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <label><![CDATA[Advanced Product Options]]></label>
            <tab>mageworx</tab>
            <resource>MageWorx_OptionFeatures::config_optionfeatures</resource>
            <group id="optionfeatures" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label><![CDATA[Option Features]]></label>
                <field id="use_min_weight" translate="label" type="select" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label><![CDATA[Enable Min-Weight]]></label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
