<?php

declare(strict_types=1);

namespace CopeX\OptionFeatures\Helper;

use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\State;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Driver\File as FileDriver;
use Magento\Framework\Image\Factory as ImageFactory;
use Magento\Store\Model\ScopeInterface;
use MageWorx\OptionFeatures\Helper\Data as ParentHelper;
use MageWorx\OptionFeatures\Model\Image as ImageModel;
use MageWorx\OptionFeatures\Model\Product\Option\Value\Media\Config;

class Data extends ParentHelper
{
    // Option value attributes
    public const KEY_MIN_WEIGHT = 'min_weight';

    // Value map
    public const MIN_WEIGHT_TRUE = '1';
    public const MIN_WEIGHT_FALSE = '0';

    // Config
    public const XML_PATH_USE_MIN_WEIGHT = 'mageworx_apo/optionfeatures/use_min_weight';

    /**
     * @var array<string>
     */
    protected array $imageAttributes = [
        self::IMAGE_MEDIA_ATTRIBUTE_BASE_IMAGE => 'Base',
        self::IMAGE_MEDIA_ATTRIBUTE_TOOLTIP_IMAGE => 'Tooltip',
        ImageModel::COLUMN_REPLACE_MAIN_GALLERY_IMAGE => 'Replace Main Gallery Image',
        ImageModel::COLUMN_OVERLAY_IMAGE => 'Overlay',
    ];

    protected Config $mediaConfig;

    protected ImageFactory $imageFactory;

    protected Filesystem $filesystem;

    protected FileDriver $fileDriver;

    protected State $state;

    /**
     * Data constructor.
     */
    public function __construct(
        Context $context,
        Config $mediaConfig,
        ImageFactory $imageFactory,
        Filesystem $filesystem,
        FileDriver $fileDriver,
        State $state
    ) {
        parent::__construct($context, $mediaConfig, $imageFactory, $filesystem, $fileDriver, $state);
        $this->mediaConfig = $mediaConfig;
        $this->imageFactory = $imageFactory;
        $this->filesystem = $filesystem;
        $this->fileDriver = $fileDriver;
        $this->state = $state;
    }

    /**
     * @param null $storeId
     */
    public function isMinWeightEnabled($storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_USE_MIN_WEIGHT,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
