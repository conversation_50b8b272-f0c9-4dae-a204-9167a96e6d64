<?php

namespace CopeX\OauthMailFetcher\Cron;

use Magento\Framework\Filesystem\DirectoryList;
use Magento\Framework\Serialize\SerializerInterface;

class OauthMailFetcher
{
    /**
     * @var $saveDir string
     * for temp saving of saveDir
     */
    private string $saveDir;

    private string $_filename = "customer-import.csv";
    private const TENANT = "1e37b4b7-33c3-4934-aff7-1bcbaaebee63";
    private const CLIENT_SECRET = "****************************************";

    // aka APPLICATION_ID
    private const CLIENT_ID = "d2fb4e8c-91ab-4ef1-a1d4-c6de105a153e";
    private const USERNAME = "<EMAIL>";
    private const PASS = 'Lj9iRxoNAPZ3Dn3HWGKL';

    /**
     * @var SerializerInterface
     */
    private SerializerInterface $serializer;
    /**
     * @var DirectoryList
     */
    private DirectoryList $dir;

    public function __construct(
        SerializerInterface $serializer,
        DirectoryList $dir
    ) {
        $this->serializer = $serializer;
        $this->dir = $dir;
    }

    public function execute()
    {
        $this->saveDir = $this->dir->getPath('var') . '/import/';
        $token = $this->getAccessToken();
        $client = new \GuzzleHttp\Client();
        $headers = [
            "headers" => [
                'Authorization' => 'Bearer ' . $token,
            ],
        ];
        $request = $client->request('GET', 'https://graph.microsoft.com/v1.0/me/mailFolders/inbox/messages', $headers);
        $messages = $this->serializer->unserialize($request->getBody()->getContents());
        foreach ($messages['value'] as $message) {
            $messageId = $message["id"];
            $request = $client->request('GET', 'https://graph.microsoft.com/v1.0/me/messages/' . $messageId . '/attachments', $headers);
            $attachments = $this->serializer->unserialize($request->getBody()->getContents());
            foreach ($attachments['value'] as $attachment) {
                if ($attachment['contentType'] == "application/csv") {
                    $attachmentId = $attachment['id'];
                    $request = $client->request('GET', 'https://graph.microsoft.com/v1.0/me/messages/' . $messageId . '/attachments/' . $attachmentId,
                        $headers);
                    $fileContent = $this->serializer->unserialize($request->getBody()->getContents());
                    $fileContentEncoded = base64_decode($fileContent["contentBytes"]);
                    $savepath = $this->saveDir . $this->_filename;
                    file_put_contents($savepath, $fileContentEncoded);
                }
            }
            //delete message
            $client->request('DELETE', 'https://graph.microsoft.com/v1.0/me/messages/' . $messageId, $headers);
        }
    }

    private function getAccessToken()
    {
        $url = 'https://login.microsoftonline.com/' . self::TENANT . '/oauth2/v2.0/token';
        $postInput = [
            'client_id'     => self::CLIENT_ID,
            'client_secret' => self::CLIENT_SECRET,
            'scope'         => 'https://graph.microsoft.com/.default',
            'grant_type'    => "password",
            'username'      => self::USERNAME,
            'password'      => self::PASS,
        ];
        $client = new \GuzzleHttp\Client();
        $response = $client->request('POST', $url, ['form_params' => $postInput]);
        $statusCode = $response->getStatusCode();
        $responseBody = json_decode($response->getBody(), true);
        return $responseBody["access_token"];
    }

}
