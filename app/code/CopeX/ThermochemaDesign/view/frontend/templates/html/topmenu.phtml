<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Top menu for store
 *
 * @var $block \Magento\Theme\Block\Html\Topmenu
 */
?>
<?php $columnsLimit = $block->getColumnsLimit() ?: 0; ?>
<?php $_menu = $block->getHtml('menu-item menu-item-type-custom menu-item-object-custom menu-item-129 dropdown', 'menu-item menu-item-type-post_type menu-item-object-page', $columnsLimit) ?>

<?php /* @escapeNotVerified */
echo $_menu; ?>
<?php /* @escapeNotVerified */
echo $block->getChildHtml(); ?>
