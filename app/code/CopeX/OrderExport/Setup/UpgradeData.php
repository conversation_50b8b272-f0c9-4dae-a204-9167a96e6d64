<?php

namespace CopeX\OrderExport\Setup;

use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\UpgradeDataInterface;

use Magento\Quote\Setup\QuoteSetupFactory;
use Magento\Sales\Setup\SalesSetupFactory;

class UpgradeData implements UpgradeDataInterface
{

    /**
     * @var QuoteSetupFactory
     */
    protected $quoteSetupFactory;

    /**
     * @var SalesSetupFactory
     */
    protected $salesSetupFactory;


    public function __construct(
        QuoteSetupFactory $quoteSetupFactory,
        SalesSetupFactory $salesSetupFactory
    )
    {
        $this->quoteSetupFactory = $quoteSetupFactory;
        $this->salesSetupFactory = $salesSetupFactory;
    }


    /**
     * {@inheritdoc}
     */
    public function upgrade(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    )
    {
        $setup->startSetup();
        if (version_compare($context->getVersion(), "1.1.0", "<")) {

            /** @var \Magento\Quote\Setup\QuoteSetup $quoteInstaller */
            $quoteInstaller = $this->quoteSetupFactory->create(['resourceName' => 'quote_setup', 'setup' => $setup]);

            /** @var \Magento\Sales\Setup\SalesSetup $salesInstaller */
            $salesInstaller = $this->salesSetupFactory->create(['resourceName' => 'sales_setup', 'setup' => $setup]);

            $setup->startSetup();

            //Add attributes to quote
            $entityAttributesCodes = [
                'is_addedInWinline' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER
            ];

            foreach ($entityAttributesCodes as $code => $type) {

                $quoteInstaller->addAttribute('quote', $code, ['type' => $type, 'length' => 255, 'visible' => false, 'nullable' => true, 'default' => false]);
                $salesInstaller->addAttribute('order', $code, ['type' => $type, 'length' => 255, 'visible' => false, 'nullable' => true, 'default' => false]);
                $salesInstaller->addAttribute('invoice', $code, ['type' => $type, 'length' => 255, 'visible' => false, 'nullable' => true, 'default' => false]);
            }
        }
        $setup->endSetup();

    }
}