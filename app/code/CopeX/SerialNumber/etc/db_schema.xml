<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="copex_serialnumber_serialnumber" resource="default" engine="innodb" comment="Serial Numbers Table">
		<column xsi:type="int" name="serialnumber_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Entity Id"/>
		<column name="product_sku" nullable="true" xsi:type="varchar" comment="Product SKU" length="64"/>
		<column name="serial_number" nullable="true" xsi:type="varchar" comment="Serial Number" length="255"/>
		<column name="status" nullable="true" xsi:type="varchar" comment="Status" length="255"/>
		<column name="order_item_id" nullable="true" xsi:type="int" comment="Order Item ID" unsigned="true"/>
		<column name="invoice_item_id" nullable="true" xsi:type="int" comment="Invoice Item ID" unsigned="true"/>
		<column name="start_date" nullable="true" xsi:type="datetime" comment="Start Date" />
		<column name="end_date" nullable="true" xsi:type="datetime" comment="End Date"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="serialnumber_id"/>
		</constraint>
		<constraint xsi:type="foreign" referenceId="COPEX_SERIALNUMBER_PRODUCT_SKU"
                    table="copex_serialnumber_serialnumber" column="product_sku" referenceTable="catalog_product_entity" referenceColumn="sku" onDelete="CASCADE" />
		<constraint xsi:type="foreign" referenceId="COPEX_SERIALNUMBER_ORDER_ITEM_ID"
					table="copex_serialnumber_serialnumber" column="order_item_id" referenceTable="sales_order_item" referenceColumn="item_id" onDelete="SET NULL" />
		<constraint xsi:type="foreign" referenceId="COPEX_SERIALNUMBER_INVOICE_ITEM_ID"
					table="copex_serialnumber_serialnumber" column="invoice_item_id" referenceTable="sales_invoice_item" referenceColumn="entity_id" onDelete="SET NULL" />
        <index referenceId="IDX_COPEX_SN_PRODUCT_SKU" indexType="btree">
            <column name="product_sku"/>
        </index>
        <constraint xsi:type="foreign"
                    referenceId="FK_COPEX_SN_PRODUCT_SKU__CPE_SKU"
                    table="copex_serialnumber_serialnumber" column="product_sku"
                    referenceTable="catalog_product_entity" referenceColumn="sku"
                    onDelete="CASCADE"/>
        <column name="product_id" xsi:type="int" unsigned="true" nullable="false" comment="Product ID"/>
        <index referenceId="IDX_COPEX_SN_PRODUCT_ID" indexType="btree">
            <column name="product_id"/>
        </index>
        <constraint xsi:type="foreign"
                    referenceId="FK_COPEX_SN_PRODUCT_ID__CPE_ENTITY_ID"
                    table="copex_serialnumber_serialnumber" column="product_id"
                    referenceTable="catalog_product_entity" referenceColumn="entity_id"
                    onDelete="CASCADE"/>
        <constraint xsi:type="foreign"
                    referenceId="FK_COPEX_SN_PRODUCT_SKU__CPE_SKU"
                    table="copex_serialnumber_serialnumber" column="product_sku"
                    referenceTable="catalog_product_entity" referenceColumn="sku"
                    onDelete="CASCADE"/>
	</table>
</schema>
