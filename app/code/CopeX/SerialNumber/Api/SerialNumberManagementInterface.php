<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Api;

use CopeX\SerialNumber\Model\SerialNumber;

/**
 * Interface SerialNumberManagementInterface
 *
 * @package CopeX\SerialNumber\Api
 */
interface SerialNumberManagementInterface
{
    /**
     * Assign an available serial number to an order item and mark it as inactive.
     *
     * @param string $productSku
     */
    public function assignSerialNumberToOrderItem(int|string $orderItemId): mixed;

    /**
     * Get an available serial number for a product SKU.
     */
    public function getAvailableSerialNumber(string $productSku): ?SerialNumber;

    /**
     * Get all serial numbers assigned to a product by SKU
     *
     * @param $productSku
     */
    public function getProductSerialNumbers($productSku): mixed;

    /**
     * Release a serial number from an order item and mark it as active.
     */
    public function releaseSerialNumberFromOrderItem(int $orderItemId): void;

    /**
     * @param $data
     */
    public function addSerialNumber($data): void;

    /**
     * @param $id
     */
    public function getById($id): mixed;
}
