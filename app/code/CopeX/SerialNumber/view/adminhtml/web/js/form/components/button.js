/**
 * @api
 */
define([
    'jquery',
    'uiElement',
    'uiRegistry',
    'uiLayout',
    'mageUtils',
    'underscore',
    'jquery/ui',
    'Magento_Ui/js/modal/modal'
], function ($,Element, registry, layout, utils, _, modal) {
    'use strict';

    return Element.extend({
        defaults: {
            buttonClasses: {},
            additionalClasses: {},
            displayArea: 'outsideGroup',
            displayAsLink: false,
            elementTmpl: 'ui/form/element/button',
            template: 'ui/form/components/button/simple',
            visible: true,
            disabled: false,
            title: '',
            buttonTextId: '',
            ariLabelledby: '',
            actionUrl: '',
            productId: ''
        },

        /**
         * Initializes component.
         *
         * @returns {Object} Chainable.
         */
        initialize: function () {
            return this._super()
                ._setClasses()
                ._setButtonClasses();
        },

        /** @inheritdoc */
        initObservable: function () {
            return this._super()
                .observe([
                    'visible',
                    'disabled',
                    'title',
                    'childError'
                ]);
        },

        /**
         * Performs configured actions
         */
        action: function () {
            let serialNumber = document.getElementsByName('add_product_serial_number')[0].value;
            if (serialNumber && serialNumber != '') {
                $.ajax({
                    url: this.actionUrl,
                    method: 'POST',
                    data: {
                        form_key: FORM_KEY,
                        product_id: this.productId,
                        serial_number: serialNumber
                    }
                }).done(function (data) {
                    if (data.success === false) {
                        alert(data.message);
                    }
                }).fail(function (data) {
                    alert(data.message);
                }).always(function(data) {
                    document.getElementsByName('add_product_serial_number')[0].value = '';
                    registry.get('index = product_grid_listing').source.reload({refresh: true});
                })
            }
        },

        /**
         * Apply action on target component,
         * but previously create this component from template if it is not existed
         *
         * @param {Object} action - action configuration
         */
        applyAction: function (action) {
            var targetName = action.targetName,
                params = utils.copy(action.params) || [],
                actionName = action.actionName,
                target;

            if (!registry.has(targetName)) {
                this.getFromTemplate(targetName);
            }
            target = registry.async(targetName);

            if (target && typeof target === 'function' && actionName) {
                params.unshift(actionName);
                target.apply(target, params);
            }
        },

        /**
         * Create target component from template
         *
         * @param {Object} targetName - name of component,
         * that supposed to be a template and need to be initialized
         */
        getFromTemplate: function (targetName) {
            var parentName = targetName.split('.'),
                index = parentName.pop(),
                child;

            parentName = parentName.join('.');
            child = utils.template({
                parent: parentName,
                name: index,
                nodeTemplate: targetName
            });
            layout([child]);
        },

        /**
         * Extends 'additionalClasses' object.
         *
         * @returns {Object} Chainable.
         */
        _setClasses: function () {
            if (typeof this.additionalClasses === 'string') {
                if (this.additionalClasses === '') {
                    this.additionalClasses = {};

                    return this;
                }

                this.additionalClasses = this.additionalClasses
                    .trim()
                    .split(' ')
                    .reduce(function (classes, name) {
                            classes[name] = true;

                            return classes;
                        }, {}
                    );
            }

            return this;
        },

        /**
         * Extends 'buttonClasses' object.
         *
         * @returns {Object} Chainable.
         */
        _setButtonClasses: function () {
            var additional = this.buttonClasses;

            if (_.isString(additional)) {
                this.buttonClasses = {};

                if (additional.trim().length) {
                    additional = additional.trim().split(' ');

                    additional.forEach(function (name) {
                        if (name.length) {
                            this.buttonClasses[name] = true;
                        }
                    }, this);
                }
            }

            _.extend(this.buttonClasses, {
                'action-basic': !this.displayAsLink,
                'action-additional': this.displayAsLink
            });

            return this;
        }
    });
});
