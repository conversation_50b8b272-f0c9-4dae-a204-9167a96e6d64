<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">copex_serialnumber_serialnumber_listing.copex_serialnumber_serialnumber_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>copex_serialnumber_serialnumber_columns</spinner>
		<deps>
			<dep>copex_serialnumber_serialnumber_listing.copex_serialnumber_serialnumber_listing_data_source</dep>
		</deps>
		<buttons>
			<!--<button name="add">
				<url path="*/*/new"/>
				<class>primary</class>
				<label translate="true">Add New SerialNumber</label>
			</button>-->
            <button name="import">
                <url path="*/*/importForm"/>
                <label translate="true">Import Serial Numbers</label>
            </button>
		</buttons>
	</settings>
	<dataSource name="copex_serialnumber_serialnumber_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
			<storageConfig>
				<param name="indexField" xsi:type="string">serialnumber_id</param>
			</storageConfig>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>CopeX_SerialNumber::SerialNumber</aclResource>
		<dataProvider name="copex_serialnumber_serialnumber_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>serialnumber_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
        <massaction name="listing_massaction">
            <action name="serialnumber_delete">
                <settings>
                    <url path="copex_serialnumber/SerialNumber/massDelete"/>
                    <type>serialnumber_delete</type>
                    <label translate="true">Delete</label>
                </settings>
            </action>
        </massaction>
	</listingToolbar>
	<columns name="copex_serialnumber_serialnumber_columns">
		<settings>
			<editorConfig>
				<param name="selectProvider" xsi:type="string">copex_serialnumber_serialnumber_listing.copex_serialnumber_serialnumber_listing.copex_serialnumber_serialnumber_columns.ids</param>
				<param name="enabled" xsi:type="boolean">true</param>
				<param name="indexField" xsi:type="string">serialnumber_id</param>
				<param name="clientConfig" xsi:type="array">
					<item name="saveUrl" xsi:type="url" path="copex_serialnumber/SerialNumber/inlineEdit"/>
					<item name="validateBeforeSave" xsi:type="boolean">false</item>
				</param>
			</editorConfig>
			<childDefaults>
				<param name="fieldAction" xsi:type="array">
					<item name="provider" xsi:type="string">copex_serialnumber_serialnumber_listing.copex_serialnumber_serialnumber_listing.copex_serialnumber_serialnumber_columns_editor</item>
					<item name="target" xsi:type="string">startEdit</item>
					<item name="params" xsi:type="array">
						<item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
						<item name="1" xsi:type="boolean">true</item>
					</item>
				</param>
			</childDefaults>
		</settings>
		<selectionsColumn name="ids">
			<settings>
				<indexField>serialnumber_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="serialnumber_id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="product_sku">
			<settings>
				<filter>text</filter>
				<label translate="true">SKU</label>
			</settings>
		</column>
		<column name="serial_number">
			<settings>
				<filter>text</filter>
				<label translate="true">Serial Number</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="status">
			<settings>
				<filter>text</filter>
				<label translate="true">Status</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="start_date">
			<settings>
				<filter>text</filter>
				<label translate="true">Start Date</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="end_date">
			<settings>
				<filter>text</filter>
				<label translate="true">End Date</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="order_item_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Order Item ID</label>
			</settings>
		</column>
		<actionsColumn name="actions" class="CopeX\SerialNumber\Ui\Component\Listing\Column\SerialNumberActions">
			<settings>
				<indexField>serialnumber_id</indexField>
				<resizeEnabled>false</resizeEnabled>
				<resizeDefaultWidth>107</resizeDefaultWidth>
			</settings>
		</actionsColumn>
	</columns>
</listing>
