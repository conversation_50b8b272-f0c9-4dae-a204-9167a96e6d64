<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Model;

use CopeX\SerialNumber\Api\Data\SerialNumberInterface;
use CopeX\SerialNumber\Api\SerialNumberManagementInterface;
use CopeX\SerialNumber\Api\SerialNumberRepositoryInterface;
use CopeX\SerialNumber\Exception\NoSerialNumberLeftException;
use CopeX\SerialNumber\Helper\Config;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Sales\Api\InvoiceItemRepositoryInterface;
use Magento\Sales\Api\OrderItemRepositoryInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;
use Psr\Log\LogLevel;

/**
 * Class SerialNumberManagement
 *
 * @package CopeX\SerialNumber\Model
 */
class SerialNumberManagement implements SerialNumberManagementInterface
{
    public TimezoneInterface $timezone;
    public FilterBuilder $filterBuilder;
    public FilterGroupBuilder $filterGroupBuilder;
    protected SerialNumberRepositoryInterface $serialNumberRepository;

    protected SerialNumberFactory $serialNumberFactory;

    protected SearchCriteriaBuilder $searchCriteriaBuilder;

    protected OrderRepositoryInterface $orderRepository;

    protected LoggerInterface $logger;
    private OrderItemRepositoryInterface $orderItemRepository;
    private InvoiceItemRepositoryInterface $invoiceItemRepository;
    private Config $config;

    /**
     * SerialNumberManagement constructor.
     */
    public function __construct(
        SerialNumberRepositoryInterface $serialNumberRepository,
        SerialNumberFactory $serialNumberFactory,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        OrderRepositoryInterface $orderRepository,
        TimezoneInterface $timezone,
        OrderItemRepositoryInterface $orderItemRepository,
        InvoiceItemRepositoryInterface $invoiceItemRepository,
        Config $config,
        LoggerInterface $logger
    ) {
        $this->serialNumberRepository = $serialNumberRepository;
        $this->serialNumberFactory = $serialNumberFactory;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->orderRepository = $orderRepository;
        $this->timezone = $timezone;
        $this->orderItemRepository = $orderItemRepository;
        $this->invoiceItemRepository = $invoiceItemRepository;
        $this->config = $config;
        $this->logger = $logger;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
    }

    /**
     * @inheirtDoc
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function assignSerialNumberToOrderItem(int|string $orderItemId): mixed
    {
        $assignedSerialNumbers = [];
        $item = $this->orderItemRepository->get($orderItemId);
        $productSku = $item->getProduct()->getSku() ?? $item->getSku();
        $itemQty = $item->getQtyOrdered();
        $currentSerialNumbers = $this->getAssignedSerialNumber($orderItemId);
        $serialNumbersToAssign = $itemQty - count($currentSerialNumbers);
        for ($i = 0; $i < $serialNumbersToAssign; $i++) {
            $serialNumber = $this->getAvailableSerialNumber($productSku);
            if ($serialNumber) {
                $serialNumber->setOrderItemId($orderItemId);
                $this->serialNumberRepository->save($serialNumber);
                $this->logger->log(
                    LogLevel::DEBUG,
                    'debug_serial_number',
                    ['order_item_id' => $orderItemId, 'serial_number' => $serialNumber->getId()]
                );
                $assignedSerialNumbers[] = $serialNumber;
            }
        }

        return $assignedSerialNumbers;
    }

    public function assignSerialNumberToInvoiceItem(int|string $invoiceItemId): void
    {
        $item = $this->invoiceItemRepository->get($invoiceItemId);
        $assignedNumbers = $this->assignSerialNumberToOrderItem($item->getOrderItemId());
        foreach ($assignedNumbers as $assignedNumber) {
            $assignedNumber->setInvoiceItemId($invoiceItemId);
            $this->serialNumberRepository->save($assignedNumber);
        }
    }

    /**
     * @inheirtDoc
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getProductSerialNumbers($productSku): mixed
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('product_sku', $productSku)
            ->create();

        return $this->serialNumberRepository->getList($searchCriteria);
    }

    /**
     * @inheritDoc
     */
    public function getById($id): mixed
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('serialnumber_id', $id)
            ->create();

        return current($this->serialNumberRepository->getList($searchCriteria)->getItems() ?? []);
    }

    /**
     * @param $productSku
     * @param $newSerialNumber
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function checkProductSerialNumberAvailability($productSku, $newSerialNumber): bool
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('product_sku', $productSku)
            ->addFilter('serial_number', $newSerialNumber)
            ->create();
        $serialNumbers = $this->serialNumberRepository->getList($searchCriteria)->getItems();
        if (! empty($serialNumbers)) {
            foreach ($serialNumbers as $serialNumber) {
                if ($serialNumber->getSerialNumber() === $newSerialNumber) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws NoSerialNumberLeftException
     */
    public function getAvailableSerialNumber(string $productSku): ?SerialNumber
    {
        $serialNumbers = $this->getAvailableSerialNumbers($productSku);
        if (! empty($serialNumbers)) {
            return array_shift($serialNumbers);
        }
        $this->noSerialNumberAvailable($productSku);

        return null;
    }

    public function noSerialNumberAvailable(string $productSku): void
    {
        $message = __('No serial number left for SKU: %1', $productSku);
        if ($this->config->shouldLogOnError()) {
            $this->logger->error($message);
        }
        if ($this->config->shouldThrowExceptionOnError()) {
            throw new NoSerialNumberLeftException($message);
        }
    }

    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getAvailableSerialNumbers(string $productSku): array
    {
        $currentDate = $this->timezone->date()->format('Y-m-d H:i:s');

        $startDateFilter = $this->filterBuilder->setField('start_date')
            ->setValue($currentDate)
            ->setConditionType('lteq')->create();
        $startDateFilterNull = $this->filterBuilder->setField('start_date')
            ->setConditionType('null')->create();
        $endDateFilter = $this->filterBuilder->setField('end_date')
            ->setValue($currentDate)
            ->setConditionType('gteq')->create();
        $endDateFilterNull = $this->filterBuilder->setField('end_date')
            ->setConditionType('null')->create();
        $startFilterGroup = $this->filterGroupBuilder->addFilter($startDateFilter)
            ->addFilter($startDateFilterNull)->create();
        $endFilterGroup = $this->filterGroupBuilder->addFilter($endDateFilter)
            ->addFilter($endDateFilterNull)->create();
        $searchCriteria = $this->searchCriteriaBuilder
            ->setFilterGroups([$startFilterGroup, $endFilterGroup])
            ->addFilter('product_sku', $productSku)
            ->addFilter('status', SerialNumberInterface::ACTIVE)
            ->addFilter('order_item_id', null, 'null')
            ->create();
        return $this->serialNumberRepository->getList($searchCriteria)->getItems();
    }

    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function releaseSerialNumberFromOrderItem(int|string $orderItemId): void
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('order_item_id', $orderItemId)
            ->create();
        $serialNumbers = $this->serialNumberRepository->getList($searchCriteria)->getItems();
        foreach ($serialNumbers as $serialNumber) {
            $serialNumber->setOrderItemId(null)->setInvoiceItemId(null)->setStatus(SerialNumberInterface::ACTIVE);
            $this->serialNumberRepository->save($serialNumber);
        }
    }

    /**
     * @param $sku
     * @param $serialNumber
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function addSerialNumber($data): void
    {
        $serialNumberModel = $this->serialNumberFactory->create();
        $serialNumberModel->setData($data);
        $this->serialNumberRepository->save($serialNumberModel);
    }

    /**
     * @param $incrementId
     */
    public function getSerialDataByOrderIncrementId($incrementId): array
    {
        $resArr = [];
        $searchCriteria = $this->searchCriteriaBuilder->addFilter('increment_id', $incrementId)->create();
        $order = current($this->orderRepository->getList($searchCriteria)->getItems());
        if ($order) {
            $orderItems = $order->getItems();
            foreach ($orderItems as $item) {
                $itemName = $item->getName();
                $itemId = $item->getItemId();
                $serialNumber = $this->getAssignedSerialNumber($itemId);
                if ($serialNumber) {
                    array_push($resArr, [$itemName => $serialNumber]);
                }
            }
        }

        return $resArr;
    }

    /**
     * @param $itemId
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getAssignedSerialNumber($itemId): array
    {
        $serialNumbersArr = [];
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('order_item_id', $itemId)
            ->create();
        $serialNumbers = $this->serialNumberRepository->getList($searchCriteria)->getItems();
        foreach ($serialNumbers as $serialNumber) {
            array_push($serialNumbersArr, $serialNumber->getSerialNumber());
        }

        return $serialNumbersArr;
    }
}
