<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Helper;

use CopeX\SerialNumber\Model\SerialNumberManagement;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;

/**
 * Class Data
 *
 * @package CopeX\SerialNumber\Helper
 */
class Data extends AbstractHelper
{
    protected SerialNumberManagement $serialNumberManagement;

    /**
     * Data constructor.
     */
    public function __construct(
        Context $context,
        SerialNumberManagement $serialNumberManagement
    ) {
        parent::__construct($context);
        $this->serialNumberManagement = $serialNumberManagement;
    }

    /**
     * @param $itemId
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getSerialNumber($itemId): array
    {
        return $this->serialNumberManagement->getAssignedSerialNumber($itemId);
    }
}
