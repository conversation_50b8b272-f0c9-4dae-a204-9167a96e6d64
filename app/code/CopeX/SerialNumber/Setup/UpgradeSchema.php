<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Setup;

use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\UpgradeSchemaInterface;

class UpgradeSchema implements UpgradeSchemaInterface
{
    /** @var string */
    private const TARGET_TABLE = 'copex_serialnumber_serialnumber';

    /** @var string */
    private const TARGET_FK = 'COPEX_SERIALNUMBER_SERIALNUMBER_PRD_SKU_CAT_PRD_ENTT_SKU';

    /**
     * {@inheritdoc}
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context): void
    {
        $setup->startSetup();
        if ($context->getVersion() === null || version_compare($context->getVersion(), '1.0.1', '<')) {
            $this->dropDuplicateForeignKeyIfExists($setup);
        }
        $setup->endSetup();
    }

    /**
     * Drop the duplicate/conflicting FK if it exists on the target table.
     */
    private function dropDuplicateForeignKeyIfExists(SchemaSetupInterface $setup): void
    {
        $connection = $setup->getConnection();
        $tableName  = $setup->getTable(self::TARGET_TABLE);
        if (!$connection->isTableExists($tableName)) {
            return;
        }
        $foreignKeys = $connection->getForeignKeys($tableName);
        foreach ($foreignKeys as $fk) {
            $fkName = $fk['FK_NAME'] ?? ($fk['CONSTRAINT_NAME'] ?? null);
            if ($fkName === self::TARGET_FK) {
                $connection->dropForeignKey($tableName, $fkName);
                break;
            }
        }
    }
}
