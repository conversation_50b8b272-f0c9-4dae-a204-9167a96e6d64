<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Controller\Adminhtml\Product;

use CopeX\SerialNumber\Api\Data\SerialNumberInterface;
use CopeX\SerialNumber\Controller\Adminhtml\SerialNumber;
use CopeX\SerialNumber\Model\SerialNumberFactory;
use CopeX\SerialNumber\Model\SerialNumberManagement;
use Magento\Backend\App\Action\Context;
use Magento\Catalog\Model\ProductRepository;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Registry;
use Magento\Framework\View\Result\PageFactory;

/**
 * Class AddSerialNumber
 *
 * @package CopeX\SerialNumber\Controller\Adminhtml\Product
 */
class AddSerialNumber extends SerialNumber
{
    protected PageFactory $resultPageFactory;

    protected ProductRepository $productRepository;

    protected SerialNumberManagement $serialNumberManagement;

    protected JsonFactory $jsonResultFactory;

    protected SerialNumberFactory $serialNumberFactory;

    /**
     * AddSerialNumber constructor.
     */
    public function __construct(
        Context $context,
        Registry $coreRegistry,
        ProductRepository $productRepository,
        PageFactory $resultPageFactory,
        SerialNumberManagement $serialNumberManagement,
        JsonFactory $jsonResultFactory,
        SerialNumberFactory $serialNumberFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->productRepository = $productRepository;
        $this->serialNumberManagement = $serialNumberManagement;
        $this->jsonResultFactory = $jsonResultFactory;
        $this->serialNumberFactory = $serialNumberFactory;
        parent::__construct($context, $coreRegistry);
    }

    public function execute(): \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\ResultInterface
    {
        $serialNumber = $this->getRequest()->getParam('serial_number');
        $productId = $this->getRequest()->getParam('product_id');
        $result = $this->jsonResultFactory->create();
        try {
            $product = $this->productRepository->getById($productId);
            $productSku = $product->getSku();
            $model = $this->serialNumberFactory->create();
            if ($this->serialNumberManagement->checkProductSerialNumberAvailability($productSku, $serialNumber)) {
                $model->setSerialNumber($serialNumber);
                $model->setStatus(SerialNumberInterface::ACTIVE);
                $model->setProductSku($productSku);
                $model->save();
                $result->setData([
                    'success' => true,
                    'message' => __('The %1 number was added successfully', __('Serial Number')),
                ]);
            } else {
                $result->setData([
                    'success' => false,
                    'message' => __('This %1 already exists', __('Serial Number')),
                ]);
            }
        } catch (\Exception $e) {
            $result->setData([
                'success' => false,
                'message' => $e->getMessage(),
            ]);
        }

        return $result;
    }
}
