<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace CopeX\SerialNumber\Controller\Adminhtml\SerialNumber;

use CopeX\SerialNumber\Controller\Adminhtml\SerialNumber;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Registry;
use Magento\Framework\View\Result\PageFactory;

/**
 * Class ImportForm
 *
 * @package CopeX\SerialNumber\Controller\Adminhtml\SerialNumber
 */
class ImportForm extends SerialNumber
{
    private \Magento\Framework\View\Result\PageFactory $resultPageFactory;

    /**
     * ImportForm constructor.
     */
    public function __construct(
        Context $context,
        Registry $coreRegistry,
        PageFactory $resultPageFactory
    ) {
        parent::__construct($context, $coreRegistry);
        $this->resultPageFactory = $resultPageFactory;
    }

    /**
     * New action
     */
    public function execute(): \Magento\Framework\Controller\ResultInterface
    {
        /** @var \Magento\Framework\View\Result\PageFactory $resultPage */
        $resultPage = $this->resultPageFactory->create();
        $this->initPage($resultPage)->addBreadcrumb(
            __('Excel/CSV Serial Import'),
            __('Excel/CSV Serial Import')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Serial Numbers'));
        $resultPage->getConfig()->getTitle()->prepend(__('Excel/CSV Serial Import'));

        return $resultPage;
    }
}
