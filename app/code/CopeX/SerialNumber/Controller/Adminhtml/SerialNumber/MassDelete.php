<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Controller\Adminhtml\SerialNumber;

use CopeX\SerialNumber\Model\ResourceModel\SerialNumber\CollectionFactory;
use CopeX\SerialNumber\Model\SerialNumberRepository;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Ui\Component\MassAction\Filter;

/**
 * Class MassDelete
 *
 * @package CopeX\SerialNumber\Controller\Adminhtml\SerialNumber
 */
class MassDelete extends Action
{
    protected CollectionFactory $collectionFactory;

    protected Filter $filter;

    protected SerialNumberRepository $serialNumberRepository;

    protected RedirectInterface $redirect;

    protected ProductRepositoryInterface $productRepository;

    /**
     * MassDelete constructor.
     */
    public function __construct(
        Context $context,
        Filter $filter,
        CollectionFactory $collectionFactory,
        SerialNumberRepository $serialNumberFactory,
        RedirectInterface $redirect,
        ProductRepositoryInterface $productRepository
    ) {
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        $this->serialNumberRepository = $serialNumberFactory;
        $this->redirect = $redirect;
        $this->productRepository = $productRepository;
        parent::__construct($context);
    }

    public function execute(): \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\ResultInterface
    {
        $refererUrl = $this->redirect->getRefererUrl();
        $collection = $this->getCollection();
        try {
            $count = 0;
            foreach ($collection as $model) {
                if ($model->getOrderItemId()) {
                    $this->messageManager->addWarningMessage(__('%1 is already assigned to an order. Skipping!', $model->getSerialNumber()));
                } else {
                    $this->serialNumberRepository->delete($model);
                    $count++;
                }
            }
            if ($count) {
                $this->messageManager->addSuccessMessage(__('A total of %1 record(s) have been deleted.', $count));
            }
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__($e->getMessage()));
        }

        return $this->resultFactory->create(ResultFactory::TYPE_REDIRECT)->setPath($refererUrl);
    }

    /**
     * @throws NoSuchEntityException
     * @throws \Exception
     */
    private function getCollection()
    {
        $collection = $this->filter->getCollection($this->collectionFactory->create());
        if (! $this->isSerialNumberList()) {
            if ($this->isProductList() && $productId = $this->getProductId()) {
                $productSku = $this->productRepository->getById($productId)->getSku();
                $collection->addFieldToFilter('product_sku', $productSku);
            } else {
                throw new \Exception('No records to delete.');
            }
        }
        return $collection;
    }

    private function isSerialNumberList()
    {
        return $this->getRequest()->getParam('namespace') === 'copex_serialnumber_serialnumber_listing';
    }

    private function isProductList()
    {
        return $this->getRequest()->getParam('namespace') === 'product_grid_listing';
    }
    private function getProductId()
    {
        $httpReferer = $this->getRequest()->getServer('HTTP_REFERER');
        $pattern = "/catalog\/product\/edit\/id\/(\d+)/";
        if (preg_match($pattern, $httpReferer, $matches)) {
            return $matches[1];
        }
        return null;
    }
}
