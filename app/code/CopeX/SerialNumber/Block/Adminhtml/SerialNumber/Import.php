<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Block\Adminhtml\SerialNumber;

use Magento\Backend\Block\Template;
use Magento\Backend\Block\Widget\Context;
use Magento\Framework\UrlInterface;

/**
 * Class Import
 *
 * @package CopeX\SerialNumber\Block\Adminhtml\SerialNumber
 */
class Import extends Template
{
    protected UrlInterface $urlBuilder;

    /**
     * Import constructor.
     */
    public function __construct(
        Context $context,
        UrlInterface $urlBuilder,
        array $data = []
    ) {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $data);
    }

    /**
     * Retrieve the form action URL
     */
    public function getUrl($route = '', $params = []): string
    {
        return $this->urlBuilder->getUrl($route, $params);
    }
}
