<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Observer\Sales;

use CopeX\SerialNumber\Helper\Config;
use CopeX\SerialNumber\Model\SerialNumberManagement;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;

/**
 * Class AssignSerialNumbers
 *
 * @package CopeX\SerialNumber\Observer
 */
class AssignSerialNumbersToOrder implements ObserverInterface
{
    protected SerialNumberManagement $serialNumberManagement;
    private Config $config;

    /**
     * AssignSerialNumbers constructor.
     */
    public function __construct(
        SerialNumberManagement $serialNumberManagement,
        Config $config
    ) {
        $this->serialNumberManagement = $serialNumberManagement;
        $this->config = $config;
    }

    public function execute(Observer $observer): void
    {
        if ($this->config->shouldAssignOnOrder()) {
            $this->assignSerialNumberToOrderItem($observer);
        }
    }

    private function assignSerialNumberToOrderItem($observer): void
    {
        $order = $observer->getEvent()->getOrder();

        if ($this->isOrderEligibleForSerialNumber($order)) {
            $this->processOrderItems($order);
        }
    }

    private function isOrderEligibleForSerialNumber($order): bool
    {
        return $order->getState() !== Order::STATE_CANCELED
            && $order->getEntityType() !== Order::ACTION_FLAG_CREDITMEMO
            && $order->getEntityType() !== Order::ACTION_FLAG_REORDER;
    }

    private function processOrderItems($order): void
    {
        foreach ($order->getAllItems() as $item) {
            $this->assignIfEligible($item);
        }
    }

    private function assignIfEligible($item): void
    {
        if ($this->isOrderItemEligible($item)) {
            $this->serialNumberManagement->assignSerialNumberToOrderItem($item->getId());
        }
    }

    private function isOrderItemEligible($item): bool
    {
        return ! $item->getParentItemId();
    }
}
