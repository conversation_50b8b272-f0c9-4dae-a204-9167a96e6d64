<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Observer\Sales;

use CopeX\SerialNumber\Helper\Config;
use CopeX\SerialNumber\Model\SerialNumberManagement;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

/**
 * Class AssignSerialNumbers
 *
 * @package CopeX\SerialNumber\Observer
 */
class AssignSerialNumbersToInvoice implements ObserverInterface
{
    protected SerialNumberManagement $serialNumberManagement;
    private Config $config;

    /**
     * AssignSerialNumbers constructor.
     */
    public function __construct(
        SerialNumberManagement $serialNumberManagement,
        Config $config
    ) {
        $this->serialNumberManagement = $serialNumberManagement;
        $this->config = $config;
    }

    public function execute(Observer $observer): void
    {
        if ($this->config->shouldAssignOnInvoice()) {
            $this->assignSerialNumberToInvoiceItem($observer);
        }
    }

    private function assignSerialNumberToInvoiceItem(Observer $observer): void
    {
        $invoice = $observer->getEvent()->getObject();
        foreach ($invoice->getAllItems() as $item) {
            $this->processInvoiceItem($item);
        }
    }

    private function processInvoiceItem($item): void
    {
        if ($this->isEligibleForSerialNumber($item)) {
            $this->serialNumberManagement->assignSerialNumberToInvoiceItem($item->getId());
        }
    }

    private function isEligibleForSerialNumber($item): bool
    {
        return ! $item->getParentItemId() && $item->getId();
    }
}
