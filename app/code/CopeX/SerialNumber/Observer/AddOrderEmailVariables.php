<?php

declare(strict_types=1);

namespace CopeX\SerialNumber\Observer;

use CopeX\SerialNumber\Model\SerialNumberManagement;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

/**
 * Class AddOrderEmailVariables
 *
 * @package CopeX\SerialNumber\Observer
 */
class AddOrderEmailVariables implements ObserverInterface
{
    protected SerialNumberManagement $serialNumberManagement;

    /**
     * ReleaseSerialNumbers constructor.
     */
    public function __construct(
        SerialNumberManagement $serialNumberManagement
    ) {
        $this->serialNumberManagement = $serialNumberManagement;
    }

    public function execute(Observer $observer): void
    {
        foreach ($observer->getTransport()->getOrder()->getItems() as $item) {
            $this->assignSerialNumber($item);
        }
    }

    private function assignSerialNumber($item): void
    {
        if ($serialNumber = $this->serialNumberManagement->getAssignedSerialNumber($item->getId())) {
            $item->setData('serial_number', $serialNumber);
        }
    }
}
