<?php

namespace CopeX\SerialNumber\Observer\Catalog\Product;

use CopeX\SerialNumber\Helper\Config;
use CopeX\SerialNumber\Model\SerialNumberManagement;
use Magento\Framework\Event\Observer;

class Salable implements \Magento\Framework\Event\ObserverInterface
{
    private Config $config;
    private SerialNumberManagement $management;

    public function __construct(Config $config, SerialNumberManagement $management)
    {
        $this->config = $config;
        $this->management = $management;
    }

    public function execute(Observer $observer): void
    {
        if ($this->config->getUnavailable()) {
            $numbersLeft = $this->management->getAvailableSerialNumbers($observer->getProduct()->getSku());
            if (count($numbersLeft) <= 0) {
                $observer->getSalable()->setIsSalable(false);
            }
        }
    }
}
