<?php
namespace Pumpe24\Framework\Webapi\Validator;

use Magento\Framework\Exception\InvalidArgumentException;

class EntityArrayValidator extends \Magento\Framework\Webapi\Validator\EntityArrayValidator {

	//need to override complexArrayItemLimit, default is 20 and way to low for some productLinks
	protected $complexArrayItemLimit = 2000;

	public function validateComplexArrayType(string $className, array $items): void {
		if (count($items) > $this->complexArrayItemLimit) {
			throw new InvalidArgumentException(
				__(
					'Maximum items of type "%type" is %max',
					['type' => $className, 'max' => $this->complexArrayItemLimit]
				)
			);
		}
	}
}
