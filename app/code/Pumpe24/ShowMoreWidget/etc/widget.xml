<?xml version="1.0"?>
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget class="Pumpe24\ShowMoreWidget\Block\Widget\ShowMore" id="show-more-widget">
        <label>Show More Widget</label>
        <description>Show More Widget</description>
        <parameters>
            <parameter name="show_more_image" xsi:type="block" visible="true" required="false" sort_order="20">
                <label translate="true">Widget Image</label>
                <block class="Pumpe24\ShowMoreWidget\Block\Adminhtml\Widget\ImageChooser">
                    <data>
                        <item name="button" xsi:type="array">
                            <item name="open" xsi:type="string">Choose Image...</item>
                        </item>
                    </data>
                </block>
            </parameter>
            <parameter name="show_more_image_loading" xsi:type="select" visible="true" required="false" sort_order="30">
                <label translate="true">Image Loading</label>
                <options>
                    <option name="lazy" value="lazy" selected="true">
                        <label translate="true">Lazy</label>
                    </option>
                    <option name="eager" value="eager">
                        <label translate="true">Eager</label>
                    </option>
                    <option name="0" value="0">
                        <label translate="true">Default</label>
                    </option>
                </options>
            </parameter>
            <parameter name="show_more_block_id" xsi:type="block" visible="true" required="true" sort_order="40">
                <label translate="true">Widget Content</label>
                <block class="Magento\Cms\Block\Adminhtml\Block\Widget\Chooser">
                    <data>
                        <item name="button" xsi:type="array">
                            <item name="open" xsi:type="string" translate="true">Select Content CMS Block...</item>
                        </item>
                    </data>
                </block>
            </parameter>
            <parameter name="show_more_text" xsi:type="text" visible="true" sort_order="50">
                <label translate="true">Learn More Text</label>
            </parameter>
        </parameters>
    </widget>
</widgets>
