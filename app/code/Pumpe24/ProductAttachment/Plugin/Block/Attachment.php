<?php
namespace Pumpe24\ProductAttachment\Plugin\Block;

class Attachment {
	protected $_mpHelper;

	public function __construct(
		\Mageprince\Productattach\Helper\Data $mpHelper
	) {
		$this->_mpHelper = $mpHelper;
	}

	public function afterGetAttachment($subject, $result) {
		$result->setOrder("p24_sort", "ASC");
		return $result;
	}

	public function afterGetAttachmentUrl($subject, $result, $attachmentId) {
		return $this->_mpHelper->getMediaUrl() . \Mageprince\Productattach\Model\Config\DefaultConfig::MEDIA_PATH . $attachmentId;
	}
}
?>
