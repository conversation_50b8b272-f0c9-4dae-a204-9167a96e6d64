<?php
namespace Pumpe24\Product\Cron;

class DeleteOutdatedSpecialPrices {

	protected $_storeManager;
	protected $_resource;
	protected $_dateTime;
	protected $_localeDate;
	protected $_eavConfig;
	protected $_processor;
	protected $_productRepository;
	protected $_metaDataPool;

	protected $_connection;

	public function __construct(
		\Magento\Store\Model\StoreManagerInterface $storeManager,
		\Magento\Framework\App\ResourceConnection $resource,
		\Magento\Framework\Stdlib\DateTime $dateTime,
		\Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
		\Magento\Eav\Model\Config $eavConfig,
		\Magento\Catalog\Model\Indexer\Product\Price\Processor $processor,
		\Magento\Framework\EntityManager\MetadataPool $metaDataPool,
		\Magento\Catalog\Model\ProductRepository $productRepository
	) {
		$this->_storeManager = $storeManager;
		$this->_resource = $resource;
		$this->_dateTime = $dateTime;
		$this->_localeDate = $localeDate;
		$this->_eavConfig = $eavConfig;
		$this->_processor = $processor;
		$this->_productRepository = $productRepository;
		$this->_metaDataPool = $metaDataPool;
	}

	protected function _getConnection() {
		if ($this->_connection === null)
			$this->_connection = $this->_resource->getConnection();
		return $this->_connection;
	}


	public function execute() {
		foreach ($this->_storeManager->getStores(true) as $store) {
			$timestamp = $this->_localeDate->scopeTimeStamp($store);

			$this->_deleteSpecialPriceByStore(
				$store->getId(),
				"special_to_date",
				$timestamp
			);
		}
	}

	protected function _deleteSpecialPriceByStore($storeId, $attrCode, $timestamp) {
		$attribute = $this->_eavConfig->getAttribute(\Magento\Catalog\Model\Product::ENTITY, $attrCode);
		$attributeId = $attribute->getAttributeId();

		$linkField = $this->_metaDataPool->getMetadata(\Magento\Catalog\Api\Data\CategoryInterface::class)->getLinkField();
		$identifierField = $this->_metaDataPool->getMetadata(\Magento\Catalog\Api\Data\CategoryInterface::class)->getIdentifierField();

		$connection = $this->_getConnection();

		$dateExpr = $connection->getDateFormatSql($connection->quote($this->_dateTime->formatDate($timestamp)), "%Y-%m-%d %H:%i:%s");

		$select = $connection->select()->from(
			['attr' => $this->_resource->getTableName(['catalog_product_entity', 'datetime'])],
			[$identifierField => 'cat.' . $identifierField]
		)->joinLeft(
			['cat' => $this->_resource->getTableName('catalog_product_entity')],
			'cat.' . $linkField . '= attr.' . $linkField, ''
		)->where(
			'attr.attribute_id = ?',
			$attributeId
		)->where(
			'attr.store_id = ?',
			$storeId
		)->where(
			'attr.value <= ?',
			$dateExpr
		);

		$selectData = $connection->fetchCol($select);

		if (!empty($selectData)) {
			foreach ($selectData as $productId)
				$this->_deleteSpecialPriceFromProduct($productId, $storeId);
			$this->_processor->getIndexer()->reindexList($selectData);
		}
	}

	protected function _deleteSpecialPriceFromProduct($productId, $storeId) {
		$product = $this->_productRepository->getById($productId);
		$product->setStoreId($storeId);
		$product->setSpecialToDate(null);
		$product->setSpecialPrice(null);
		$product->setSpecialFromDate(null);
		$this->_productRepository->save($product);
	}
}
