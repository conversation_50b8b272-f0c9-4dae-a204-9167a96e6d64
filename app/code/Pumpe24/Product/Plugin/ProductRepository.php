<?php
namespace Pumpe24\Product\Plugin;

class ProductRepository {

	protected $_configurableProduct;
	protected $_flushCache;
	protected $_indexerFactory;

	public function __construct(
		\Magento\ConfigurableProduct\Model\Product\Type\Configurable $configurableProduct,
		\Magento\InventoryCache\Model\FlushCacheByProductIds $flushCache,
		\Magento\Indexer\Model\IndexerFactory $indexerFactory
	) {
		$this->_configurableProduct = $configurableProduct;
		$this->_flushCache = $flushCache;
		$this->_indexerFactory = $indexerFactory;
	}

	//if configurable child is updated, flush the cache of all its parent products
	public function afterSave($subject, $result, $product, $saveOptions = false) {
		$parentIds = $this->_configurableProduct->getParentIdsByChild($product->getId());
		if (($parentIds) && (sizeof($parentIds) >= 1)) {
			$indexer = $this->_indexerFactory->create();
			$indexer->load("catalog_product_price");
			$indexer->invalidate();
			$this->_flushCache->execute($parentIds);
		}
		return $result;
	}
}
?>
