<?php
namespace Pumpe24\Product\Plugin;

class ReviewListView {

	protected $_request;

	public function __construct(
		\Magento\Framework\App\RequestInterface $request
	) {
		$this->_request = $request;
	}

	public function afterGetReviewsCollection($subject, $result) {
		$page = $this->_request->getParam("p");
		if (!$page)
			$page = 1;

		$result->setPageSize(6)->setCurPage($page);
		return $result;
	}
}
?>
