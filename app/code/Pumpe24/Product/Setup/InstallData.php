<?php
namespace Pumpe24\Product\Setup;

use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Eav\Model\Config as EavConfig;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;

class InstallData implements InstallDataInterface {

	protected $eavSetupFactory;
	protected $attributeSetFactory;

	public function __construct(
		EavSetupFactory $eavSetupFactory,
		EavConfig $eavConfig
	) {
		$this->eavSetupFactory = $eavSetupFactory;
		$this->eavConfig = $eavConfig;
	}

	public function install(
		ModuleDataSetupInterface $setup,
		ModuleContextInterface $context
	) {
		$setup->startSetup();

		//create Product Attributes
		$this->createProductAttributes($setup);

		$setup->endSetup();
	}

	protected function createProductAttributes($setup) {
		$eavSetup = $this->eavSetupFactory->create(["setup" => $setup]);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_is_general_cargo",
			[
				"type" => "int",
				"label" => "P24 Is General Cargo",
				"input" => "boolean",
				"sort_order" => 10,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_is_pickup_only",
			[
				"type" => "int",
				"label" => "P24 Is Pickup Only",
				"input" => "boolean",
				"sort_order" => 20,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_can_express",
			[
				"type" => "int",
				"label" => "P24 Can Express",
				"input" => "boolean",
				"sort_order" => 40,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_can_gls",
			[
				"type" => "int",
				"label" => "P24 Can GLS",
				"input" => "boolean",
				"sort_order" => 42,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => "1",
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_can_dpd",
			[
				"type" => "int",
				"label" => "P24 Can DPD",
				"input" => "boolean",
				"sort_order" => 42,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_can_ups",
			[
				"type" => "int",
				"label" => "P24 Can UPS",
				"input" => "boolean",
				"sort_order" => 44,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_stock_status",
			[
				"type" => "int",
				"label" => "P24 Stock Status",
				"input" => "text",
				"sort_order" => 50,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_main_attributes",
			[
				"type" => "text",
				"label" => "P24 Main Attributes",
				"input" => "text",
				"sort_order" => 60,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
			\Magento\Catalog\Model\Product::ENTITY,
			"p24_show_consultation",
			[
				"type" => "int",
				"label" => "P24 Show Consultation",
				"input" => "boolean",
				"sort_order" => 70,
				"source" => "",
				"global" => ScopedAttributeInterface::SCOPE_GLOBAL,
				"visible" => true,
				"visible_on_front" => false,
				"required" => false,
				"user_defined" => true,
				"default" => null,
				"group" => "Pumpe24 Custom Options",
				"backend" => ""
			]
		);

		$eavSetup->addAttribute(
                        \Magento\Catalog\Model\Product::ENTITY,
                        "keyfacts",
                        [
                                "type" => "text",
                                "label" => "Keyfacts",
                                "input" => "textarea",
                                "sort_order" => 80,
                                "source" => "",
                                "global" => ScopedAttributeInterface::SCOPE_STORE,
                                "visible" => true,
                                "visible_on_front" => false,
                                "required" => false,
                                "user_defined" => true,
                                "default" => "",
                                "group" => "Pumpe24 Custom Options",
                                "backend" => ""
                        ]
                );

		$eavSetup->addAttribute(
                        \Magento\Catalog\Model\Product::ENTITY,
                        "p24_base_price_divider",
                        [
                                "type" => "decimal",
                                "label" => "Base Price Divider",
                                "input" => "text",
                                "sort_order" => 90,
                                "source" => "",
                                "global" => ScopedAttributeInterface::SCOPE_GLOBAL,
                                "visible" => true,
                                "visible_on_front" => false,
                                "required" => false,
                                "user_defined" => true,
                                "default" => null,
                                "group" => "Pumpe24 Custom Options",
                                "backend" => ""
                        ]
                );

		$eavSetup->addAttribute(
                        \Magento\Catalog\Model\Product::ENTITY,
                        "p24_base_price_unit",
                        [
                                "type" => "text",
                                "label" => "Base Price Unit",
                                "input" => "text",
                                "sort_order" => 100,
                                "source" => "",
                                "global" => ScopedAttributeInterface::SCOPE_GLOBAL,
                                "visible" => true,
                                "visible_on_front" => false,
                                "required" => false,
                                "user_defined" => true,
                                "default" => "",
                                "group" => "Pumpe24 Custom Options",
                                "backend" => ""
                        ]
                );


	}
}
?>
