<?php

declare(strict_types=1);

namespace Pumpe24\Product\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Cms\Model\Template\FilterProvider;

class AttributeData implements ArgumentInterface
{
    /**
     * @var FilterProvider
     */
    protected $filter;

    /**
     * AttributeData constructor.
     * @param FilterProvider $filter
     */
    public function __construct(
        FilterProvider $filter
    ) {
        $this->filter = $filter;
    }
    /**
     * @param $productDescription
     * @return string
     */
    public function getDescription($productDescription)
    {
        return "<div id=\"description-wrapper\" class=\"grid mt-1\" "
        . "x-data=\"initProductDetail('description-wrapper', 'description', '')\" "
        . "@adddetail.window=\"addElement(\$event.detail)\" "
        . "@selectdetail.window=\"selectElement(\$event.detail)\" "
        . ">"
        . "<div class=\"transition-all ease-in duration-200 overflow-hidden\" >"
        . $this->filter->getPageFilter()->filter(trim($productDescription))
        . "</div>"
        . "</div>";
    }

    /**
     * @param $product
     * @return string
     */
    public function getTechnicalDetails($product) {
        $group_id = 23;
        $productAttributes = $product->getAttributes();
        $attributeSetId = $product->getAttributeSetId();
        $html = '<div id="attribute-wrapper" class="grid"
                                 x-data="initProductDetail(\'attribute-wrapper\', \'attributes\', \'grid grid-cols-2 grid-cols-prod-details lg:grid-cols-1 xl:grid-cols-2\')"
                                 @adddetail.window="addElement($event.detail);"
                                 @selectdetail.window="selectElement($event.detail);"
                            ><div class="grid grid-cols-2 grid-cols-prod-details lg:grid-cols-1 xl:grid-cols-2 overflow-hidden transition-all ease-in duration-200 grid-area-1-1" x-show="selected == 0"
                                    x-transition:enter-start="opacity-0"
                                    x-transition:enter-end="opacity-100"
                                    x-transition:leave-start="opacity-100"
                                    x-transition:leave-end="opacity-0" >';
        foreach ($productAttributes as $attribute) {
            if (($attribute->isInGroup($attributeSetId, $group_id)) &&
                ($attribute->getFrontend()->getValue($product)) &&
                ($attribute->getAttributeCode() != "einstufung_nach_eg_richtlinien")
            ) {
                $html .= "<div class=\"font-bold break-words\">";
                $html .= $attribute->getFrontendLabel();
                $html .= "</div>";
                $html .= "<div class=\"break-words\">";
                $html .= $attribute->getFrontend()->getValue($product);
                $html .= "&nbsp";
                $html .= $attribute->getData("p24_unit");
                $html .= "</div>";
            }
        }
        $html .= "</div></div>";

        return $html;
    }

    public function getManufacturerBlock($blockContent)
    {
        $html = '<div id="manufacturer-wrapper" class="grid"
                                 x-data="initProductDetail(\'manufacturer-wrapper\', \'manufacturer\', \'grid grid-cols-2 grid-cols-prod-details lg:grid-cols-1 xl:grid-cols-2\')"
                                 @adddetail.window="addElement($event.detail);"
                                 @selectdetail.window="selectElement($event.detail);"
                            ><div class="grid grid-cols-2 grid-cols-prod-details lg:grid-cols-1 xl:grid-cols-2 overflow-hidden transition-all ease-in duration-200 grid-area-1-1" x-show="selected == 0"
                                    x-transition:enter-start="opacity-0"
                                    x-transition:enter-end="opacity-100"
                                    x-transition:leave-start="opacity-100"
                                    x-transition:leave-end="opacity-0" >';
        $html .= $blockContent;
        $html .= "</div></div>";
        return $html;
    }
}
