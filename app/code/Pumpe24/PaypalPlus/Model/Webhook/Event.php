<?php
namespace Pumpe24\PaypalPlus\Model\Webhook;

class Event extends \Iways\PayPalPlus\Model\Webhook\Event {

	protected $_logger;
	protected $_invoiceSender;

	public function __construct(
		\Magento\Sales\Model\Order\Payment\TransactionFactory $salesOrderPaymentTransactionFactory,
		\Magento\Sales\Model\OrderFactory $salesOrderFactory,
		\Psr\Log\LoggerInterface $logger,
		\Magento\Sales\Model\Order\Email\Sender\InvoiceSender $invoiceSender
	) {
		parent::__construct($salesOrderPaymentTransactionFactory, $salesOrderFactory, $logger);
		$this->_logger = $logger;
		$this->_invoiceSender = $invoiceSender;
	}

	//martin we need to override this because of two reasons:
	//1. $_order->queueNewOrderEmail is absent
	//2. we want to send the invoice instead
	protected function paymentSaleCompleted(\PayPal\Api\WebhookEvent $webhookEvent) {
		$paymentResource = $webhookEvent->getResource();
		$parentTransactionId = $paymentResource->parent_payment;
		$payment = $this->_order->getPayment();

		$payment->setTransactionId($paymentResource->id)
			->setCurrencyCode($paymentResource->amount->currency)
			->setParentTransactionId($parentTransactionId)
			->setIsTransactionClosed(true)
			->registerCaptureNotification(
				$paymentResource->amount->total,
				true
			);
		$this->_order->save();

		$invoice = $payment->getCreatedInvoice();
		if ($invoice) {
			$this->_invoiceSender->send($invoice);

			$this->_order->addStatusHistoryComment("Invoice sent by Pumpe24_PaypalPlus", "processing");
			$this->_order->save();
		} else {
			$this->_logger->critical("Cannot create Invoice for Order " . $this->_order->getIncrementId());
		}
	}
}
?>
