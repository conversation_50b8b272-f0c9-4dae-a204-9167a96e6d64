<div class="payment-method" data-bind="css: {'_active': (getCode() == isChecked())}">
	<div class="payment-method-title field choice">
		<input type="radio"
			name="payment[method]"
			class="radio"
			data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: p24PPPSelectPaymentMethod, visible: isRadioButtonVisible()"/>
		<label data-bind="attr: {'for': getCode()}" class="label">
			<span data-bind="text: getTitle()"></span>
		</label>
	</div>
    <div class="payment-method-content">
        <div id="ppplus"></div>
	<style>#ppplus > iframe {width:100% !important;}</style>
	<!-- ko with: p24PPPCheckIfSelected() --><!-- /ko -->
        <div class="payment-method-billing-address" data-bind="style: { display: isPPPMethod() ? 'block' : 'none' }">
            <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>
        <div class="checkout-agreements-block"  data-bind="style: { display: isPPPMethod() ? 'block' : 'none' }">
            <!-- ko foreach: $parent.getRegion('before-place-order') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>
        <div class="actions-toolbar" data-bind="style: { display: isPPPMethod() ? 'block' : 'none' }">
            <div class="primary">
                <button class="action primary checkout"
                        id="place-ppp-order"
                        type="submit"
                        data-bind="
                        click: placePPPOrder,
                        attr: {'title': $t('Place Order')},
                        css: {disabled: !isPlaceOrderActionAllowed()}
                        "
                        disabled>
                    <span data-bind="i18n: 'Place Order'"></span>
                </button>
            </div>
        </div>
    </div>
</div>
