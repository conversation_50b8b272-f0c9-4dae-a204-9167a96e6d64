<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Doofinder\Feed\Observer\Product\CatalogProductSaveAfterObserver"><!-- done -->
		<plugin name="p24_doofinder_product_save_observer" type="\Pumpe24\Doofinder\Plugin\ChangedProductObserver" sortOrder="10" />
	</type>
	<type name="Magento\Catalog\Model\Indexer\Product\Price\Action\Row">
		<plugin name="p24_catalog_doofinder_plugin_row" type="Pumpe24\Doofinder\Plugin\Product\Price\Indexer\Row" sortOrder="10" />
	</type>
	<type name="Magento\Catalog\Model\Indexer\Product\Price\Action\Rows">
		<plugin name="p24_catalog_doofinder_plugin_rows" type="Pumpe24\Doofinder\Plugin\Product\Price\Indexer\Rows" sortOrder="10" />
	</type>
	<type name="Doofinder\Feed\Model\Config\Source\Cronexpression">
		<plugin name="p24_cron_add_minute" type="Pumpe24\Doofinder\Plugin\Config\Source\CronExpression" sortOrder="10" />
	</type>
    <type name="Doofinder\Feed\Model\ProductRepository">
        <plugin name="add_product_information"  type="Pumpe24\Doofinder\Plugin\AddProductInformation"/>
    </type>
</config>
