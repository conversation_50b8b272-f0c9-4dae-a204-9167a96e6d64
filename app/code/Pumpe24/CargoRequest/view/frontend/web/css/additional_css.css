
.checkout-index-index .shipping-expander {
    transform: translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
    width: 40px;
    height: 40px;
}

.checkout-index-index .shipping-title-block {
    display:flex;
}

.hide-list {
    display: none;
}

.rotate-180 {
    --tw-rotate: 180deg;
}

.checkout-index-index .new-cart-icon {
    width: 40px;
    height: 40px;
}

#expand-icon, #expand-icon:hover, .cart-icon-button .cart-icon-button:hover {
    background: unset;
    border: unset;
}

.checkout-index-index .summary-title:before {
    content: unset !important;
}

.checkout-index-index .summary-title {
    font-size: 1.8rem;
    font-weight: 500;
    margin-left: 10px;
}

.checkout-index-index .summary-title-block {
    display: flex;
    padding: 0 0 15px;
}

.checkout-index-index div[name="shippingAddress.vat_id"] {
    width: 100% !important;
}
