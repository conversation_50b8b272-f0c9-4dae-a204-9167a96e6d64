<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
<system>
	<tab id="pumpe24" translate="label" sortOrder="9999">
		<label>Pumpe24</label>
	</tab>
	<section id="cargoRequest" translate="label" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
		<class>separator-top</class>
		<label>Cargo Request</label>
		<tab>pumpe24</tab>
		<resource>Pumpe24_CargoRequest::configuration</resource>
		<group id="main" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
			<label>Main</label>
			<field id="template" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Email Template</label>
				<source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
			</field>
			<field id="email" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Send Request to Email</label>
			</field>
		</group>
	</section>
</system>
</config>
