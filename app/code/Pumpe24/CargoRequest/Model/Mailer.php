<?php
namespace Pumpe24\CargoRequest\Model;

class Mailer {

	protected $_storeManager;
	protected $_transportBuilder;
	protected $_countryFactory;
	protected $_escaper;
	protected $_helper;

	protected $_totalWeight;

	public function __construct(
		\Magento\Store\Model\StoreManagerInterface $storeManager,
		\Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
		\Magento\Directory\Model\CountryFactory $countryFactory,
		\Magento\Framework\Escaper $escaper,
		\Pumpe24\CargoRequest\Helper\Data $helper
	) {
		$this->_storeManager = $storeManager;
		$this->_transportBuilder = $transportBuilder;
		$this->_countryFactory = $countryFactory;
		$this->_escaper = $escaper;
		$this->_helper = $helper;
	}

	public function sendRequest($items, $addressData) {

		if (sizeof($items) <= 0)
			return false;
		$template = $this->_helper->readModuleConfig("template");

		if ($template) {
			$recipient = trim((string)$this->_helper->readModuleConfig("email"));
			if ($recipient == "")
				return false;
			$sender = [
				"name" => $this->_helper->readGlobalConfig("trans_email/ident_general/name"),
				"email" => $this->_helper->readGlobalConfig("trans_email/ident_general/email")
			];
			$vars = $this->_getVars($items, $addressData);
			$transport = $this->_transportBuilder
			->setTemplateIdentifier($template)
			->setTemplateOptions(
				[
					"area" => \Magento\Framework\App\Area::AREA_FRONTEND,
					"store" => $this->_storeManager->getStore()->getId()
				]
			)
			->setTemplateVars($vars)
			->setFrom($sender)
			->setReplyTo($addressData->email)
			->addTo($recipient)
			->getTransport();

			$transport->sendMessage();
			return true;
		} else
			return false;
	}

//TODO ESCAPER!!

	protected function _getVars($items, $addressData) {
		$destCountry = $this->_countryFactory->create()->loadByCode($addressData->shippingAddress->countryId);
		if ($destCountry)
			$destCountryName = $destCountry->getName();
		else
			$destCountryName = $addressData->shippingAddress->countryId;

		$result = array();
		$result["email"] = $this->_escaper->escapeHtml($addressData->email);
		$result["shippingAddress"] = $this->_buildAddress($addressData->shippingAddress);
		$result["billingAddress"] = $this->_buildAddress($addressData->billingAddress);
		$result["products"] = $this->_buildProducts($items);
		$result["destPostCountry"] = $this->_escaper->escapeHtml($addressData->shippingAddress->postcode . " " . $destCountryName);
		$result["totalWeight"] = $this->_escaper->escapeHtml(number_format($this->_totalWeight, 3, ",", "."));

		return $result;
	}
	protected function _buildAddress($address) {
		$result = "";
		if ($address->company)
			$result .= $this->_escaper->escapeHtml($address->company) . "<br />";
		$result .= $this->_escaper->escapeHtml($address->firstname . " " . $address->lastname) . "<br />"
			. $this->_escaper->escapeHtml($address->street[0]) . "<br />"
			. $this->_escaper->escapeHtml($address->countryId . "-" . $address->postcode . " " . $address->city);
		if ($address->telephone)
			$result .= "<br /><br /> Telefon: " . $this->_escaper->escapeHtml($address->telephone);
		return $result;
	}

	protected function _buildProducts($items) {

		$result = "<table>"
			. "<tr><th>Artikelnr.</th><th>Name</th><th>Menge</th><th class=\"r\">Einzelpr.</th><th class=\"r\">Gesamtpr.</th></tr>";
		$total = 0;
		$this->_totalWeight = 0;
		foreach ($items as $p) {
			$result .= "<tr>"
				. "<td>" . $this->_escaper->escapeHtml($p->getSku()) . "</td>"
				. "<td>" . $this->_escaper->escapeHtml($p->getName()) . "</td>"
				. "<td class=\"r\">" . $this->_escaper->escapeHtml($p->getQty()) . "</td>"
				. "<td class=\"r\">" . $this->_escaper->escapeHtml(number_format($p->getPriceInclTax(), 2, ",", ".")) . " &euro;</td>"
				. "<td class=\"r\">" . $this->_escaper->escapeHtml(number_format($p->getRowTotalInclTax(), 2, ",", ".")) . " &euro;</td>"
				. "</tr>";
			$total += $p->getRowTotalInclTax();
			$this->_totalWeight += ($p->getProduct()->getWeight() * $p->getQty());
		}

		$result .= "<tr><td class=\"r\" colspan=\"4\"><b>Gesamtpreis:</b></td><td class=\"r\"><b>" . $this->_escaper->escapeHtml(number_format($total, 2, ",", ".")) . " &euro;</b></td></tr>"
			. "</table>";

		return $result;
	}
}
?>
