<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<fieldset name="pumpe24_custom_settings">
		<argument name="data" xsi:type="array">
			<item name="config" xsi:type="array">
				<item name="label" xsi:type="string" translate="true">Pumpe24 Custom Settings</item>
				<item name="collapsible" xsi:type="boolean">true</item>
				<item name="sortOrder" xsi:type="number">200</item>
			</item>
		</argument>
		<field name="p24_customer_group">
			<argument name="data" xsi:type="array">
				<item name="options" xsi:type="object">Pumpe24\CustomerCategory\Model\Config\Source\CustomerGroup</item>
				<item name="config" xsi:type="array">
					<item name="dataType" xsi:type="string">text</item>
					<item name="label" translate="true" xsi:type="string">P24 Customer Groups</item>
					<item name="formElement" xsi:type="string">multiselect</item>
				</item>
			</argument>
		</field> 
	</fieldset>
</form>
