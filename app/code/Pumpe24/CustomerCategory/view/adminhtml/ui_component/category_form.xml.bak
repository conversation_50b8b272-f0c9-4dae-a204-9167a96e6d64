<?xml version="1.0"?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
<!--	<fieldset name="pumpe24_custom_fieldset"> -->
	<fieldset name="display settings">
		<field name="filterable_attributes">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="sortOrder" xsi:type="number">10</item>
					<item name="dataType" xsi:type="string">string</item>
					<item name="formElement" xsi:type="string">input</item>
					<item name="label" xsi:type="string" translate="true">Filterable Attributes</item>
					<item name="default" xsi:type="string"></item>
				</item>
			</argument>
		</field>
	</fieldset>
</form>
