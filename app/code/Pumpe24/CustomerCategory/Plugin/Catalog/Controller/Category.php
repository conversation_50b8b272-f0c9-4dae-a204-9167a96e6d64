<?php
namespace Pumpe24\CustomerCategory\Plugin\Catalog\Controller;

class Category extends \Pumpe24\CustomerCategory\Plugin\AbstractPlugin {

	public function afterExecute($subject, $page) {
		$category = $this->_registry->registry("current_category");
		if ($category) {
			$customerGroups = $category->getData("p24_customer_group");
			if ($customerGroups != "") {
				$page->setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0", true);
				$this->_pageConfig->setRobots("NOINDEX,NOFOLLOW");
			}
		}
		return $page;
	}
}
?>
