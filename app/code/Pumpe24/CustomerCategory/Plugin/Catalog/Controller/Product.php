<?php
namespace Pumpe24\CustomerCategory\Plugin\Catalog\Controller;

class Product extends \Pumpe24\CustomerCategory\Plugin\AbstractPlugin {

	public function afterExecute($subject, $page) {
		$product = $this->_registry->registry("current_product");
                $customerGroups = $this->_helper->collectCustomerGroupsForProduct($product);
                if (sizeof($customerGroups) >= 1) {
                        $page->setHeader("Cache-Control", "no-store, no-cache, must-revalidate, max-age=0", true);
			$this->_pageConfig->setRobots("NOINDEX,NOFOLLOW");
                        if ($this->_helper->checkCustomerGroups($customerGroups))
                                return $page;
                        else
                                return $this->_redirect();
                } else
                        return $page;
	}
}
?>
