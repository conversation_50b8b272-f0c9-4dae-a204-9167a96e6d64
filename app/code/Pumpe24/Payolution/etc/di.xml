<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Magento\Framework\Mail\Template\TransportBuilder">
		<plugin name="Pumpe24_Payolution::AddBccToInvoiceEmail" type="Pumpe24\Payolution\Plugin\MailEvent" sortOrder="10" disabled="false" />
	</type>
	<type name="Payone\Core\Model\Methods\Payolution\Invoice">
		<plugin name="Pumpe24_Payolution::InvoiceCheckAddress" type="Pumpe24\Payolution\Plugin\Methods\Invoice" sortOrder="10" disabled="false" />
	</type>
	<type name="Payone\Core\Model\Methods\Payolution\Installment">
		<plugin name="Pumpe24_Payolution::InstallmentCheckAddress" type="Pumpe24\Payolution\Plugin\Methods\Installment" sortOrder="10" disabled="false" />
	</type>
</config>


