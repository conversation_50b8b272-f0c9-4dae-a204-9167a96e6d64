<?php
namespace Pumpe24\Review\Controller\Review;

class Load extends \Magento\Framework\App\Action\Action {

	protected $pageSize = 6;
	protected $ratingSteps = 5;

	protected $_storeManager;
	protected $_productRepository;
	protected $_reviewsColFactory;
	protected $_escaper;
	protected $_resultJsonFactory;

	public function __construct(
		\Magento\Framework\App\Action\Context $context,
		\Magento\Store\Model\StoreManagerInterface $storeManager,
		\Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
		\Magento\Review\Model\ResourceModel\Review\CollectionFactory $reviewsColFactory,
		\Magento\Framework\Escaper $escaper,
		\Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory
	) {
		$this->_storeManager = $storeManager;
		$this->_productRepository = $productRepository;
		$this->_reviewsColFactory = $reviewsColFactory;
		$this->_escaper = $escaper;
		$this->_resultJsonFactory = $resultJsonFactory;
		return parent::__construct($context);
	}

	public function execute() {
		$productId = $this->_request->getParam("product");
		if ($productId) {
			$page = $this->_request->getParam("page", 1);
			$collection = $this->_reviewsColFactory->create()->addStoreFilter($this->_storeManager->getStore()->getId())->addStatusFilter(\Magento\Review\Model\Review::STATUS_APPROVED)
				->addEntityFilter("product", $productId)->setDateOrder();

			//addRateVotes must be the last call (because it executes load() ? )
			//otherwise, sort and limit won't work
			$items = $collection->setPageSize($this->pageSize)->setCurPage($page)->addRateVotes()->getItems();
			if (count($items) > 0) {
				$result = [];
				foreach ($items as $i) {
					$tmp = [
						"nickname" => $this->_escaper->escapeHtml($i->getNickname()),
						"title" => $this->_escaper->escapeHtml($i->getTitle()),
						"detail" => nl2br($this->_escaper->escapeHtml($i->getDetail())),
						"date" => $this->_escaper->escapeHtml(date("d.m.y", strtotime($i->getCreatedAt()))),
						"ratings" => []
					];

					foreach ($i->getRatingVotes() as $vote) {
						$rating = $vote->getPercent();
						$starsFilled = is_numeric($rating) ? floor($rating / 100 * $this->ratingSteps) : 0;
						$tmp["ratings"][] = $starsFilled;
					}

					$result[] = $tmp;
				}
				$json = $this->_resultJsonFactory->create();
				$json->setData(["data" => $result]);
				return $json;
			}
		}
	}
}

?>
