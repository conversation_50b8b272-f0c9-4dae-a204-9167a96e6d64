<?php
namespace Pumpe24\Shipping\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

/**
 * @codeCoverageIgnore
 */
class InstallSchema implements InstallSchemaInterface
{
	/**
	* {@inheritdoc}
	* @SuppressWarnings(PHPMD.ExcessiveMethodLength)
	*/
	public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
	{
		$setup->startSetup();
		$rateTableNames = array(
			"pumpe24_shipping_rates_dpd",
			"pumpe24_shipping_rates_gls",
			"pumpe24_shipping_rates_ups",
			"pumpe24_shipping_rates_dachser",
			"pumpe24_shipping_rates_express_18",
			"pumpe24_shipping_rates_express_12",
			"pumpe24_shipping_rates_express_saturday",
			"pumpe24_shipping_rates_pickup"
		);

		foreach ($rateTableNames AS $tableName) {
			$table = $setup->getConnection()->newTable($setup->getTable($tableName))
			->addColumn(
				'id',
				\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
				null,
				['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
				null
			)
			->addColumn(
				'country',
				\Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
				2,
				['nullable' => false, 'default' => ''],
				null
			)
			->addColumn(
				'postcode_from',
				\Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
				12,
				['nullable' => true, 'default' => null],
				null
			)
			->addColumn(
				'postcode_to',
				\Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
				12,
				['nullable' => true, 'default' => null],
				null
			)
			->addColumn(
				'weight_from',
				\Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
				'12,3',
				['nullable' => false, 'default' => 0],
				null
			)
			->addColumn(
				'weight_to',
				\Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
				'12,3',
				['nullable' => false, 'default' => 0],
				null
			)
			->addColumn(
				'rate',
				\Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
				'12,2',
				['nullable' => false, 'default' => 0],
				null
			);
			$setup->getConnection()->createTable($table);
		}

		$table = $setup->getConnection()->newTable($setup->getTable("pumpe24_shipping_holidays"))
		->addColumn(
			'id',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			null,
			['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
			null
		)
		->addColumn(
			'name',
			\Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
			255,
			['nullable' => false, 'default' => ''],
			null
		)
		->addColumn(
			'day',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			null,
			['nullable' => true, 'default' => null],
			null
		)
		->addColumn(
			'month',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			255,
			['nullable' => true, 'default' => null],
			null
		)
		->addColumn(
			'formula',
			\Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
			255,
			['nullable' => true, 'default' => null],
			null
		);
		$setup->getConnection()->createTable($table);
		$table = $setup->getConnection()->newTable($setup->getTable("pumpe24_shipping_holidays_counties"))
		->addColumn(
			'id',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			null,
			['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
			null
		)
		->addColumn(
			'name',
			\Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
			255,
			['nullable' => false, 'default' => ''],
			null
		);
		$setup->getConnection()->createTable($table);

		$table = $setup->getConnection()->newTable($setup->getTable("pumpe24_shipping_holidays_postcodes"))
		->addColumn(
			'postcode',
			\Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
			12,
			['nullable' => false, 'primary' => true],
			null
		)
		->addColumn(
			'county',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			null,
			['nullable' => false, 'unsigned' => true],
			null
		)
		->addForeignKey(
			$setup->getFkName('pumpe24_shipping_holidays_counties_postcodes', 'county', 'pumpe24_shipping_holidays_counties', 'id'),
			'county',
			$setup->getTable('pumpe24_shipping_holidays_counties'),
			'id',
			\Magento\Framework\DB\Ddl\Table::ACTION_RESTRICT
		);
		$setup->getConnection()->createTable($table);

		$table = $setup->getConnection()->newTable($setup->getTable("pumpe24_shipping_holidays_counties_holidays"))
		->addColumn(
			'id',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			null,
			['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
			null
		)
		->addColumn(
			'county',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			null,
			['unsigned' => true, 'nullable' => false, 'primary' => true],
			null
		)
		->addColumn(
			'holiday',
			\Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
			null,
			['unsigned' => true, 'nullable' => false, 'primary' => true],
			null
		)
		->addForeignKey(
			$setup->getFkName('pumpe24_shipping_holidays_counties_holidays', 'county', 'pumpe24_shipping_holidays_counties', 'id'),
			'county',
			$setup->getTable('pumpe24_shipping_holidays_counties'),
			'id',
			\Magento\Framework\DB\Ddl\Table::ACTION_RESTRICT
		)
		->addForeignKey(
			$setup->getFkName('pumpe24_shipping_holidays_counties_holidays', 'holiday', 'pumpe24_shipping_holidays', 'id'),
			'holiday',
			$setup->getTable('pumpe24_shipping_holidays'),
			'id',
			\Magento\Framework\DB\Ddl\Table::ACTION_RESTRICT
		);
		$setup->getConnection()->createTable($table);

		$setup->endSetup();
	}
}
?>
