<?php

namespace Pumpe24\Shipping\Model\Carrier;

use Pumpe24\Shipping\Model\Carrier\AbstractP24Carrier;

class Gls extends AbstractP24Carrier
{
	public function __construct(
		\Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
		\Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory,
		\Psr\Log\LoggerInterface $logger,
		\Magento\Shipping\Model\Rate\ResultFactory $rateResultFactory,
		\Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory,
		\Magento\Framework\HTTP\PhpEnvironment\RemoteAddress $remoteAddress,
		\Pumpe24\Shipping\Model\ResourceModel\GlsFactory $tablerateFactory,
		\Pumpe24\Shipping\Model\ShippingDateCalculator $shippingDateCalculator,
		\Magento\Checkout\Model\Cart $cart,
		\Pumpe24\Helper\Helper\Data $p24Helper,
		array $data = []
	) {
		parent::__construct($scopeConfig, $rateErrorFactory, $logger, $rateResultFactory, $rateMethodFactory, $remoteAddress, $shippingDateCalculator, $cart, $p24Helper, $data);
		$this->_code = "pumpe24Gls";
		$this->tablerateFactory = $tablerateFactory;
	}
}
