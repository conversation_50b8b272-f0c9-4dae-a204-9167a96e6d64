<?php
namespace Pumpe24\Shipping\Block;

class EstimatedShippingCosts extends \Magento\Framework\View\Element\Template {

	protected $_model;
	protected $_shippingMethods = array();

	protected $_product;

	public function __construct(
		\Magento\Framework\View\Element\Template\Context $context,
		\Pumpe24\Shipping\Model\EstimatedShippingCosts $model
	) {
		parent::__construct($context);
		$this->_model = $model;
	}

	public function getEstimatedShippingCosts($product) {
		return $this->_model->getEstimatedShippingCosts($product);
	}

	public function setProduct($p) {
		$this->_product = $p;
		return $this;
	}

	public function getProduct() {
		return $this->_product;
	}
}
?>
