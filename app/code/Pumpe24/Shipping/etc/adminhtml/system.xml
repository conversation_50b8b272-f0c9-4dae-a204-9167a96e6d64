<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<section id="carriers" translate="label" type="text" sortOrder="320" showInDefault="1" showInWebsite="1" showInStore="1">
			<group id="pumpe24Dpd" translate="label" type="text" sortOrder="900" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 DPD</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>


				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

			<group id="pumpe24Gls" translate="label" type="text" sortOrder="1000" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 GLS</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

			<group id="pumpe24Ups" translate="label" type="text" sortOrder="1050" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 UPS</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>


				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

			<group id="pumpe24Dachser" translate="label" type="text" sortOrder="1100" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 Dachser</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

			<group id="pumpe24Express18" translate="label" type="text" sortOrder="1200" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 Express 18</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today, receiving tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_today_receiving_another_day" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today, Receiving another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

			<group id="pumpe24Express12" translate="label" type="text" sortOrder="1300" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 Express 12</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today, Receiving tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_today_receiving_another_day" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today, Receiving another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

			<group id="pumpe24ExpressSaturday" translate="label" type="text" sortOrder="1400" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 Express Saturday</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

			<group id="pumpe24Pickup" translate="label" type="text" sortOrder="1500" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Pumpe24 Pickup</label>
				<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Enabled</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Method Name</label>
				</field>
				<field id="info_text" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Info text</label>
					<comment>html allowed</comment>
				</field>
				<field id="sort_order" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Sort Order</label>
				</field>
				<field id="show_on_product" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on product page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
				<field id="show_on_cart" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Show on cart page</label>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>

				<group id="restrictions" type="label" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Restrictions</label>
					<field id="allow_on_weekdays" translate="label" type="multiselect" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed on Weekdays</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="allow_if_out_of_stock" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if out of stock</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_cargo_only" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Cargo Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allow_pickup_only" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow if Pickup Only is set</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_express_only" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if Express allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_gls_only" translate="label" type="select" sortOrder="61" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if GLS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_dpd_only" translate="label" type="select" sortOrder="62" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if DPD allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="need_ups_only" translate="label" type="select" sortOrder="64" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allow only if UPS allowed</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="allowed_ips" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed IPs (everyone allowed, if empty)</label>
						<comment>comma-separated list of allowed ip adresses for this method</comment>
					</field>
					<field id="forbidden_postcodes" translate="label" type="text" sortOrder="75" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Forbidden Postcodes (everyone allowed, if empty)</label>
						<comment>comma-separated list of forbidden postcodes for this method</comment>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Ship to Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
					<field id="disabled_payments" showInDefault="1" type="multiselect" showInStore="1" showInWebsite="1" sortOrder="100" translate="label">
						<label>Disable Payment Methods</label>
						<source_model>Magento\Payment\Model\Config\Source\Allmethods</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="free_shipping" type="label" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Free Shipping</label>
					<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Enabled</label>
						<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					</field>
					<field id="free_from" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>free shipping from</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>cart value including tax in €</comment>
					</field>
					<field id="name" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Method Name</label>
					</field>
					<field id="sallowspecific" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Applicable Countries</label>
						<frontend_class>shipping-applicable-country</frontend_class>
						<source_model>Magento\Shipping\Model\Config\Source\Allspecificcountries</source_model>
					</field>
					<field id="specificcountry" translate="label" type="multiselect" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Allowed For Specific Countries</label>
						<source_model>Magento\Directory\Model\Config\Source\Country</source_model>
						<can_be_empty>1</can_be_empty>
					</field>
				</group>

				<group id="shipping_date" type="label" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Shipping Date</label>
					<field id="same_day_cutoff_1" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Monday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_2" translate="label" type="text" sortOrder="11" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Tuesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_3" translate="label" type="text" sortOrder="12" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Wednesday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_4" translate="label" type="text" sortOrder="13" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Thursday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_5" translate="label" type="text" sortOrder="14" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Friday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_6" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Saturday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="same_day_cutoff_7" translate="label" type="text" sortOrder="16" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Same Day Cutoff Sunday</label>
						<comment>Format: hh:mm</comment>
					</field>
					<field id="delay" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Delay</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
						<comment>Shipping Delay in days</comment>
					</field>
					<field id="days_on_road" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Days on road</label>
						<can_be_empty>0</can_be_empty>
						<validate>validate-number validate-zero-or-greater</validate>
					</field>
					<field id="no_receiving_weekdays" translate="label" type="multiselect" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Weekdays without Delivery from this carrier</label>
						<comment>All days allowed if empty</comment>
						<source_model>Magento\Config\Model\Config\Source\Locale\Weekdays</source_model>
					</field>
					<field id="text_if_today" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping today)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_tomorrow" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping tomorrow)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime" translate="label" type="textarea" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_anytime_delay_set" translate="label" type="textarea" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (Shipping another day - delay is set)</label>
						<comment>html allowed - vars: {$shippingDate},{$receivingDate},{$cutoffDate},{$timeToOrder}</comment>
					</field>
					<field id="text_if_not_in_stock" translate="label" type="textarea" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
						<label>Text (if not in stock)</label>
						<comment>html allowed - NO vars!</comment>
					</field>
				</group>
			</group>

		</section>
	</system>
</config>
