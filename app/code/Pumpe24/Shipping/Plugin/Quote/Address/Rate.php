<?php
namespace Pumpe24\Shipping\Plugin\Quote\Address;

class Rate
{
	public function afterImportShippingRate($subject, $result, $rate)
	{
		if ($rate instanceof \Magento\Quote\Model\Quote\Address\RateResult\Method) {
			$result->setP24PricePraefix($rate->getP24PricePraefix());
			$result->setP24ShippingDateInfo($rate->getP24ShippingDateInfo());
			$result->setP24InfoOverlay($rate->getP24InfoOverlay());
		}
		return $result;
	}
}
?>
