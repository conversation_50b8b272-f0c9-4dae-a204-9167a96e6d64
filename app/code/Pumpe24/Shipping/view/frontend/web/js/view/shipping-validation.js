define(
	[
		"uiComponent",
		"Magento_Checkout/js/model/shipping-rates-validator",
		"Magento_Checkout/js/model/shipping-rates-validation-rules",
		"../model/shipping-validator",
		"../model/shipping-validation-rules"
	],
	function (
		Component,
		defaultShippingRatesValidator,
		defaultShippingRatesValidationRules,
		shippingRatesValidator,
		shippingRatesValidationRules
	) {
		"use strict";
		defaultShippingRatesValidator.registerValidator("pumpe24Dachser", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24Dachser", shippingRatesValidationRules);
		defaultShippingRatesValidator.registerValidator("pumpe24Dpd", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24Dpd", shippingRatesValidationRules);
		defaultShippingRatesValidator.registerValidator("pumpe24DGls", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24Gls", shippingRatesValidationRules);
		defaultShippingRatesValidator.registerValidator("pumpe24Ups", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24Ups", shippingRatesValidationRules);
		defaultShippingRatesValidator.registerValidator("pumpe24Express12", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24Express12", shippingRatesValidationRules);
		defaultShippingRatesValidator.registerValidator("pumpe24Express18", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24Express18", shippingRatesValidationRules);
		defaultShippingRatesValidator.registerValidator("pumpe24ExpressSaturday", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24ExpressSaturday", shippingRatesValidationRules);
		defaultShippingRatesValidator.registerValidator("pumpe24Pickup", shippingRatesValidator);
		defaultShippingRatesValidationRules.registerRules("pumpe24Pickup", shippingRatesValidationRules);
		return Component;
	}
);
