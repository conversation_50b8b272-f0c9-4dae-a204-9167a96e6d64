<!--
/**
 * PAYONE Magento 2 Connector is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Lesser General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * PAYONE Magento 2 Connector is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU Lesser General Public License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with PAYONE Magento 2 Connector. If not, see <http://www.gnu.org/licenses/>.
 *
 * PHP version 5
 *
 * @category  Payone
 * @package   Payone_Magento2_Plugin
 * <AUTHOR> GmbH <<EMAIL>>
 * @copyright 2003 - 2017 Payone GmbH
 * @license   <http://www.gnu.org/licenses/> GNU Lesser General Public License
 * @link      http://www.payone.de
 */
-->
<div class="payment-method" data-bind="css: {'_active': (getCode() == isChecked())}">
    <div class="payment-method-title field choice">
        <input type="radio"
               name="payment[method]"
               class="radio"
               data-bind="attr: {'id': getCode()}, value: getCode(), checked: isChecked, click: selectPaymentMethod, visible: isRadioButtonVisible()"/>
        <label data-bind="attr: {'for': getCode()}" class="label">
		<span data-bind="text: getTitle()"></span>
		<div if="window.checkoutConfig.p24PaymentInfo.payone_payolution_installment" class="p24_info">
			<i class="p24_info_icon"></i>
			<div class="p24_info_box" bindHtml="window.checkoutConfig.p24PaymentInfo.payone_payolution_installment"></div>
		</div>
	</label>
	<em class="payment-icon ratenkauf" onclick="jQuery(this).parent().children('input')[0].click();"></em>
    </div>

    <div class="payment-method-content">
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <div class="payment-method-billing-address">
            <!-- ko foreach: $parent.getRegion(getBillingAddressFormName()) -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>

        <form action="#" method="post" data-bind="attr: {id: getCode() + '_form'}">
            <input type="hidden" name="payment[duration]" value="" data-bind="attr: {id: getCode() + '_duration'}">
            <fieldset data-bind="attr: {class: 'fieldset payment items ccard ' + getCode()}">
                <!-- ko if: requestBirthday() -->
                <div class="field number required" data-bind="attr: {id: getCode() + '_birthday_field'}">
                    <label data-bind="attr: {for: getCode() + '_birthday'}" class="label">
                        <span><!-- ko i18n: 'Birthday'--><!-- /ko --></span>
                    </label>
                    <div class="control">
                        <input type="text" name="payment[birthday]" class="input-text" value=""
                               style="width:auto;margin-right:10px;"
                               size="3" maxlength="2"
                               data-bind="attr: {
                                    autocomplete: off,
                                    id: getCode() + '_birthday',
                                    title: $t('Day of birth'),
                                    'data-container': getCode() + '-birthday'},
                              value: birthday,
                              valueUpdate: 'keyup' "/>
                        <input type="text" name="payment[birthmonth]" class="input-text" value=""
                               style="width:auto;margin-right:10px;"
                               size="3" maxlength="2"
                               data-bind="attr: {
                                    autocomplete: off,
                                    id: getCode() + '_birthmonth',
                                    title: $t('Month of birth'),
                                    'data-container': getCode() + '-birthmonth'},
                              value: birthmonth,
                              valueUpdate: 'keyup' "/>
                        <input type="text" name="payment[birthyear]" class="input-text" value=""
                               style="width:auto;margin-right:10px;"
                               size="8" maxlength="4"
                               data-bind="attr: {
                                    autocomplete: off,
                                    id: getCode() + '_birthyear',
                                    title: $t('Year of birth'),
                                    'data-container': getCode() + '-birthyear'},
                              value: birthyear,
                              valueUpdate: 'keyup' "/> <!-- ko i18n: '(DD.MM.YYYY)'--><!-- /ko -->
                    </div>
                </div>
                <!--/ko-->

                <div class="field" data-bind="attr: {id: getCode() + '_installmentplan'}" style="display:none;"></div>

                <div class="field number required" style="display:none;" data-bind="attr: {id: getCode() + '_iban_field'}">
                    <label data-bind="attr: {for: getCode() + '_iban'}" class="label">
                        <span><!-- ko i18n: 'IBAN'--><!-- /ko --></span>
                    </label>
                    <div class="control">
                        <input type="text" name="payment[iban]" class="input-text" value=""
                               data-bind="attr: {autocomplete: off, id: getCode() + '_iban', title: $t('IBAN'), 'data-container': getCode() + '-iban'},
                                          value: iban,
                                          valueUpdate: 'keyup' "/>
                    </div>
                </div>
                <div class="field number required" style="display:none;" data-bind="attr: {id: getCode() + '_bic_field'}">
                    <label data-bind="attr: {for: getCode() + '_bic'}" class="label">
                        <span><!-- ko i18n: 'BIC'--><!-- /ko --></span>
                    </label>
                    <div class="control">
                        <input type="text" name="payment[bic]" class="input-text" value=""
                               data-bind="attr: {autocomplete: off, id: getCode() + '_bic', title: $t('BIC'), 'data-container': getCode() + '-bic'},
                                          value: bic,
                                          valueUpdate: 'keyup' "/>
                    </div>
                </div>

                <input type="checkbox" data-bind="attr: {id: getCode() + '_additional_fields_agreement_checkbox'}, checked: agreement" />
                <label data-bind="attr: {for: getCode() + '_additional_fields_agreement_checkbox'}" class="required" style="float:none;">
			<span style="color:#e02b27; font-size:1.4rem;"> *</span>
                    <!-- ko i18n: 'I agree with the transmission of the necessary data to payolution which is needed for processing the purchase, the identity-check and the credit rating.'--><!-- /ko --><br>
                    <!-- ko i18n: 'My'--><!-- /ko -->
                    <a href="#" style="float:none; margin:0;" data-bind="click: displayPayolutionOverlay"><!-- ko i18n: 'acceptance'--><!-- /ko --></a>
                    <!-- ko i18n: 'can be revoked for the future any time.'--><!-- /ko -->
                </label>
                <div data-bind="attr: {id: getCode() + '_overlay'}" style="display:none;" class="payolution_overlay">
                    <a href="#" data-bind="click: removePayolutionOverlay"
                       style="float:right;font-weight:bold;"><!-- ko i18n: 'Close window'--><!-- /ko --></a><br><br>
                    <div data-bind="html: getPrivacyDeclaration()"></div>
                </div>
            </fieldset>
        </form>

        <p data-bind="html: getInstructions()"></p>
        <div class="checkout-agreements-block" data-bind="attr: {id: getCode() + '_agreements'}">
            <!-- ko foreach: $parent.getRegion('before-place-order') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </div>
        <div data-role="checkout-payone-boni-agreement">
            <div class="checkout-payone-boni-agreement">
                <!-- ko if: (isAgreementVisible()) -->
                    <!-- ko if: (canShowPaymentHintText()) -->
                        <div>
                            <strong><span data-bind="html: getPaymentHintText()"></span></strong>
                        </div>
                    <!-- /ko -->
                    <!-- ko if: (canShowAgreementMessage()) -->
                        <div>
                            <input type="checkbox"
                                   data-bind="attr: {
                                                        'id': 'payone_boni_agreement_' + getCode(),
                                                        'name': 'payone_boni_agreement',
                                                        'value': '1'}" />
                            <label data-bind="attr: {'for': 'payone_boni_agreement_' + getCode()}">
                                <span data-bind="html: getAgreementMessage()"></span>
                            </label>
                        </div>
                    <!-- /ko -->
                <!-- /ko -->
            </div>
        </div>
        <div class="actions-toolbar">
            <div class="primary">
                <button class="action primary" type="button"
                        data-bind="
                        click: handleInstallment,
                        attr: {id: getCode() + '_check', title: $t('Check installment availability')},
                        enable: (getCode() == isChecked())">
                    <span data-bind="i18n: 'Check installment availability'"></span>
                </button>
                <button class="action primary checkout" type="submit" style="display:none;"
                        data-bind="
                        click: placeOrder,
                        attr: {id: getCode() + '_submit', title: $t('Place Order')},
                        enable: (getCode() == isChecked()),
                        css: {disabled: !isPlaceOrderActionAllowed()}
                        "
                        disabled>
                    <span data-bind="i18n: 'Place Order'"></span>
                </button>
            </div>
        </div>
    </div>
</div>
