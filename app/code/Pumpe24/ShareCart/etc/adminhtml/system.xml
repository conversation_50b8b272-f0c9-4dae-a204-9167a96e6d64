<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
<system>
	<tab id="pumpe24" translate="label" sortOrder="9999">
		<label>Pumpe24</label>
	</tab>
	<section id="shareCart" translate="label" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
		<class>separator-top</class>
		<label>Share Cart</label>
		<tab>pumpe24</tab>
		<resource>Pumpe24_ShareCart::configuration</resource>
		<group id="main" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
			<label>Main</label>
			<field id="active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Active</label>
				<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
			</field>
			<field id="clearCart" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>Clear Cart before load</label>
				<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
			</field>
			<field id="addCampaign" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>send campaign to matomo</label>
			</field>
			<field id="addCampaignIps" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
				<label>campaign ips</label>
				<comment>send campaign only on carts generated by this ips ... comma-separated list of ip adresses or subnets (v4/v6) which will add the campaign - everytime empty</comment>
			</field>
		</group>
	</section>
</system>
</config>
