<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Magento\Framework\View\Element\Message\MessageConfigurationsPool">
		<arguments>
			<argument name="configurationsMap" xsi:type="array">
				<item name="shareCartSuccessMessage" xsi:type="array">
					<item name="renderer" xsi:type="const">\Magento\Framework\View\Element\Message\Renderer\BlockRenderer::CODE</item>
					<item name="data" xsi:type="array">
						<item name="template" xsi:type="string">Pumpe24_ShareCart::share_cart_success_message.phtml</item>
					</item>
				</item>
			</argument>
		</arguments>
	</type>
</config>
