<?php
namespace Pumpe24\Matomo\Model;

class Tracker {

	protected $_quoteFactory;
	protected $_helper;
	protected $_remoteAddress;
	protected $_session;
	protected $_matomo;

	public function __construct(
		\Magento\Quote\Model\QuoteFactory $quoteFactory,
		\Pumpe24\Matomo\Helper\Data $helper,
		\Magento\Framework\HTTP\PhpEnvironment\RemoteAddress $remoteAddress,
		\Magento\Framework\Session\SessionManagerInterface $session
	) {
		try {
			$this->_quoteFactory = $quoteFactory;
			$this->_helper = $helper;
			$this->_remoteAddress = $remoteAddress;
			$this->_session = $session;
			$this->_session->start();

			require_once(__DIR__ . "/../lib/MatomoTracker.php");
			$this->_matomo = new \MatomoTracker(
				$this->_helper->readModuleConfig("site_id"),
				$this->_helper->readModuleConfig("hostname")
			);
			$this->_matomo->disableCookieSupport();
			$this->_matomo->setRequestTimeout(1);

			$authToken = $this->_helper->readModuleConfig("auth_token");
			if ($authToken)
				$this->_matomo->setTokenAuth($authToken);

			$this->_matomo->setIp($this->_remoteAddress->getRemoteAddress());

		} catch (\Exception $e) {
		} catch (\Throwable $e) {
		}
	}

	public function trackPageView($title = "") {
		try {
			$resolution = $this->_session->getResolution();
			if ($resolution)
				$this->_matomo->setResolution($resolution["w"], $resolution["h"]);

			$this->_matomo->doTrackPageView($title);
		} catch (\Exception $e) {
		} catch (\Throwable $e) {
		}
	}

	public function trackEcommerceView($sku, $productName, $categoryName, $price) {
		try {
			$title = ($productName ? $productName : $categoryName);
			$this->_matomo->setEcommerceView($sku, $productName, $categoryName, $price);
			$this->trackPageView($title);
		} catch (\Exception $e) {
		} catch (\Throwable $e) {}
	}

	public function trackSearch($query, $resultsCount) {
		try {
			$this->_matomo->doTrackSiteSearch($query, false, $resultsCount);
		} catch (\Exception $e) {
		} catch (\Throwable $e) {}
	}

	public function trackCart($cart) {
		try {
			$this->_addQuoteItems($cart->getQuote());
			$this->_matomo->doTrackEcommerceCartUpdate($cart->getQuote()->getGrandTotal());
		} catch (\Exception $e) {
		} catch (\Throwable $e) {}
	}

	public function trackCheckoutSuccess($orderIds, $order) {
		try {
			$quote = $this->_quoteFactory->create()->load($order->getQuoteId());
			$this->_addQuoteItems($quote);
			$this->_matomo->doTrackEcommerceOrder(
				$orderIds[0],
				$order->getGrandTotal(),
				$order->getBaseSubTotal(),
				$order->getTaxAmount(),
				$order->getBaseShippingAmount(),
				$order->getDiscountAmount()
			);
		} catch (\Exception $e) {
		} catch (\Throwable $e) {}
	}

	public function trackCampaign($campaign) {
		try {
			$url = $this->_matomo->getUrlTrackPageView();
			if (strpos($url, "?") !== false)
				$url .= "&";
			else
				$url .= "?";
			$url .= "pk_campaign=" . $campaign;
			$this->_matomo->setUrl($url);
			$this->trackPageView();
		} catch (\Exception $e) {
		} catch (\Throwable $e) {}
	}

	protected function _addQuoteItems($quote) {
		try {
			foreach ($quote->getAllItems() as $i) {
				if ($i->getProductType() != "simple")
                	                continue;
				$parent = $i->getParentItem();
				if ($parent) {
					$price = $parent->getPrice();
					$qty = $parent->getQty();
				} else {
					$price = $i->getPrice();
					$qty = $i->getQty();
				}

				$this->_matomo->addEcommerceItem($i->getSku(), $i->getName(), false, $price, $qty);
			}
		} catch (\Exception $e) {
		} catch (\Throwable $e) {}
	}
}
?>
