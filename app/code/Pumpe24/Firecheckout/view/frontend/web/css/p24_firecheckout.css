html {
font-size:100%;
}

body {
font-size:1rem;
}

.block {
margin:0;
}

.header-wrapper .search-icon {
border:0;
border-radius:0;
}

.page-wrapper {
min-height:unset;
}

.header-logo { margin-bottom: 4px;}

.header-wrapper .icon-and-text-cart, .header-wrapper .search-container, .header-wrapper #search-button-container-main, .header-wrapper .buttons-wrapper #account-login{
  display: none;
}

.header-wrapper button:hover, .header-wrapper button:focus {
  background-color: rgb(255,255,255); border: 0;
}

.header-wrapper a:hover {
text-decoration:none;
}

.navigation a:visited {
color:rgb(55,65,81);
}

.navigation .container {
width:100%;
max-width:unset;
padding:0 !important;
}

.header .logo-wrapper {
height:auto;
padding-bottom:4px;;
}

.header .logo-wrapper img {
height:64px;
width:auto;
}

.page-footer {
background:none;
}

.footer.content {
max-width:unset;
padding:0 !important;
}

.footer.content a:visited,
.footer.content a:hover {
color:rgb(255,255,255);
}

#contact input {
line-height:1.5rem;
padding:.5rem .75rem;
height:auto;
font-size:1rem;
}

#contact textarea {
padding: .5rem .75rem;
}

#contact select {
height:auto;
}

#contact textarea,
#contact select
#contact input[type="text"] {
border-color: rgb(156, 163, 175);
}

#contact textarea:focus,
#contact select:focus,
#contact input:focus {
box-shadow:none;
}

#contact .btn {
margin-top:0.15rem
}
#contact .block {
margin-bottom:0;
}

#contact .btn.block:focus {
background-color:rgb(0,72,147);
color:rgb(255,255,255);
}

#contact h4 {
line-height:1.5rem;
}

.firecheckout #checkout .opc-wrapper .step-title::before,
.firecheckout #checkout .opc-block-summary > .title::before,
.firecheckout #checkout .opc-sidebar .step-title::before {
display:flex;
justify-content:center;
}

.firecheckout #checkout .opc-wrapper .opc .checkout-left-wrapper {
margin:0 !important;
padding:0 !important;
border:0 !important;
box-shadow:none !important;
}

.firecheckout #maincontent {
  padding-left: 0;
  padding-right: 0;
}
.firecheckout #maincontent .page-title-wrapper{
  padding: 0 1.5rem;
}

.firecheckout #checkout .opc-wrapper .opc .checkout-left-wrapper > ul > li {
width:100%;
margin-bottom:5px;
padding:25px;
border:1px solid rgb(242,245,247);
border-radius:4px;
box-sizing:border-box;
box-shadow:0 10px 20px -5px rgba(0,0,0,0.05);
}

.firecheckout input[type="email"],
.firecheckout input[type="text"],
.firecheckout input[type="password"] {
font-size:16px;
}

.p24_advantages h3 {
font-size:1.8rem;
color:rgb(51,51,51);
font-weight:600;
padding: 0 0 15px;
}

.firecheckout-content-below-order-summary a,
.firecheckout-content-below-order-summary a:visited {
color:rgb(1, 104, 179);
}

.firecheckout-content-below-order-summary a:hover {
color:rgb(0,72,147);
}

.opc-wrapper .form-shipping-address,
.opc-wrapper .form-login,
.opc-wrapper .methods-shipping {
background-color:unset !important;
}

.opc-wrapper .form-login {
padding-left:0 !important;
padding-right:0 !important;
}

.firecheckout #checkout .opc-wrapper .step-title,
.firecheckout #checkout .opc-block-summary > .title,
.firecheckout #checkout .opc-sidebar .step-title {
font-size:1.8rem !important;
}

.firecheckout #checkout .opc-wrapper .step-title::before,
.firecheckout #checkout .opc-block-summary > .title::before,
.firecheckout #checkout .opc-sidebar .step-title::before {
color:rgb(149, 195, 29) !important;
border-color:rgb(149, 195, 29) !important;
}

.firecheckout .minicart-items-wrapper {
max-height:unset !important;
}

.product-item {
font-size:1rem !important;
}

.opc-block-summary .product-item .price {
font-size:1.2rem !important;
}

.opc-block-summary .table-totals .grand .amount,
.opc-block-summary .table-totals .grand .mark {
font-size:1.4rem;
}

.firecheckout .items-in-cart > .minicart-items::after {
background:none !important;
}

.firecheckout .items-in-cart > .title {
cursor:default;
}

.firecheckout .items-in-cart > .title::after {
display:none !important;
}

.firecheckout .opc-block-summary .cart-header {
display:none;
}

.firecheckout .opc-block-summary .product-item {
padding:0 !important;
}

.firecheckout .opc-block-summary .product-item-details {
padding:0.5rem 0;
display:grid;
grid-template-columns:auto;
}

.firecheckout .opc-block-summary .product-item .product-item-details::before {
display:none !important;
}

.firecheckout .opc-block-summary .product-image {
display:flex;
justify-content:center;
align-items:center;
grid-row: span 1/span 1;
}

.firecheckout .opc-block-summary .product-item .product-image img {
width:5rem !important;
}

.firecheckout .opc-block-summary .product-prices {
grid-column: 1;
width:100%;
display:grid;
grid-template-columns:repeat(3, minmax(0,1fr));
margin-top:0.5rem;
}

.firecheckout .opc-block-summary .product-name {
font-weight:600;
grid-column:1;
display:flex;
align-items:center;
}

.firecheckout .opc-block-summary .cart-header .right,
.firecheckout .opc-block-summary .cart-header .right-p {
text-align:right;
}

.firecheckout .opc-block-summary .cart-header .right-p {
padding-right:0.5rem;
}

.firecheckout .opc-block-summary .product-prices > div {
display:flex;
flex-direction:column;
}

.firecheckout .opc-block-summary .product-prices .product-price,
.firecheckout .opc-block-summary .product-prices .product-qty,
.firecheckout .opc-block-summary .product-prices .product-total {
display:flex;
align-items:center;
}

.firecheckout .opc-block-summary .product-prices span.data {
margin-top:0.25rem;
}

.firecheckout .opc-block-summary .product-prices .product-price span {
width:100%;
text-align:left;
}

.firecheckout .opc-block-summary .product-prices .product-qty span {
width:100%;
text-align:center;
}

.firecheckout .opc-block-summary .product-prices .product-total span {
width:100%;
text-align:right;
}

.firecheckout .opc-block-summary .product-prices span.title {
margin:0;
}

.firecheckout .opc-block-summary .table-totals .grand.totals.incl td {
padding-top:1rem;
border:0;
padding-bottom:0;
}

.firecheckout .opc-block-summary .totalHolder {
display:flex;
}

.firecheckout .opc-block-summary .totalHolder .mark {
flex:auto;
}

.firecheckout .opc-block-summary .totals-tax td {
border:0;
padding:0 0 1rem 0;
font-size:0.9rem;
}

.firecheckout .p24_shipping_comment {
margin-left:30px;
}

.firecheckout .shipping-address-item {
padding:15px !important;
}

.firecheckout .edit-address-link,
.firecheckout .action-select-shipping-item {
line-height:initial !important;
width:100% !important;
position:relative !important;
right:0 !important;
}

.firecheckout .modal-slide .modal-content {
padding-bottom: 0 !important;
}

@media all and (min-width:640px) {
  .firecheckout .opc-block-summary .product-item-details {
  grid-template-columns:repeat(5, minmax(0,1fr));
  }
  .firecheckout .opc-block-summary .product-image {
  grid-row: span 2/span 2;
  padding-right:0.5rem;
  }
  .firecheckout .opc-block-summary .product-prices {
  grid-column:span 4/span 4;
  }
  .firecheckout .opc-block-summary .product-name {
  grid-column:span 4/span 4;
  }

}

@media all and (min-width:768px) {
  .firecheckout #maincontent {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .firecheckout #maincontent .page-title-wrapper{
    padding: 0;
  }
  .header-wrapper .buttons-wrapper {
    grid-column: span 2 / span 2;
  }
  .firecheckout.firecheckout-col2-set #checkout .opc-sidebar {
  padding-right:10px;
  }
  .firecheckout .opc-block-summary > .place-order.last .actions-toolbar, .firecheckout .opc-block-summary > .place-order:last-child .actions-toolbar {
  margin-right: -10px !important;
  }
  .firecheckout .opc-block-summary .product-item-details {
  grid-template-columns:auto;
  }
  .firecheckout .opc-block-summary .product-image {
  grid-row: 1;
  padding-right:0;
  }
  .firecheckout .opc-block-summary .product-prices {
  grid-column:1;
  }
  .firecheckout .opc-block-summary .product-name {
  grid-column:1;
  }
}

@media all and (min-width:1024px) {
  .firecheckout.firecheckout-col2-set #checkout .opc-sidebar {
  padding-right:25px;
  }
  .firecheckout .opc-block-summary > .place-order.last .actions-toolbar, .firecheckout .opc-block-summary > .place-order:last-child .actions-toolbar {
  margin-right: -25px !important;
  }
  .firecheckout .opc-block-summary .product-item-details {
  grid-template-columns:repeat(5, minmax(0,1fr));
  }
  .firecheckout .opc-block-summary .product-image {
  grid-row: span 2/span 2;
  padding-right:0.5rem;
  }
  .firecheckout .opc-block-summary .product-prices {
  grid-column:span 4/span 4;
  }
  .firecheckout .opc-block-summary .product-name {
  grid-column:span 4/span 4;
  }
}

@media all and (min-width:1428px) {
  .firecheckout .opc-block-summary .cart-header {
  font-weight:600;
  border-bottom:1px solid #f4f4f4;
  }
  .firecheckout .opc-block-summary .cart-header,
  .firecheckout .opc-block-summary .product-item-details {
  display:grid;
  grid-template-columns:repeat(8, minmax(0,1fr));
  }
  .firecheckout .opc-block-summary .cart-header .header-product-name {
  grid-column:span 4/span 4;
  }
  .firecheckout .opc-block-summary .product-item .product-image {
  grid-row:1;
  }
  .firecheckout .opc-block-summary .product-item .product-name {
  font-weight:normal;
  }
  .firecheckout .opc-block-summary .product-prices {
  grid-column: span 3/span 3;
  margin-top:0;
  }
  .firecheckout .opc-block-summary .product-prices > div {
  flex-direction:row;
  }
  .firecheckout .opc-block-summary .product-prices span.title {
  display:none;
  }
  .firecheckout .opc-block-summary .product-prices span.data {
  width:100%;
  text-align:right;
  margin-top:0;
  }

  .firecheckout .opc-block-summary .totalHolder {
  margin-left:50%;
  }
}


.actions-toolbar {
border:0;
padding:0;
margin:0;
}


.firecheckout.firecheckout-col2-set #checkout .opc-wrapper .opc > li, .firecheckout.firecheckout-col2-set #checkout .opc-sidebar {
margin-bottom:5px;
}

.payment-methods {
padding-top:0;
padding-bottom:0;
}

.payment-methods .p24_info {
margin-left:0.5rem;
}

.firecheckout .checkout-payment-method .payment-method .payment-method-title {
padding-top:0 !important;
padding-bottom:0 !important;
min-height:40px;
display:grid;
grid-template-columns:30px auto 3.5rem;
}

.firecheckout .checkout-payment-method .payment-method .payment-method-content {
padding-left:30px;
}

.firecheckout #checkout .checkout-payment-method .payment-method .payment-method-title > input[type="radio"] {
margin-right:13px;
}

.firecheckout .checkout-payment-method .opc-payment {
margin-bottom:0;
}

#co-payment-form > fieldset {
margin-bottom:0;
}
#co-payment-form > fieldset > br {
display:none;
}

.p24-fc-hide-me {
display:none !important;
}

.p24-fc-width-100 {
width:100% !important;
}

#customer-email-fieldset .note {
display:none !important;
}

.payment-methods a,
.payment-methods a:visited {
color:rgb(1, 104, 179) !important;
}

#payone_payolution_installment_submit {
display:none !important;
}

.payment-method .actions-toolbar .primary {
float:left;
}

#payone_payolution_installment_check {
display:block;
float:left;
margin:0;
padding:0.5rem;
font-size:18px;
border-color:rgb(149, 195, 29);
color:rgb(149, 195, 29);
}

#payone_payolution_installment_check:hover {
background-color:rgb(149, 195, 29);
color:rgb(255,255,255);
}

.firecheckout #checkout .payment-method input:not([type="checkbox"]):not([type="radio"]):not([type="image"]) {
height:32px;
padding: 0 7px;
}

#payone_payolution_installment_birthday_field .control span:before,
.payone_payolution_invoice .control span:before {
content: ' ';
display: block;
clear:right;
}

.firecheckout .payment-method .fieldset div.field,
.firecheckout .payment-method .actions-toolbar {
padding-left: 0;
}

.firecheckout .payment-method .fieldset div.field {
padding-top: 0;
}

.firecheckout .payment-method .fieldset div.field > .label {
margin-bottom:0;
}

.firecheckout .checkout-payment-method .ccard.fieldset > .field .fields.group.group-2 .field.month,
.firecheckout .checkout-payment-method .ccard.fieldset > .field .fields.group.group-2 .field + .field.year {
margin-right: 0;
padding: 0;
}

.firecheckout .checkout-payment-method .ccard {
max-width:100% !important;
}

#payone_creditcard_cc_type_exp_div,
#payone_creditcard_cc_type_cvv_div {
width:100%;
max-width:260px;
}

.firecheckout .checkout-payment-method .ccard .cvv > .control {
width:100% !important;
}

#payone_payolution_installment_installmentplan > div:nth-of-type(1) {
clear:both;
}

.payolution_installment_overview td {
padding:0;
}
.payolution_installment_overview table tr:nth-of-type(3) td {
font-weight:bold;
}

#payone_payolution_installment_submit {
margin:0;
}

.firecheckout .place-order .actions-toolbar .primary .action {
color:#ffffff;
border-color:rgb(149, 195, 29) !important;
background-color:rgb(149, 195, 29) !important;
}

.firecheckout .place-order .actions-toolbar .primary .action:hover {
border-color:rgb(0,72,147) !important;
background-color:rgb(0,72,147) !important;
}

.opc-block-summary .table-totals tbody .mark,
.opc-block-summary .table-totals tbody .amount,
.opc-block-summary .table-totals tbody .grand .amount,
.cart-totals .grand strong, .opc-block-summary .table-totals .grand strong
{
padding:0;
}

.opc-block-summary .not-calculated {
color:#ff0000;
}

.totals-tax {
font-style: italic;
}

@media (min-width: 769px) {
.header.content, .navigation, .container {
padding-left:2rem !important;
padding-right:2rem !important;
}
}

.firecheckout .checkout-billing-address .actions-toolbar .primary {
display:flex;
justify-content:normal !important;
width:100%;
}

.firecheckout .checkout-billing-address .actions-toolbar .primary button {
width:100% !important;
display:flex;
justify-content:center;
align-items:center;
}

.firecheckout .checkout-billing-address .actions-toolbar .primary .action-update {
background-color:rgb(1, 104, 179) !important;
border-color:rgb(1, 104, 179) !important;
padding-left:0 !important;
padding-right:0 !important;
margin-left:6px !important;
}

.firecheckout .checkout-billing-address .actions-toolbar .primary .action-update:hover {
border-color:rgb(0,72,147) !important;
background-color:rgb(0,72,147) !important;
}

.firecheckout .checkout-billing-address .actions-toolbar .primary .action-cancel {
padding-top:13px;
padding-bottom:13px;
margin-left:0 !important;
margin-right:6px !important;
}

#df-mobile__searchbox__dfclassic:focus {
box-shadow:none;
}

.page-footer {
padding: 0 !important;
}

.footer.content {
max-width:100% !important;
background-color:#ffffff !important;
}

@media all and (min-width:1280px),print {
  .header.content, .navigation, .container, .page-main {
  max-width:1024px !important;
  }
}

@media all and (min-width:1428px),print {
  .header.content, .navigation, .container, .page-main {
  max-width:1428px !important;
  }
}

@media all and (min-width:1536px),print {
  .header.content, .navigation, .container, .page-main {
  max-width:1536px !important;
  }
}

@media all and (max-width:639px) {
  .firecheckout.fc-form-compact .form-shipping-address .fieldset div.field,
  .firecheckout.fc-form-compact #checkout .checkout-shipping-address .fieldset div.field,
  .firecheckout.fc-form-compact #checkout .checkout-billing-address .fieldset div.field {
  width:100% !important;
  }
}

@media all and (max-width:399px) {
  .firecheckout #checkout .opc-wrapper .step-title,
  .firecheckout #checkout .opc-block-summary > .title,
  .firecheckout #checkout .opc-sidebar .step-title {
  font-size:1.5rem !important;
  }
}

@media all and (max-width:767px) {
  .firecheckout #checkout .opc-block-summary > .place-order:last-child .actions-toolbar {
    margin-left: -15px; margin-right: -15px;
  }
}
