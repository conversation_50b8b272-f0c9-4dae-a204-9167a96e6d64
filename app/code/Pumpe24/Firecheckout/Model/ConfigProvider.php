<?php
namespace Pumpe24\Firecheckout\Model;

class ConfigProvider implements \Magento\Checkout\Model\ConfigProviderInterface {

	protected $_layout;
	protected $_p24Helper;
	protected $_scopeConfig;

	public function __construct(
		\Magento\Framework\View\LayoutInterface $layout,
		\Pumpe24\Helper\Helper\Data $p24Helper,
		\Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
	) {
		$this->_layout = $layout;
		$this->_p24Helper = $p24Helper;
		$this->_scopeConfig = $scopeConfig;
	}

	public function getConfig() {
		return [
			"p24_advantages" => $this->_layout->createBlock("Magento\Cms\Block\Block")->setBlockId("checkout_advantages")->toHtml(),
			"p24_shipping_comment" => $this->_layout->createBlock("Magento\Cms\Block\Block")->setBlockId("shipping_comment")->toHtml(),
			"p24_eu_vat_info" => $this->_layout->createBlock("Magento\Cms\Block\Block")->setBlockId("eu_vat_info")->toHtml(),
			"p24_show_vat_info" => !$this->_p24Helper->isCustomerVatFree(),
			"p24_company_filter" => $this->_scopeConfig->getValue("p24Firecheckout/main/companyFilter", \Magento\Store\Model\ScopeInterface::SCOPE_STORE),
		];
	}
}
?>
