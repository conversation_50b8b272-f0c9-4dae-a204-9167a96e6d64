<?php
$order = $block->getOrder();
if ($order):
	$type = $block->getType();
	if ($type == "shipping")
		$address = $order->getShippingAddress();
	else
		$address = $order->getBillingAddress();
?>
	<?=(($address->getCompany()) ? $block->escapeHtml($address->getCompany()) . "<br />" : "")?>
	<?=$block->escapeHtml($address->getName())?><br />
	<?=$block->escapeHtml($address->getStreet()[0])?><br />
	<?=$block->escapeHtml($address->getCountryId() . "-" . $address->getPostcode() . " " . $address->getCity())?><br /><br />
	Telefon: <?=$block->escapeHtml($address->getTelephone())?>
<?php
endif;
?>
