<?php
namespace Pumpe24\Email\Block;

class InvoiceItems extends \Pumpe24\Email\Block\Order {

	protected $_productHelper;

	public function __construct(
		\Magento\Framework\View\Element\Template\Context $context,
		\Magento\Sales\Model\OrderFactory $orderFactory,
		\Pumpe24\Product\Helper\Data $productHelper,
		array $data = [])
	{
		parent::__construct($context, $orderFactory, $data);
		$this->_productHelper = $productHelper;
	}

	public function getStockStatusLabel($stockStatus) {
		$tmp = $this->_productHelper->getStockStatus($stockStatus);
		if ($tmp)
			return $tmp["label"];
		return "";
	}
}
