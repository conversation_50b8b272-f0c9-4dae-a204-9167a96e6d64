<?php

declare(strict_types=1);

namespace Pumpe24\Category\ViewModel;

use Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable\Product\Collection as linkCollection;
use Magento\Framework\Phrase;
use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Catalog\Model\CategoryRepository;
use Magento\Catalog\Model\Layer\Resolver;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Cms\Model\Template\FilterProvider;
use Magento\Framework\View\LayoutInterface;
use Magento\Catalog\Helper\Image;
use Magento\Widget\Model\Template\Filter;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Catalog\Model\Product\Visibility;
use Magento\Catalog\Model\Product\Attribute\Source\Status;


/**
 * Class CategoryData
 * @package Pumpe24\Category\ViewModel
 */
class CategoryData implements ArgumentInterface
{
    const CATEGORY_SLIDER_OPTIONS = [
        'slides_to_show' => 1.5,
        'slides_to_scroll' => 1,
        'slides_to_show_m' => 3.5,
        'slides_to_scroll_m' => 1,
        'slides_to_show_l' => 4.5,
        'slides_to_scroll_l' => 1,
        'autoplay' => false,
        'draggable' => true
    ];

    /**
     * @var CategoryRepository
     */
	protected $categoryRepository;

    /**
     * @var Resolver
     */
	private $layerResolver;

    /**
     * @var CategoryFactory
     */
    protected $categoryFactory;

    /**
     * @var Image
     */
    protected $imageHelper;

    /**
     * @var Filter
     */
    protected $widgetFilter;

    /**
     * @var CollectionFactory
     */
    protected $productCollectionFactory;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;
    private LayoutInterface $layout;
    private FilterProvider $filterProvider;
    /**
     * @var \Magento\Catalog\Api\Data\CategoryInterface|mixed|null
     */
    private mixed $currentCategory = null;
    private linkCollection $linkCollection;

    /**
     * CategoryData constructor.
     * @param CategoryRepository $categoryRepository
     * @param Resolver $layerResolver
     * @param StoreManagerInterface $storeManager
     * @param LayoutInterface $layout
     * @param FilterProvider $filterProvider
     * @param CategoryFactory $categoryFactory
     * @param Image $imageHelper
     * @param Filter $widgetFilter
     * @param CollectionFactory $productCollectionFactory
     */
	public function __construct(
		CategoryRepository $categoryRepository,
		Resolver $layerResolver,
        StoreManagerInterface $storeManager,
        LayoutInterface $layout,
        FilterProvider $filterProvider,
        CategoryFactory $categoryFactory,
        Image $imageHelper,
        Filter $widgetFilter,
        CollectionFactory $productCollectionFactory,
        linkCollection $linkCollection
	) {
		$this->categoryRepository = $categoryRepository;
		$this->layerResolver = $layerResolver;
        $this->storeManager = $storeManager;
        $this->layout = $layout;
        $this->filterProvider = $filterProvider;
        $this->categoryFactory = $categoryFactory;
        $this->imageHelper = $imageHelper;
        $this->widgetFilter = $widgetFilter;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->linkCollection = $linkCollection;
    }

    /**
     * @param $catId
     * @return mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
	public function getCategoryImageById($catId)
    {
		$category = $this->categoryRepository->get($catId);
		return $category->getImageUrl();
	}

    /**
     * @return false|string
     */
	public function getCurrentCategoryIndex()
    {
		$cat = $this->getCategory();
		if ($cat)
			return "category-node-" . $cat->getId();
		return false;
	}

    /**
     * @return string
     */
	public function getCurrentCategoryName()
    {
		$cat = $this->getCategory();
		if (($cat) && ($cat->getId() > 2))	//don't ouput default category
			return $cat->getName();

		return "";
	}

    /**
     * @return false|mixed
     */
	public function getCurrentCategoryId()
    {
		$cat = $this->getCategory();
		if ($cat)
			return $cat->getId();
		return false;
	}

    /**
     * @return false|mixed
     */
    public function getParentCategoryId()
    {
        $cat = $this->getCategory();
        if ($cat)
            return $cat->getParentId();

        return false;
    }

    /**
     * @return false|mixed
     */
    public function getCategoryLevel()
    {
        $cat = $this->getCategory();
        if ($cat)
            return $cat->getLevel();

        return false;
    }

    /**
     * @param $type
     * @return false|mixed
     */
	public function contentRenderer($type = "category_top", $additionalType = "desktop")
    {
        $html = false;
        $currentCategory = $this->getCategory();
        if ($currentCategory) {
            $content = trim((string)$currentCategory->getData($type . "_" . $additionalType));
            if ($content) {
                $html =  $this->filterProvider->getBlockFilter()->filter($content);
            }
            if(!$html){
                $blockId = $type . '_' . $currentCategory->getId();
                if($additionalType == "mobile"){
                    $blockId = "m" . $blockId;
                }
                $html = $this->layout
                    ->createBlock('Magento\Cms\Block\Block')
                    ->setBlockId($blockId)
                    ->toHtml();
            }
        }

        return $html;
    }

    /**
     * @param $parentCategoryId
     * @return false
     */
    public function firstLevelSliderWidgetRenderer($parentCategoryId)
    {
        $html = false;
        if ($parentCategoryId) {
            $html = $this->layout
                ->createBlock('Pumpe24\Category\Block\Widget\Category')
                ->setData(self::CATEGORY_SLIDER_OPTIONS)
                ->setData('id_path', 'category/' . $parentCategoryId)
                ->toHtml();
        }

        return $html;
    }

    public function getCurrentCategory(){

    }

    /**
     * @return \Magento\Catalog\Model\Category
     */
    public function getCategory(): \Magento\Catalog\Model\Category
    {
        if ($this->currentCategory === null) {
            $currentCategory = $this->layerResolver->get()->getCurrentCategory();
            if ($currentCategory) {
                $this->currentCategory = $this->categoryRepository->get($currentCategory->getId());
            }
        }
        return $this->currentCategory;
    }

    /**
     * @param $parentCategoryId
     * @return false
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getItems($parentCategoryId)
    {
        if ($parentCategoryId) {
            $categories = $this->categoryRepository->get($parentCategoryId)->getChildrenCategories();
            if(!is_array($categories)){
                $categories->addAttributeToSelect('image_url');
            }
            return $categories;
        }

        return false;
    }

    /**
     * @param $id
     * @return mixed
     */
    public function getItem($id)
    {
        return $this->categoryFactory->create()->load($id);
    }

    /**
     * @param $item
     * @return string
     */
    public function getCategorySlideImage($item)
    {
        $categoryImageUrl = $item->getImageUrl();
        if (!$categoryImageUrl || $categoryImageUrl == '') {
            $resultUrl = $this->imageHelper->getDefaultPlaceholderUrl('image');
        } else {
            $resultUrl = $categoryImageUrl;
        }

        return $resultUrl;
    }

    /**
     * @param $id
     * @return false|int
     */
    public function getProductCount()
    {
        return $this->layerResolver->get()->getProductCollection()->getSize();
    }

    /**
     * Including invisible products
     * @param $id
     * @return false|int
     */
    public function getAllProductCount()
    {
        $category = $this->getCategory();
        $categories = [$category->getId()];
        $childrenCategories = $category->getChildrenCategories();
        if (count($childrenCategories)) {
            $categories = array_merge($categories, array_map( function ($category) { return $category->getId();},is_array($childrenCategories) ? $childrenCategories : $childrenCategories->getItems()));
        }
        $productCollection = $this->getProductCollection($categories);
        $idArray = $productCollection->getAllIds();
        $this->linkCollection->getSelect()->where('link_table.parent_id in (?)', $idArray, \Zend_Db::INT_TYPE);
        $this->linkCollection->addAttributeToFilter('status',Status::STATUS_ENABLED);
        $idArray = array_merge($idArray,  $this->linkCollection->getAllIds());
        return count(array_unique($idArray));
    }

    /**
     * @param $categories array
     * @return \Magento\Catalog\Model\ResourceModel\Product\Collection
     */
    protected function getProductCollection($categories)
    {
        $productCollection = $this->productCollectionFactory->create();
        $productCollection->addAttributeToSelect('entity_id');
        $productCollection->addAttributeToFilter('visibility', ['nin' => [Visibility::VISIBILITY_IN_SEARCH]]);
        $productCollection->addAttributeToFilter('status',Status::STATUS_ENABLED);
        $productCollection->addCategoriesFilter(['in' => $categories]);

        return $productCollection;
    }
}
