<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Magento\Catalog\Model\CategoryRepository">
		<plugin name="pumpe24_category_category_repository" type="Pumpe24\Category\Plugin\Catalog\Model\CategoryRepository" sortOrder="10" />
	</type>
	<type name="Magento\Catalog\Model\Layer\Category\FilterableAttributeList">
		<plugin name="pumpe24_category_filterableattributelist" type="Pumpe24\Category\Plugin\Catalog\Model\FilterableAttributeList" sortOrder="10" />
	</type>
</config>
