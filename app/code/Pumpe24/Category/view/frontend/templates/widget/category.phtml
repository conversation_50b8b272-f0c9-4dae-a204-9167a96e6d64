<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Pumpe24\Category\ViewModel\Slider;
use Magento\Framework\Escaper;
use Pumpe24\Category\Block\Widget\Category;
use Pumpe24\Category\ViewModel\CategoryData as CategoryData;

/** @var Category $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */
/** @var Slider $sliderViewModel */
/** @var CategoryData $categoryDataViewModel */

$sliderViewModel = $viewModels->require(Slider::class);
$categoryDataViewModel = $viewModels->require(CategoryData::class);
$parentCategoryId = $block->getParentCategoryId();
$slidesCollection = $categoryDataViewModel->getItems($parentCategoryId);
$slideRenderer = 'Pumpe24_Category::widget/slide-renderer.phtml';
$sliderTemplate = 'Pumpe24_Category::widget/slider-template.phtml';
$widgetData = $block->getWidgetData();
?>

<div class="category-slider-widget container px-0 my-2">
    <?php if ($slidesCollection): ?>
        <?= $sliderViewModel->getSliderForItems($slideRenderer, $slidesCollection, $sliderTemplate, $widgetData)->toHtml(); ?>
    <?php else: ?>
        <div class="w-full mx-auto">
            <div class="text-center uppercase">
                <?= $block->escapeHtml(__('This category has no subcategories')) ?>
            </div>
        </div>
    <?php endif; ?>
</div>
