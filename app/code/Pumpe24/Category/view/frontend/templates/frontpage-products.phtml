<?php
$collections = $block->getFrontpageCollections();
?>

<?php if (sizeof($collections) >= 1): ?>
	<div class="container order-3 p24-frontpage-products">
		<?php if (sizeof($collections) == 1): ?>
			<?php $col = $collections[0] ?>
			<?php
				$productsBlock = $this->getChildBlock("product_list");
				$productsBlock->setCollection($col["collection"]);
			?>
			<?=$productsBlock->toHtml()?>
		<?php else: ?>
			<div x-data="{tab : 0}">
				<nav class="grid grid-cols-2 sm:flex sm:flex-wrap sm:border-b border-gray-300 text-xl lg:text-lg xl:text-xl">
					<?php $count = 0; ?>
					<?php foreach ($collections as $col): ?>
						<span class="text-gray-500 font-bold hover:text-black cursor-pointer border-gray-300 border-b sm:border-b-0"
							@click.prevent="tab = <?=$count?>"
						>
							<span class="inline-block py-2 sm:pb-1 sm:pt-0 sm:px-2 w-full text-center border-b-3 border-transparent<?=(($count == 0) ? " active text-black border-applegreen" : "")?>"
								:class="{'active text-black border-applegreen': tab === <?=$count?>}"
							>
								<?=$block->escapeHtml($col["name"])?>
							</span>
						</span>
						<?php $count++; ?>
					<?php endforeach; ?>
				</nav>
				<div class="grid">
					<?php $count = 0; ?>
					<?php foreach ($collections as $col): ?>
						<div class="bg-white transition-all ease-in duration-200"
							x-show="tab == <?=$count?>"
							x-transition:enter-start="opacity-0"
							x-transition:enter-end="opacity-100"
							x-transition:leave-start="opacity-100"
							x-transition:leave-end="opacity-0"
							style="grid-area: 1 / 1;<?=(($count >= 1) ? " display:none;" : "")?>"
						>
							<?php
								$productsBlock = $this->getChildBlock("product_list");
								$productsBlock->setCollection($col["collection"]);
							?>
							<?=$productsBlock->toHtml()?>
						</div>
						<?php $count++; ?>
					<?php endforeach; ?>
				</div>
			</div>
		<?php endif; ?>
	</div>
<?php endif; ?>

