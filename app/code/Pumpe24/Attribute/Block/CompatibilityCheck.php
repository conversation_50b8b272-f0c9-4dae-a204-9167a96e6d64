<?php
namespace Pumpe24\Attribute\Block;

class CompatibilityCheck extends  \Magento\Framework\View\Element\Template {

	protected $_productAttributeRepository;
	protected $_attributeOptionCollection;
	protected $_cart;
	protected $_helper;

	public function __construct(
		\Magento\Framework\View\Element\Template\Context $context,
		\Magento\Catalog\Model\Product\Attribute\Repository $productAttributeRepository,
		\Magento\Eav\Model\ResourceModel\Entity\Attribute\Option\CollectionFactory $attributeOptionCollection,
		\Magento\Checkout\Model\Cart $cart,
		\Pumpe24\Attribute\Helper\Data $helper
	) {
		parent::__construct($context);
		$this->_productAttributeRepository = $productAttributeRepository;
		$this->_attributeOptionCollection = $attributeOptionCollection;
		$this->_cart = $cart;
		$this->_helper = $helper;
	}

	protected function getLabels($attr) {
		$result = array();
		foreach ($attr as $optionId => $v) {
			$attributeOptionCollectionFactory = $this->_attributeOptionCollection->create();
			$optionData = $attributeOptionCollectionFactory
				->setPositionOrder('asc')
				->setIdFilter($optionId)
				->setStoreFilter()
				->load();
			$v = $optionData->getData();
			array_push($result, $v[0]["value"]);
		}
		return $result;
	}

	protected function getAttributeData($attributeCode) {
		$attr = $this->_productAttributeRepository->get($attributeCode);
		$result = array();
		$result["label"] = $attr->getStoreLabel();
		$result["p24_unit"] = $attr->getData("p24_unit");
		return $result;
	}

	public function formatValues($attr) {
		$result = "";
		$x = sizeof($attr["values"]);
		foreach ($attr["values"] as $v) {
			if ($result != "")
				$result .= ($x > 1 ? ", " : " " . __("and") . " ");
			$result .= $v . $attr["attribute"]["p24_unit"];
			$x--;
		}
		return $result;
	}

	public function getCartIncompatibilities() {
		$incompatibilities = $this->_helper->getCartIncompatibilities($this->_cart);
		if ($incompatibilities) {
			$result = array();
			foreach ($incompatibilities as $attrCode => $v) {
				$tmp = array();
				$tmp["attribute"] = $this->getAttributedata($attrCode);
				$tmp["values"] = $this->getLabels($v);
				array_push($result, $tmp);
			}
			return $result;
		} else
			return null;
	}
}

?>
