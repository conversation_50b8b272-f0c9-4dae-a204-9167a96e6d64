<?php
namespace Pumpe24\Attribute\Setup;

use \Magento\Framework\Setup\InstallSchemaInterface;
use \Magento\Framework\Setup\ModuleContextInterface;
use \Magento\Framework\Setup\SchemaSetupInterface;
use \Magento\Framework\DB\Ddl\Table;


class InstallSchema implements InstallSchemaInterface
{
	public function install(SchemaSetupInterface $setup, ModuleContextInterface $context) {
		$setup->startSetup();
		$connection = $setup->getConnection();

		$table = $setup->getTable("eav_attribute");
		$columns = [
			"p24_unit" => [
				"type" => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
				"LENGTH" => 255,
				"nullable" => false,
				"default" => "",
				"comment" => "pumpe24 attribute unit",
			],
			"p24_is_slider" => [
				"type" => \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
				"nullable" => false,
				"default" => "0",
				"comment" => "pumpe24 attribute slider",
			],
			"p24_incompatible_values" => [
				"type" => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
				"nullable" => false,
				"default" => "",
				"comment" => "pumpe24 attribute incompatible values",
			]

		];

		foreach ($columns as $name => $definition) {
			$connection->addColumn($table, $name, $definition);
		}

		$table = $setup->getTable("eav_attribute_option");
		$columns = [
			"p24_sort_order" => [
				"type" => \Magento\Framework\DB\Ddl\Table::TYPE_BIGINT,
				"unsigned" => true,
				"nullable" => false,
				"default" => "0",
				"comment" => "Sort Order as bigint by pumpe24"
			]
		];

		foreach ($columns as $name => $definition) {
			$connection->addColumn($table, $name, $definition);
		}

		$setup->endSetup();
	}
}
?>
