<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<type name="Magento\Catalog\Model\Product\Attribute\Repository">
		<plugin name="pumpe24_attribute_attribute_repository" type="Pumpe24\Attribute\Plugin\Catalog\Model\Product\Attribute\Repository" sortOrder="10" />
	</type>
	<type name="Magento\Catalog\Block\Adminhtml\Product\Attribute\Edit\Tab\Main">
		<plugin name="pumpe24_attribute_attribute_edit_form" type="Pumpe24\Attribute\Plugin\Catalog\Block\Adminhtml\Product\Attribute\Edit\Tab\Main" sortOrder="10"/>
	</type>
	<type name="Magento\Catalog\Helper\Output">
		<plugin name="pumpe24_attribute_helper_output" type="Pumpe24\Attribute\Plugin\Catalog\Helper\Output" sortOrder="10"/>
	</type>
	<type name="Magento\ConfigurableProduct\Model\ResourceModel\Attribute\OptionSelectBuilder">
		<plugin name="pumpe24_optionselectbuilder" type="Pumpe24\Attribute\Plugin\ConfigurableProduct\ResourceModel\OptionSelectBuilder" sortOrder="10"/>
	</type>
	<type name="Magento\Swatches\Block\LayeredNavigation\RenderLayered">
		<plugin name="pumpe24_swatches" type="Pumpe24\Attribute\Plugin\Swatches\Block\RenderLayered" sortOrder="10"/>
	</type>
	<type name="Magento\Eav\Model\Entity\Attribute\Source\Table">
		<plugin name="pumpe24_attribute_source_table" type="Pumpe24\Attribute\Plugin\Eav\Model\Entity\Attribute\Source\Table" sortOrder="10" />
	</type>
	<preference for="Magento\Catalog\Model\Product\Attribute\Repository" type="Pumpe24\Attribute\Model\Repository" />
	<preference for="Magento\CatalogSearch\Model\Layer\Filter\Attribute" type="Pumpe24\Attribute\Model\CatalogSearch\FilterAttribute" />
	<preference for="Magento\Eav\Model\ResourceModel\Entity\Attribute" type="Pumpe24\Attribute\Model\ResourceModel\Entity\Attribute" />
	<preference for="Magento\Catalog\Model\ResourceModel\Layer\Filter\Attribute" type="Pumpe24\Attribute\Model\ResourceModel\Layer\Filter\Attribute" />
</config>
