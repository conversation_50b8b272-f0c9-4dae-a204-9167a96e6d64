<?php
namespace Pumpe24\Attribute\Plugin\Catalog\Helper;

use Magento\Catalog\Model\Product as ModelProduct;

class Output {

	protected $_eavConfig;

	public function __construct(
	        \Magento\Eav\Model\Config $eavConfig
	) {
		$this->_eavConfig = $eavConfig;
	}

	public function afterProductAttribute(
		\Magento\Catalog\Helper\Output $subject,
		$result,
		$product,
		$attributeHtml,
		$attributeName
	) {
		$attribute = $this->_eavConfig->getAttribute(ModelProduct::ENTITY, $attributeName);

		$unit = $attribute->getData("p24_unit");
		if ($unit != "")
			$result .= "&nbsp;" . $unit;
		return $result;
	}
}
?>
