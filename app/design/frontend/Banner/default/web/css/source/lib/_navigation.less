// /**
//  * Copyright © Magento, Inc. All rights reserved.
//  * See COPYING.txt for license details.
//  */

//
//  Navigation
//  _____________________________________________

.lib-main-navigation(
    @_nav__indent-side: 15px,
    @_nav-background-color: @navigation__background,
    @_nav-border: @navigation__border,
    @_nav-level0-font-size: @navigation-level0-item__font-size,
    @_nav-level0-font-weight: @navigation-level0-item__font-weight,
    @_nav-level0-item-line-height: @navigation-level0-item__line-height,
    @_nav-level0-item-margin: @navigation-level0-item__margin,
    @_nav-level0-item-padding: @navigation-level0-item__padding,
    @_nav-level0-text-transform: @navigation-level0-item__text-transform,

    @_nav-level0-item-background-color: @navigation-level0-item__background,
    @_nav-level0-item-border: @navigation-level0-item__border,
    @_nav-level0-item-color: @navigation-level0-item__color,
    @_nav-level0-item-text-decoration: @navigation-level0-item__text-decoration,

    @_nav-level0-item-background-color-active: @navigation-level0-item__active__background,
    @_nav-level0-item__active__border-color: @navigation-level0-item__active__border-color,
    @_nav-level0-item__active__border-style: @navigation-level0-item__active__border-style,
    @_nav-level0-item__active__border-width: @navigation-level0-item__active__border-width,
    @_nav-level0-item-color-active: @navigation-level0-item__active__color,
    @_nav-level0-item-text-decoration-active: @navigation-level0-item__active__text-decoration,

    @_submenu-background-color: @submenu__background,
    @_submenu-border: @submenu__border,
    @_submenu-font-size: @submenu__font-size,
    @_submenu-font-weight: @submenu__font-weight,
    @_submenu-line-height: @submenu-item__line-height,
    @_submenu-item__padding-top: @submenu__padding-top,
    @_submenu-item__padding-right: @submenu__padding-right,
    @_submenu-item__padding-bottom: @submenu__padding-bottom,
    @_submenu-item__padding-left: @submenu__padding-left,

    @_submenu-item-background-color: @submenu-item__background,
    @_submenu-item-border: @submenu-item__border,
    @_submenu-item-color: @submenu-item__color,
    @_submenu-item-text-decoration: @submenu-item__text-decoration,

    @_submenu-item-background-color-active: @submenu-item__active__background,
    @_submenu-item__active__border: @submenu-item__active__border,
    @_submenu-item__active__border-color: @submenu-item__active__border-color,
    @_submenu-item__active__border-style: @submenu-item__active__border-style,
    @_submenu-item__active__border-width: @submenu-item__active__border-width,
    @_submenu-item-color-active: @submenu-item__active__color,
    @_submenu-item-text-decoration-active: @submenu-item__active__text-decoration
) {
    .navigation {
        box-sizing: border-box;
        padding: 0 !important;
        ul {
            margin: 0;
            padding: 0;
        }

        li {
            margin: 0;
        }

        a {
            display: block;
            .lib-css(padding-top, 5px);
            .lib-css(padding-right, 15px);
            .lib-css(padding-bottom, 5px);
            .lib-css(padding-left, @_submenu-item__padding-left);
            text-transform: uppercase;
            font-family: @font-family-proximaNova-regular;
            font-weight: 400;
        }

        a,
        a:hover {
            .lib-css(color, @_nav-level0-item-color);
            .lib-css(text-decoration, @_nav-level0-item-text-decoration);
        }

        .level0 {
            .lib-css(background, #f5f6fa);
            > .level-top {
                .lib-css(line-height, @_nav-level0-item-line-height);
                .lib-css(padding, 10px 15px);
                .lib-css(text-transform, @_nav-level0-text-transform);
                word-wrap: break-word;
                text-transform: uppercase;
                color: @color-link-red;
                font-family: @font-family-proximaNova-bold;
                font-weight: 600;
                text-align: center;
                &:hover {
                    .lib-css(color, @color-link-red);
                }
            }

            &.hidden-grid-breakpoint{
                display: none;
            }

            &.active {
                .all-category {
                    .ui-state-focus {
                        .lib-css(background, @_nav-level0-item-background-color-active);
                        .lib-css(border-color, @_nav-level0-item__active__border-color);
                        .lib-css(border-style, @_nav-level0-item__active__border-style);
                        .lib-css(border-width, @_nav-level0-item__active__border-width);
                        .lib-css(color, @_nav-level0-item-color-active);
                        .lib-css(padding-left, @_nav__indent-side - @_submenu-item__active__border);
                        .lib-css(text-decoration, @_nav-level0-item-text-decoration-active);
                        display: inline-block;
                    }
                }
            }

            > .level1 {
                .lib-css(font-weight, @font-weight__semibold);
            }

            &.active,
            &.has-active { // ToDo UI: remove "has_active" here, when mobile navigation default open state is implemented
                > a:not(.ui-state-active) {
                    .lib-css(background, @_nav-level0-item-background-color-active);
                    .lib-css(color, @_nav-level0-item-color-active);
                    .lib-css(text-decoration, @_nav-level0-item-text-decoration-active);
                }
            }
            &:nth-child(2n){
                .lib-css(background, white);
            }
        }

        .submenu {
            text-align: center;
            > li {
                word-wrap: break-word;
                > a {
                    &:hover {
                        .lib-css(color, @navigation-level0-item__hover__color);
                    }
                }
                &.all-category{
                    display: none;
                }
                .menu-icon{
                    display: none;
                }
            }

            &:not(:first-child) {
                .lib-css(background, @_submenu-background-color);
                .lib-css(border, @_submenu-border);
                .lib-css(font-size, @_submenu-font-size);
                .lib-css(font-weight, @_submenu-font-weight);
                .lib-css(line-height, @_submenu-line-height);
                left: auto !important;
                overflow-x: hidden;
                padding: 0;
                position: relative;
                top: auto !important;
                transition: left .3s ease-out;

                > li {
                    > a {
                        .lib-css(padding-left, @_nav__indent-side);
                    }

                    &:last-child {
                        margin-bottom: 0;
                    }
                }

                ul {
                    display: block;
                    .lib-css(padding-left, @_submenu-item__padding-left);

                    > li {
                        margin: 0;

                        a {
                            .lib-css(background, @_submenu-item-background-color);
                            .lib-css(border, @_submenu-item-border);
                            .lib-css(color, @_submenu-item-color);
                            .lib-css(text-decoration, @_submenu-item-text-decoration);
                            display: block;
                            line-height: normal;
                            &:hover {
                                .lib-css(color, @navigation-level0-item__hover__color);
                            }
                        }
                    }
                }

                &.expanded {
                    display: block !important;
                    padding-right: 0;
                    top: 0 !important;
                }

                .active {
                    > a {
                        .lib-css(color, @color-link-red);
                        .lib-css(text-decoration, @_submenu-item-text-decoration-active);
                    }
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.lib-main-navigation-desktop(
    @_nav-background-color: @navigation-desktop__background,
    @_nav-border: @navigation-desktop__border,
    @_nav-font-size: @navigation-desktop__font-size,
    @_nav-font-weight: @navigation-desktop__font-weight,

    @_nav-level0-item-line-height: @navigation-desktop-level0-item__line-height,
    @_nav-level0-item-margin: @navigation-desktop-level0-item__margin,
    @_nav-level0-item-padding: @navigation-desktop-level0-item__padding,

    @_nav-level0-item-background-color: @navigation-desktop-level0-item__background,
    @_nav-level0-item-border: @navigation-desktop-level0-item__border,
    @_nav-level0-item-color: @navigation-desktop-level0-item__color,
    @_nav-level0-item-text-decoration: @navigation-desktop-level0-item__text-decoration,

    @_nav-level0-item-background-color-hover: @navigation-desktop-level0-item__hover__background,
    @_nav-level0-item-border-hover: @navigation-desktop-level0-item__hover__border,
    @_nav-level0-item-color-hover: @navigation-desktop-level0-item__hover__color,
    @_nav-level0-item-text-decoration-hover: @navigation-desktop-level0-item__hover__text-decoration,

    @_nav-level0-item-background-color-active: @navigation-desktop-level0-item__active__background,
    @_nav-level0-item__active__border-color: @navigation-desktop-level0-item__active__border-color,
    @_nav-level0-item__active__border-style: @navigation-desktop-level0-item__active__border-style,
    @_nav-level0-item__active__border-width: @navigation-desktop-level0-item__active__border-width,
    @_nav-level0-item-color-active: @navigation-desktop-level0-item__active__color,
    @_nav-level0-item-text-decoration-active: @navigation-desktop-level0-item__active__text-decoration,

    @_submenu-background-color: @submenu-desktop__background,
    @_submenu-border-width: @submenu-desktop__border-width,
    @_submenu-border-style: @submenu-desktop__border-style,
    @_submenu-border-color: @submenu-desktop__border-color,
    @_submenu-box-shadow: @submenu-desktop__box-shadow,
    @_submenu-font-size: @submenu-desktop__font-size,
    @_submenu-font-weight: @submenu-desktop__font-weight,
    @_submenu-min-width: @submenu-desktop__min-width,
    @_submenu-padding: @submenu-desktop__padding,

    @_submenu-arrow: @submenu-desktop-arrow,
    @_submenu-arrow-size: @submenu-desktop-arrow__size,
    @_submenu-arrow-left: @submenu-desktop-arrow__left,

    @_submenu-item-padding: @submenu-desktop-item__padding,
    @_submenu-item-background-color: @submenu-desktop-item__background,
    @_submenu-item-border: @submenu-desktop-item__border,
    @_submenu-item-color: @submenu-desktop-item__color,
    @_submenu-item-text-decoration: @submenu-desktop-item__text-decoration,

    @_submenu-item__hover__background-color: @submenu-desktop-item__hover__background,
    @_submenu-item-border-hover: @submenu-desktop-item__hover__border,
    @_submenu-item-color-hover: @submenu-desktop-item__hover__color,
    @_submenu-item-text-decoration-hover: @submenu-desktop-item__hover__text-decoration,

    @_submenu-item-background-color-active: @submenu-desktop-item__active__background,
    @_submenu-item__active__border-color: @submenu-desktop-item__active__border-color,
    @_submenu-item__active__border-style: @submenu-desktop-item__active__border-style,
    @_submenu-item__active__border-width: @submenu-desktop-item__active__border-width,
    @_submenu-item-color-active: @submenu-desktop-item__active__color,
    @_submenu-item-text-decoration-active: @submenu-desktop-item__active__text-decoration
) {

    .navigation {
        .lib-css(background, @_nav-background-color);
        .lib-css(border, @_nav-border);
        .lib-css(font-size, @_nav-font-size);
        .lib-css(font-weight, @_nav-font-weight);
        height: inherit;
        left: auto;
        overflow: inherit;
        padding: 0;
        position: static;
        top: 0;
        width: 100%;
        z-index: 3;

        &:empty {
            display: none;
        }

        ul {
            margin-top: 0;
            margin-bottom: 0;
            padding: 0;
            position: static;
        }

        li.level0 {
            .lib-css(border-top, none);
        }

        li.level1 {
            position: relative;
        }

        .level0 {
            .lib-css(margin, 0);
            display: inline-block;
            position: static;

            &:last-child {
                margin-right: 0;
                padding-right: 0;
            }

            &:hover {
                &:after {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 0;
                    left: 100%;
                    width: 10px;
                    height: calc(100% + 3px);
                    z-index: 1;
                }
            }

            > .level-top {
                .lib-css(background, @_nav-level0-item-background-color);
                .lib-css(border, @_nav-level0-item-border);
                .lib-css(color, @color-all);
                .lib-css(line-height, 22px);
                .lib-css(padding, 0 15px);
                .lib-css(text-decoration, @_nav-level0-item-text-decoration);
                box-sizing: border-box;
                position: relative;
                display: inline-block;
                text-transform: uppercase;
                font-size: 16px;
                letter-spacing: 0px;
                font-family: @font-family-proximaNova-regular;
                font-weight: normal;
                > span{
                    position: relative;
                    padding-bottom: 11px;
                    display: inline-block;
                }

                &:hover, &.ui-state-focus {
                    .lib-css(background, @_nav-level0-item-background-color-hover);
                    .lib-css(border, @_nav-level0-item-border-hover);
                    .lib-css(color, @color-link-red);
                    .lib-css(text-decoration, @_nav-level0-item-text-decoration-hover);
                    &:after{
                        content: "";
                        position: absolute;
                        bottom: 0;
                        top: unset;
                        left: 50%;
                        margin-left: -5px;
                        width: 10px;
                        height: 5px;
                        background-color: @color-link-red;
                    }
                }
            }

            &.active,
            &.has-active {
                > .level-top {
                    .lib-css(color, @color-link-red);
                    .lib-css(text-decoration, @_nav-level0-item-text-decoration-active);
                    display: inline-block;
                    &:after{
                        content: "";
                        position: absolute;
                        bottom: 0;
                        left: 50%;
                        margin-left: -5px;
                        width: 10px;
                        height: 5px;
                        background-color: @color-link-red;
                    }
                }
            }

            &.parent:hover > .submenu,
            &.parent > .submenu:hover {
                display: flex !important;
                flex-wrap: wrap;
                justify-content: center;
                overflow: visible !important;
                opacity: 1;
                visibility: visible;
                transition-delay: 0s; // Show immediately
            }

            &.parent {
                > .level-top {
                    padding-right: 15px;

                    > .ui-menu-icon {
                        position: absolute;
                        right: 0;

                        .lib-icon-font(
                            @icon-down,
                            @_icon-font-size: 12px,
                            @_icon-font-line-height: 20px,
                            @_icon-font-text-hide: true,
                            @_icon-font-position: after
                        );
                        display: none;
                    }
                    &.ui-state-active + .submenu{
                        display: flex;
                        flex-wrap: wrap;
                        justify-content: center;
                    }
                }
            }

            .submenu {
                .lib-css(background, @bg-drop-menu);
                .lib-css(border, none);
                .lib-css(box-shadow, none);
                .lib-css(font-size, @_submenu-font-size);
                .lib-css(font-weight, @_submenu-font-weight);
                .lib-css(min-width, @_submenu-min-width);
                .lib-css(padding, @_submenu-padding);
                display: none;
                opacity: 0;
                visibility: hidden;
                left: 0 !important;
                right: 0;
                margin: 0 auto 0 !important;
                top: 100% !important;
                position: absolute;
                z-index: 999;
                transition: opacity 0.2s ease-out, visibility 0.2s ease-out;
                transition-delay: 0.3s;
                max-width: 1400px;
                padding: 15px;
                width: 100%;
                box-sizing: border-box;

                ._lib-submenu-arrow(
                    @_submenu-arrow,
                    @_bg: @_submenu-background-color,
                    @_border: @_submenu-border-color,
                    @_size: @_submenu-arrow-size,
                    @_left: @_submenu-arrow-left
                );

                &:after {
                    content: '';
                    display: block;
                    position: absolute;
                    top: 0;
                    .lib-css(background, @bg-drop-menu);
                    width: 10000px;
                    z-index: -1;
                    bottom: 0;
                    right: -100%;
                    left: -100%;
                    max-height: 100%;

                }

                a {
                    display: block;
                    line-height: inherit;
                    .lib-css(background, @_submenu-item-background-color);
                    .lib-css(border, @_submenu-item-border);
                    .lib-css(color, @_submenu-item-color);
                    .lib-css(padding, @_submenu-item-padding);
                    .lib-css(text-decoration, @_submenu-item-text-decoration);
                    text-transform: uppercase;
                    font-weight: 600;
                    font-family: @font-family-proximaNova-bold;

                    &:hover,
                    &.ui-state-focus {
                        .lib-css(color, @color-all);
                        .lib-css(text-decoration, @_submenu-item-text-decoration-hover);
                    }

                    .menu-icon{
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        padding: 0;
                        margin-bottom: 1rem;
                        height: 120px; // Fixed height for consistent text positioning
                        img{
                            width: 200px;
                            max-height: 120px;
                            object-fit: contain; // Maintain aspect ratio while fitting within bounds
                        }
                    }
                    span{
                        padding-top: 15px; // Reduced padding since menu-icon now has fixed height
                        padding-bottom: 35px;
                        display: block;
                    }
                }

                .active > a {
                    .lib-css(color, @color-all);
                    .lib-css(text-decoration, @_submenu-item-text-decoration-active);
                    .lib-css(padding, @_submenu-item-padding);
                    display: block;
                    text-align: center;
                    font-family: @font-family-proximaNova-bold;
                    text-transform: uppercase;
                    letter-spacing: 0.05em;
                    line-height: 25px;
                    font-weight: 600;
                }

                .submenu {
                    top: 0 !important;
                    left: 100% !important;
                }

                .submenu-reverse{
                    left: auto !important;
                    right: 100%;
                }

                li {
                    margin: 0;
                    width: 20%;
                    text-align: center;
                    padding-top: 40px;
                    &.parent {
                        > a {
                            > .ui-menu-icon {
                                position: absolute;
                                right: 3px;

                                .lib-icon-font(
                                    @icon-next,
                                    @_icon-font-size: 12px,
                                    @_icon-font-line-height: 20px,
                                    @_icon-font-text-hide: true,
                                    @_icon-font-position: after
                                );
                            }
                        }
                    }
                    &:hover{
                        span{
                            color: @color-link-red;
                        }
                    }
                }

                // Styles for submenus without images - no extra spacing
                &.no-images {
                    a {
                        span {
                            padding-top: 30px; // Original padding when no images
                            padding-bottom: 35px;
                        }
                    }
                }

                &.drop-block{
                    .category-item{
                        padding-top: 40px;
                        a{
                            padding: 0;
                        }
                        .menu-icon{
                            padding: 0;
                            overflow: hidden;
                            height: 80px; // Fixed height for consistent text positioning
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            > img{
                                width: 100px;
                                max-height: 80px;
                                object-fit: contain; // Maintain aspect ratio while fitting within bounds
                                transition: transform 0.3s ease; // Smooth transition for hover effect
                            }
                        }
                        span{
                            padding-top: 15px; // Reduced padding since menu-icon now has fixed height
                            padding-bottom: 35px;
                            display: block;
                        }
                        &:hover{
                            .menu-icon{
                                > img{
                                    transform: scale(1.1); // Scale effect instead of margin-top for better visual
                                }
                            }
                            span{
                                color: @color-link-red;
                            }
                        }
                    }
                }

                // Styles for drop-block submenus without images
                &.drop-block.no-images {
                    .category-item {
                        a {
                            span {
                                padding-top: 30px; // Original padding when no images
                                padding-bottom: 35px;
                            }
                        }
                    }
                }
            }

            &.more {
                position: relative;
                .lib-icon-font(
                    @icon-pointer-down,
                    @_icon-font-size: 26px,
                    @_icon-font-position: after
                );

                &:before {
                    display: none;
                }

                &:after {
                    cursor: pointer;
                    padding: 8px 12px;
                    position: relative;
                    z-index: 1;
                }

                &:hover > .submenu,
                & > .submenu:hover {
                    display: flex !important;
                    flex-wrap: wrap;
                    justify-content: center;
                    overflow: visible !important;
                    opacity: 1;
                    visibility: visible;
                    transition-delay: 0s;
                }

                li {
                    display: block;
                }
            }
        }
    }
}

//  Submenu arrow
._lib-submenu-arrow(
    @_submenu-arrow,
    @_bg,
    @_border,
    @_size,
    @_left
) when (@_submenu-arrow = true) {
    & when (iscolor(@_bg)) and (iscolor(@_border)) {
        @_outer-size: @_size + 1;
        @_outer-left: @_left - 1;
        .lib-css(margin-top, @_outer-size);

        > ul {
            .lib-css(margin-top, @_outer-size);

            &:before,
            &:after {
                content: '';
                display: block;
                overflow: hidden;
                position: absolute;
            }

            &:before {
                .lib-css(color, @_bg);
                .lib-css(left, @_left);
                .lib-css(top, -@_size*2);
                .lib-arrow(up, @_size, @_bg);
                z-index: 4;
            }

            &:after {
                .lib-arrow(up, @_outer-size, @_border);
                .lib-css(color, @_border);
                .lib-css(left, @_outer-left);
                .lib-css(top, -@_outer-size*2);
                z-index: 3;
            }
        }
    }
    & when (iscolor(@_bg)) and not (iscolor(@_border)) {
        .lib-css(margin-top, -@_size);

        > ul {
            .lib-css(margin-top, @_size);

            &:before {
                .lib-arrow(up, @_size, @_bg);
                .lib-css(color, @_bg);
                .lib-css(left, @_left);
                .lib-css(top, -@_size*2);
                content: '';
                display: block;
                overflow: hidden;
                position: absolute;
                z-index: 4;
            }
        }
    }
    & when (iscolor(@_border)) and not (iscolor(@_bg)) {
        .lib-css(margin-top, -@_size);

        > ul {
            .lib-css(margin-top, @_size);

            &:before {
                .lib-arrow(up, @_size, @_border);
                .lib-css(color, @_border);
                .lib-css(left, @_left);
                .lib-css(top, -@_size*2);
                content: '';
                display: block;
                overflow: hidden;
                position: absolute;
                z-index: 4;
            }
        }
    }
}
