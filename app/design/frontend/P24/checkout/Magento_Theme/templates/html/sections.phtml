
<?php
/**
 * web-vision GmbH
 *
 * NOTICE OF LICENSE
 *
 * <!--LICENSETEXT-->
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.web-vision.de for more information.
 *
 * @category    WebVision
 * @package     Pumpe24/default
 * @copyright   Copyright (c) 2001-2021 web-vision GmbH (https://www.web-vision.de)
 * @license     <!--LICENSEURL-->
 * <AUTHOR> <<EMAIL>>
 */

/**
 * @var $block \Magento\Framework\View\Element\Template
 */
$group = $block->getGroupName();
$groupCss = $block->getGroupCss();
?>

<?php if ($detailedInfoGroup = $block->getGroupChildNames($group, 'getChildHtml')): ?>
    <div class="sections <?= $block->escapeHtmlAttr($groupCss); ?>">
        <?php $layout = $block->getLayout(); ?>
        <div class="section-items <?= $block->escapeHtmlAttr($groupCss); ?>-items"
             data-mage-init='{"tabs":{"openedState":"active"}}'>
            <a class="nav-tab" href="/" data-bind="i18n: 'Home'"></a>
            <span data-action="toggle-nav" class="nav-close"></span>
            <?php foreach ($detailedInfoGroup as $name): ?>
                <?php
                $html = $layout->renderElement($name);
                if (!trim($html) && ($block->getUseForce() != true)) {
                    continue;
                }
                $alias = $layout->getElementAlias($name);
                $label = $block->getChildData($alias, 'title');
                ?>
                <div class="section-item-title <?= $block->escapeHtmlAttr($groupCss); ?>-item-title"
                     data-role="collapsible">
                    <a class="<?= $block->escapeHtmlAttr($groupCss); ?>-item-switch"
                       data-toggle="switch" href="#<?= $block->escapeHtmlAttr($alias); ?>">
                        <?= /* @noEscape */ $label; ?>
                    </a>
                </div>
                <div
                     id="<?= $block->escapeHtmlAttr($alias); ?>"
                     data-role="content">
                    <?= /* @noEscape */ $html; ?>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
<?php endif; ?>
