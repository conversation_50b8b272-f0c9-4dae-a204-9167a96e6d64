function initShareCart() {
	return {
		open: false,
		loading: true,
		copied: false,
		resultUrl: "",
		shareCart: function() {
			if (!this.open) {
				this.loading = true;
				this.open = true;

				var _this = this;
				fetch("/wk/create?ajax", {
					method: "GET"
				})
				.then(function (response) {
					return response.json()
				})
				.then(function (data) {
					_this.resultUrl = data.url;
					_this.loading = false;
				})
			}
		},
		invalidate: function() {
			this.open = false;
			this.loading = true;
			this.copied = false;
		},
		toClipboard: function() {
			var tmp = document.createElement("input");
			tmp.setAttribute("value", this.resultUrl);
			document.body.appendChild(tmp);
			tmp.select();
			document.execCommand("copy");
			document.body.removeChild(tmp);
			this.copied = true;

			var _this = this;
			setTimeout(function() { _this.clearCopied(); }, 2000);
		},
		clearCopied: function() {
			this.copied = false;
		}
	};
}
