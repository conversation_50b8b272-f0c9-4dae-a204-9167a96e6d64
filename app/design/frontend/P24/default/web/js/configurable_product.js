function initConfigurableOptions(productId, optionConfig) {
    return {
        optionConfig,
        productId,
        allowedAttributeOptions: [],
        selectedValues: [],
        productIndex: [],
        loadedDetails: [],
        findSimpleIndex: function () {
            var matches = [];
            for (var p in this.optionConfig.index) {
                var res = true;
                for (var k in this.selectedValues) {
                    if ((!this.optionConfig.index[p][k]) || (this.optionConfig.index[p][k] != this.selectedValues[k]))
                        res = false;
                }
                if (res)
                    matches.push(p);
            }
            this.productIndex = matches;
        },
        findAllowedAttributeOptions: function () {
            var allAttributes = this.optionConfig.attributes;

            var allAttributesSorted = Object.values(allAttributes).sort((a, b) => {
                if (this.selectedValues[a.id] && this.selectedValues[b.id])
                    return a.position - b.position;
                else if (this.selectedValues[a.id])
                    return -1;
                else if (this.selectedValues[b.id])
                    return 1;
                else
                    return a.position - b.position;
            });
            var previousOption = false;
            var productIndexes = this.optionConfig.index;
            var availableIndexes = Object.keys(productIndexes);
            var newAllowedAttributeOptions = [];

            allAttributesSorted.forEach(attribute => {
                if (previousOption && this.selectedValues[previousOption]) {
                    availableIndexes = availableIndexes.filter(availableIndex => {
                        return productIndexes[availableIndex][previousOption] === this.selectedValues[previousOption];
                    })
                }
                newAllowedAttributeOptions[attribute.id] = allAttributes[attribute.id].options.filter(option => {
                    return !!option.products.find(product => {
                        return availableIndexes.includes(product);
                    });
                });
                previousOption = attribute.id;
            });
            this.allowedAttributeOptions = newAllowedAttributeOptions;
        },
        getAllowedAttributeOptions: function (attributeId) {
            window.dispatchEvent(new CustomEvent("setswatchwidth" + attributeId));
            return this.allowedAttributeOptions[attributeId] || [];
        },
        clearIllegalSelectedValues: function () {
            for (var a in this.allowedAttributeOptions) {
                if (this.selectedValues[a]) {
                    var attr = this.allowedAttributeOptions[a];
                    var isOk = false;
                    for (var o in attr) {
                        if (this.selectedValues[a] == attr[o].id)
                            isOk = true;
                    }
                    if (!isOk)
                        delete (this.selectedValues[a]);
                }
            }
        },
        preselectIfOnlyOneOption: function () {
            var isDone = false;
            for (var attr in this.allowedAttributeOptions) {
                if ((this.allowedAttributeOptions[attr].length == 1) && (this.selectedValues[attr] != this.allowedAttributeOptions[attr][0].id)) {
                    this.changeOption(attr, this.allowedAttributeOptions[attr][0].id);
                    //this.selectedValues[attr] = this.allowedAttributeOptions[attr][0].id;
                    return false;
                    break;
                }
            }
            return true;
        },
        changeOptionByLabel: function (optionId, value) {
            Object.values(this.allowedAttributeOptions[optionId]).forEach(v => {
                var d = Object.values(v);
                if (d[1] == value)
                    this.changeOption(optionId, d[0]);
            });
        },
        updateStockStatus: function () {
            var stockStatus = 10;
            var canExpress = 0;
            for (var p in this.productIndex) {
                v = this.optionConfig.p24CustomData.stockStatus[this.productIndex[p]];
                if (v < stockStatus)
                    stockStatus = v;
                v = this.optionConfig.p24CustomData.canExpress[this.productIndex[p]];
                if (v > canExpress)
                    canExpress = v;
            }

            var stockStatusData = this.optionConfig.p24CustomData.stockStatusData[stockStatus];

            var s = "<span class=\"" + stockStatusData["className"] + "\">" + stockStatusData["label"].charAt(0).toUpperCase() + stockStatusData["label"].slice(1);
            if ((stockStatusData["canExpress"] == true) && (canExpress == 1))
                s += ", " + this.optionConfig.p24CustomData.expressLabel;
            s += "</span>"
            if ('frontend_label' in stockStatusData) {
                s += '<span class="font-normal">' + stockStatusData['frontend_label'] + '</span>';
            }
            document.querySelector("#stockStatusLabel").innerHTML = s;
            if(document.querySelector("#stockStatusLabelExtra"))
                document.querySelector("#stockStatusLabelExtra").innerHTML = 'frontend_label_extra' in stockStatusData ? stockStatusData['frontend_label_extra'] : "";
            if (stockStatusData["isSaleable"] == true) {
                document.querySelector("#addtocartproductdetail" + this.productId).classList.remove("eol");
                if (this.productIndex.length == 1) {
                    document.querySelector("#addtocartproductdetail" + this.productId).removeAttribute("disabled");
                    document.querySelector("#addtocartproductdetail" + this.productId).classList.remove("selvar");
                } else {
                    document.querySelector("#addtocartproductdetail" + this.productId).setAttribute("disabled", "");
                    document.querySelector("#addtocartproductdetail" + this.productId).classList.add("selvar");
                }
            } else {
                document.querySelector("#addtocartproductdetail" + this.productId).setAttribute("disabled", "");
                document.querySelector("#addtocartproductdetail" + this.productId).ClassList.remove("selvar");
                document.querySelector("#addtocartproductdetail" + this.productId).classList.add("eol");
            }
        },
        changeOption: function (optionId, value) {
            this.selectedValues[optionId] = value;
            this.findAllowedAttributeOptions();
            if (this.preselectIfOnlyOneOption()) {
                this.clearIllegalSelectedValues();
                this.findSimpleIndex();
                this.updatePrices();
                this.updateStockStatus();
                this.updateGallery();
                this.updateDetails();
            }
        },
        updatePrices: function () {
            var value = null;
            var isFrom = false;
            basePrice = false;
            basePriceUnit = false;
            for (var p in this.productIndex) {
                v = this.optionConfig.optionPrices[this.productIndex[p]];
                if ((value != null) && (v.finalPrice.amount != value.finalPrice.amount))
                    isFrom = true;
                if ((value == null) || (v.finalPrice.amount < value.finalPrice.amount)) {
                    value = v;
                    if ((this.optionConfig.p24CustomData.basePrice) && (this.optionConfig.p24CustomData.basePrice[this.productIndex[p]]))
                        basePrice = this.optionConfig.p24CustomData.basePrice[this.productIndex[p]];
                }
            }
            document.querySelector("#product-price-" + this.productId).innerHTML = window.hyva.formatPrice(value.finalPrice.amount, false);
            const oldPrice = document.querySelector("#product-price-old-" + this.productId);
            const oldPriceValue = (value.finalPrice.amount != value.oldPrice.amount) ? window.hyva.formatPrice(value.oldPrice.amount, false) : "";
            if (oldPriceValue) {
                oldPrice.style.display = "block";
                let currencySymbol = oldPrice.querySelector('[data-currency-symbol]').dataset.currencySymbol;
                oldPrice.querySelector('.price').innerHTML = oldPriceValue.replace(currencySymbol, "");
            }
            else {
                oldPrice.style.display = "none";
            }

            const basePriceWrapper = document.querySelector("#base-price-wrapper-" + this.productId);
            if (basePrice) {
                document.querySelector("#base-price-" + this.productId).innerHTML = basePrice;
                basePriceWrapper.style.display = "block";
            } else
                basePriceWrapper.style.display = "none";
        },
        updateGallery: function () {
            var value = this.productIndex.length == 1 ? this.optionConfig.images[this.productIndex[0]] : null;
            window.dispatchEvent(new CustomEvent(
                "updategallery",
                {detail: value}
            ));
            window.dispatchEvent(new CustomEvent(
                "update-gallery",
                {detail: value}
            ));
        },
        updateDetails: function () {
            if (this.productIndex.length == 1) {
                if (this.loadedDetails[this.productIndex[0]]) {
                    window.dispatchEvent(new CustomEvent("selectdetail", {detail: this.productIndex[0]}));
                } else {
                    let _this = this;
                    fetch("/p24product/details?product=" + this.productIndex[0] + "&isAjax=1", {
                        method: "GET"
                    }).then(function (response) {
                        return response.json();
                    }).then(function (data) {
                        _this.loadedDetails[_this.productIndex[0]] = true;
                        window.dispatchEvent(new CustomEvent("adddetail", {detail: data}));
                    });
                }
            } else {
            }
        },
        preselectQuerystringItems: function () {
            const urlQueryParams = new URLSearchParams(window.location.search.replace('?', ''));
            Object.values(this.optionConfig.attributes).map(attribute => {
                urlQueryParams.get(attribute.code) && this.changeOptionByLabel(attribute.id, urlQueryParams.get(attribute.code));
            });
        },
    }
}
