
.range-slider-outer,
.range-slider-inner {
height:0.3rem;
top:0.45rem;
}

.range-slider-outer {
  @apply bg-container-filter;
}

.range-slider-inner {
  @apply bg-blue-700;
}

.range-slider-thumb {
border:0;
cursor:pointer;
height:1.4rem;
width:13px;
}

.range-slider-thumb-left {
background:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 60 100' style='stroke:rgb(55,65,81);'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='14' d='M15 20 L40 52 L15 84'></path></svg>");
transform: translateY(200rem);
}

.range-slider-thumb-right {
background:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 60 100' style='stroke:rgb(55,65,81);'><path stroke-linecap='round' stroke-linejoin='round' stroke-width='14' d='M45 20 L20 52 L45 84'></path></svg>");
transform: translateY(200rem);
}

.range-slider > .left::-moz-range-thumb {
@apply range-slider-thumb;
@apply range-slider-thumb-left;
}

.range-slider > .left::-webkit-slider-thumb {
-webkit-appearance: none;
@apply range-slider-thumb;
@apply range-slider-thumb-left;
height:20px;
}

.range-slider > .right::-moz-range-thumb {
@apply range-slider-thumb;
@apply range-slider-thumb-right;
}

.range-slider > .right::-webkit-slider-thumb {
-webkit-appearance: none;
@apply range-slider-thumb;
@apply range-slider-thumb-right;
}

.range-slider > .left,
.range-slider > .right { 
top: -200rem;
-webkit-appearance: none;
}

.range-slider .range-value{
position: absolute;
/*top: 160%;*/
top:-2.5rem;
padding:0.25rem;
border-radius:10%;

  @apply bg-white border-blue-700 border-2;
}
 
.range-slider .range-value::before {
content: ' ';
position: absolute;
width: 10px;
height: 10px;
/*top: -10px;*/
bottom:-9px;
}

.range-slider .range-value-right::before {
left: 2px;
background:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='white' viewbox='0,0,10,10' style='stroke:rgb(0,105,180);'><path stroke-linejoin='round' stroke-width='2' d='M1 1 L1 9 L9 1'></path></svg>");
}

.range-slider .range-value-left::before {
right: 2px;
background:url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' fill='white' viewbox='0,0,10,10' style='stroke:rgb(0,105,180);'><path stroke-linejoin='round' stroke-width='2' d='M1 1 L9 9 L9 1'></path></svg>");
}

@media (max-width:639px) {
  .range-slider .range-value {
  display:block !important;
  }
}
