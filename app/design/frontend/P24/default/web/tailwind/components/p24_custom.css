
@media (max-width:1023px) {
  .container {
  max-width:100%;
  }
}


a {
color:revert;
}

header a,
footer a,
#home-blocks-wrapper a,
a.home-blocks,
.products-list a,
.product-attachment a,
.toolbar a:not(.text-white),
.filter-layer a:not(.text-white),
.cart-items a {
color:inherit;
}

.toolbar a.text-white {
  color: white !important;
}

.p24-word-break {
word-break:break-word;
}

h1,h2,h3,h4,h5,h6 {
font-size:revert;
font-weight:revert;
}

[multiple]:focus, [type="date"]:focus, [type="email"]:focus,
[type="number"]:focus, [type="password"]:focus, [type="search"]:focus,
[type="tel"]:focus, [type="text"]:focus, [type="time"]:focus,
[type="url"]:focus, select:focus, textarea:focus {
-tw-ring-shadow: none;
box-shadow:none;
}

[type=button], [type=reset], [type=submit], button {
-webkit-appearance: none !important;
}

.page-main {
margin-top:0;
}

.fullscreen-bg {
position:fixed;
top:0;
left:0;
right:0;
bottom:0;
background-color:rgba(0,0,0,0.5);
z-index:49;
}

.main-menu-level-0:hover .title-level-0 {
    @apply text-blue-300
}

.catalogsearch-result-index .column.main {
width:100% !important;
}

#service-phone:hover button,
#menu-cart-icon:hover button {
    @apply border-primary;

}

#service-phone:hover svg,
#service-phone:hover span,
#menu-cart-icon:hover svg,
#menu-cart-icon:hover span.icon-cart-text {
    @apply border-primary;
}

.hover-wrap ul li::before,
.keyfacts-content ul li::before {
@apply bg-black;
border-radius: 100%;
content: "";
display: inline-block;
height: 4px;
margin-right: 7px;
margin-bottom:2px;
width: 4px;
}

#product-info-block .product-description ul li, ul.checklist > li, #good-reasons ul li {
    background-position-y: center;
    background-repeat: no-repeat;
    @apply pl-6 my-1;
}

#product-info-block .product-description ul li::before, #good-reasons ul li::before {
    display: none;
}

#good-reasons ul li {
    background-image: url('../images/icons/check-round.svg');
    margin-top: 0.25rem;
    margin-bottom: 0.25rem;
    padding-left: 1.5rem;
}

.category-banner h1,
.category-text h1 {
@apply text-p24blue font-bold text-center mb-4;
font-size:1.5rem;
}
.category-text a {
@apply text-p24blue;
}

.category-form .price-box .normal-price,
.item-tabs .price-box .normal-price {
order:2;
}

.product-info-main .swatch-attribute label,
.swatch-attribute .swatch-attribute-options .swatch-option {
margin:0;
}

.prod-description h2,
.prod-description h3,
.prod-description h4,
.prod-description h5,
.prod-description h6,
.category-banner h2,
.category-banner h3,
.category-banner h4,
.category-banner h5,
.category-banner h6,
.category-text h2,
.category-text h3,
.category-text h4,
.category-text h5,
.category-text h6 {
@apply my-3;
font-weight:600;
display:table;
}

.prod-description h2, .prod-description h3,
.category-banner h2, .category-banner h3,
.category-text h2, .category-text h3 {
font-size:1.35rem;
}

.prod-description h4,
.prod-description h5,
.prod-description h6,
.category-banner h4,
.category-banner h5,
.category-banner h6,
.category-text h4,
.category-text h5,
.category-text h6 {
font-size:1.15rem;
}

.prod-description h2, .prod-description h4,
.category-banner h2, .category-banner h4,
.category-text h2, .category-text h4 {
@apply border-p24blue border-b-2 pb-1;
}

.prod-description iframe {
max-width:100%;
height:auto;
}

.prod-description .products-list form:last-child .item-tabs,
.prod-description .products-list > .item-tabs:last-child {
border-bottom:0 !important;
}

.filter-options-content .swatch-attribute .swatch-attribute-options .swatch-option,
.toolbar .toolbar-sorter-content .sorter-options .sorter-option {
margin:0.1rem 0.4rem;
}

.filter-layer .filter-option {
min-width:180px;
}

.filter-layer .swatch-attribute .swatch-attribute-options {
display: grid;
}

.toolbar .toolbar-sorter-content .sorter-options .sorter-option {
border-width:1px;
display:flex;
justify-content:center;
padding:.25rem .5rem;
}

#toolbar-bottom .toolbar-sorter,
.addtocart .endoflife,
.addtocart .selectvariant,
.addtocart:disabled .saleable,
.review-toolbar .limiter {
display:none;
}

.swatch-attribute .swatch-attribute-options .swatch-option:hover,
.toolbar .toolbar-sorter-content .sorter-options .sorter-option:hover {
@apply bg-applegreen;
}

.swatch-attribute .swatch-attribute-options .swatch-option.bg-primary:hover,
.toolbar .toolbar-sorter-content .sorter-options .sorter-option.bg-primary:hover {
@apply bg-applegreen;
}

.product-info-main #product_addtocart_form {
margin:0;
}

.category-form .price-label {
font-size:1rem;
display:contents;
}

.btn-hover:hover {
    @apply bg-primary-darker text-white;
}

.link-hover:hover,
.svg-hover:hover {
    @apply text-primary-darker;
}

.addtocart:disabled,
.addtocart:disabled:hover {
cursor:default;
    @apply cursor-default bg-gray-400;
}

.addtocart:disabled .endoflife,
.addtocart:disabled:hover .endoflife {
    @apply text-red-400;
}

.addtocart:disabled.eol .endoflife,
.addtocart:disabled.selvar .selectvariant,
.addtocart .saleable {
display:block;
}

.product-info-main .price-container .old-price .price-wrapper {
text-decoration-thickness:0.16rem;
}

.grid-cols-product-header-review,
.grid-cols-prod-details {
grid-template-columns:50% 50%;
}

.grid-cols-product-variants-tab {
grid-template-columns:65% auto;
}

.catalog-category-view .products .category-form:hover > .products-wrap img,
.p24-frontpage-products .products .category-form:hover > .products-wrap img,
.product-slider .category-form:hover > .products-wrap img,
.item-tabs:hover img,
.nav-level1:hover img,
.mini-cart-item:hover img,
.cart-items:hover img {
--tw-scale-x:1;
--tw-scale-y:1;
}


.product-slider-prison {
height: 447px;
}

.product-slider .item-wrapper {
width:340px;
}
#minicart-slider-container:hover {
    @apply min-h-max;
}

.minicart-keyfacts ul li{
    @apply text-xs;
}

#description-wrapper table {
margin-left:1px;
max-width:0px;
border:0 !important;
}

#description-wrapper center table {
max-width:unset;
}

#description-wrapper table th,
#description-wrapper table td {
@apply py-1 px-2;
}

#description-wrapper table td {
@apply bg-container-darker;
}

#description-wrapper table:not(.foerderleistung) td,
#description-wrapper table:not(.foerderleistung) th {
@apply border border-black;
}

#description-wrapper table:not(.foerderleistung) tr:nth-child(odd) td {
    @apply bg-gray-350
}

#description-wrapper table.foerderleistung th:nth-child(1),
#description-wrapper table.foerderleistung td:nth-child(1) {
@apply border-applegreen border-r-2 text-right;
}

#description-wrapper p {
@apply pb-5;
}

#description-wrapper iframe {
@apply my-6;
}

.btn-green {
@apply btn p-2 ml-0 mt-2 w-full bg-white border-applegreen border text-gray-900 shadow;
}

.btn-green:hover {
@apply bg-applegreen text-white;
}

.keyfacts-content a {
color:revert;
}

#mini-cart-drawer .mini-cart-item.border-b:last-of-type {
border-bottom:0;
}

.cms-no-route .main .btn:hover {
    @apply bg-primary-darker;
}

.p24Accordion {
@apply border-gray-300 border my-4;
}

.p24Accordion .p24AccordionHeader {
@apply flex items-center cursor-pointer border-black border-b-2 p-2;
}

.p24Accordion .p24AccordionHeader svg {
@apply w-4 h-4 mr-2 text-p24blue duration-200 ease-in;
}

.p24Accordion .p24AccordionHeader span {
@apply w-full font-bold;
}

.p24Accordion .p24AccordionContentWrapper {
@apply w-full overflow-hidden transition-all ease-in duration-200;
}

.p24Accordion .p24AccordionContent {
@apply mx-2 my-8;
}

@media (min-width:1428px) {
  #gallery-thumbs {
  max-width:200px;
  }
}

@media (max-width:767px) {
  #filter-layer-wrapper {
  max-height:0;
  }
  .page-footer a {
  line-height:1.75rem;
  }
}

@media (min-width:640px) {
  .prod-description iframe {
  min-height:360px;
  }
}
#gallery .h-swiper-full .swiper-zoom-container {
    & > canvas,
    > img,
    > svg {
        padding-top: 200px;
        padding-bottom: 106px;
    }
}
@media (min-width:768px) {
 #gallery .h-swiper-full .swiper-zoom-container {
     & > canvas,
     > img,
     > svg {
         padding-top: 70px;
         padding-bottom: 116px;
     }
 }
}


@media (max-width:639px) {
    #customer-reviews-form {
        height: 125vh;
    }
}

.cms-index-index .show-more-widget, .cms-index-index .category-slider-widget {
  @apply md:hidden;
}

.active.border-p24blue {
    --tw-border-opacity: 1;
    @apply border-primary;
}

.product-slider-outer .glider-prev,
.product-slider-outer .glider-next {
    @apply bg-primary;
    color: white;
    cursor: pointer;
    position: absolute;
    top: 200px;
    width: auto;
    border-radius: 3px;
}
