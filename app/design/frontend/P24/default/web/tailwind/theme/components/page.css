.page-main {
    .column.main {
        @apply flex flex-col;
    }

    .table-row-items > div.table-row-item {
        @apply bg-container-darker;

        &:nth-child(2n + 1) {
            @apply bg-container-lighter;
        }
    }

    .product-items.widget-product-grid {
        li:nth-child(n + 5) {
            @apply hidden md:block;
        }

        .product-image-container {
            width: 240px;
        }

        .product-image-container span.product-image-wrapper {
            padding-bottom: 125%;
        }

        .single-product:hover > .hover-wrap {
            max-height: 180px;
        }
    }
}

.widget_container_overlay_sticker iframe {
    @apply z-0 !important;
}

.glider-dot.active {
    opacity: 1;
    width: 1rem;
}
.flex.flex-row.glider-initialized {
    flex-direction: column;
}