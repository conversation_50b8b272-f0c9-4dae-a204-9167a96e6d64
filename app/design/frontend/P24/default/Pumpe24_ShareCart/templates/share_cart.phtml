<?php
use Hyva\Theme\ViewModel\HeroiconsOutline;
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>

<?php if (($block->isActive()) && ($block->cartHasProducts())): ?>

<div class="mx-auto md:mx-0 py-4"
	x-data="initShareCart()"
>
	<div class="flex flex-row px-2"
		:class="!open ? 'cursor-pointer' : ''"
		x-on:click="shareCart()"
		@private-content-loaded.window="invalidate()"
	>
		<span class="flex-auto w-full text-lg font-bold">
			<?=$escaper->escapeHtml(__("Share Cart"));?>
		</span>
		<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"
			class="flex-auto transition-transform w-5 h-5 mt-1 transform duration-200 ease-in-out"
			:class="open ? 'rotate-180' : ''"
		>
			<path d="M19 9L12 16L5 9" stroke="#0168B3" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
		</svg>
	</div>

	<div
		class="overflow-hidden transition-all ease-in duration-200"
		x-ref="container"
		x-bind:style="open ? 'max-height: ' + $refs.container.scrollHeight + 'px;' : 'max-height:0;'"
		style="max-height:0;"
	>
		<div
			class="flex flex-row justify-center items-center"
			x-show="loading"
		>
			<svg width="57" height="57" viewBox="0 0 57 57" xmlns="http://www.w3.org/2000/svg" stroke="#fff" style="transform: scale(0.6)" class="stroke-current text-primary">
				<g fill="none" fill-rule="evenodd">
					<g transform="translate(1 1)" stroke-width="2">
						<circle cx="5" cy="50" r="5">
							<animate attributeName="cy" begin="0s" dur="2.2s" values="50;5;50;50" calcMode="linear" repeatCount="indefinite"></animate>
							<animate attributeName="cx" begin="0s" dur="2.2s" values="5;27;49;5" calcMode="linear" repeatCount="indefinite"></animate>
						</circle>
						<circle cx="27" cy="5" r="5">
							<animate attributeName="cy" begin="0s" dur="2.2s" from="5" to="5" values="5;50;50;5" calcMode="linear" repeatCount="indefinite"></animate>
							<animate attributeName="cx" begin="0s" dur="2.2s" from="27" to="27" values="27;49;5;27" calcMode="linear" repeatCount="indefinite"></animate>
						</circle>
						<circle cx="49" cy="50" r="5">
							<animate attributeName="cy" begin="0s" dur="2.2s" values="50;50;5;50" calcMode="linear" repeatCount="indefinite"></animate>
							<animate attributeName="cx" from="49" to="49" begin="0s" dur="2.2s" values="49;5;27;49" calcMode="linear" repeatCount="indefinite"></animate>
						</circle>
					</g>
				</g>
			</svg>
			<div class="ml-4 text-primary"><?=$escaper->escapeHtml(__("Loading..."));?></div>
		</div>

		<div class="flex flex-row items-center px-2 pt-1"
			x-show="!loading"
		>
			<div class="grid items-center">
				<span class="text-applegreen text-sm transition-all ease-in duration-200 z-1 bg-white"
					x-bind:style="copied ? 'grid-area:1/1; opacity:1;' : 'grid-area:1/1; opacity:0'"
					style="grid-area:1/1; opacity:1;"
				>
					<?=__("Copied to Clipboard")?>
				</span>
				<a class="text-gray-500 text-sm transition-all ease-in duration-200 z-2 bg-white"
					x-text="resultUrl"
					x-bind:href="resultUrl"
					x-bind:style="copied ? 'grid-area:1/1; opacity:0;' : 'grid-area:1/1; opacity:1'"
					style="grid-area:1/1; opacity:1;"
				>
				</a>
			</div>
			<div class="grid"
				x-on:click="toClipboard()"
			>
				<span class="transition-all ease-in duration-200 text-gray-500 svg-hover cursor-pointer"
					title="<?=__("Copy to Clipboard")?>"
					x-show="!copied"
					x-transition:enter-start="opacity-0"
					x-transition:enter-end="opacity-100"
					x-transition:leave-start="opacity-100"
					x-transition:leave-end="opacity-0"
					style="grid-area:1/1;"
				>
					<?=$heroicons->clipboardCopyHtml("w-8 h-8", 25, 25)?>
				</span>
				<span class="transition-all ease-in duration-200 text-applegreen"
					x-show="copied"
					x-transition:enter-start="opacity-0"
					x-transition:enter-end="opacity-100"
					x-transition:leave-start="opacity-100"
					x-transition:leave-end="opacity-0"
					style="display:none; grid-area:1/1;"
				>
					<?=$heroicons->clipboardCheckHtml("w-8 h-8", 25, 25)?>
				</span>
			</div>
		</div>
	</div>
</div>
<?php endif; ?>
