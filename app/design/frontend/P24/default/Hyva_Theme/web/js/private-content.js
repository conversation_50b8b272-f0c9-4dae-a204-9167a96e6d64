    'use strict';
    {
//TODO mobile safari mag globale const nicht?

        var private_content_key = 'mage-cache-storage';
        var private_content_expire_key = 'mage-cache-timeout';
        var private_content_version_key = 'private_content_version';
        var section_data_ids_key = 'section_data_ids';
        var mage_cache_session_id_key = 'mage-cache-sessid';
        var last_visited_store_key = 'last_visited_store';

        var ttl = 3600;

        if (typeof hyva === 'undefined' || (!hyva.getBrowserStorage || !hyva.getCookie || !hyva.setCookie)) {
            console.warn("Hyvä helpers are not loaded yet. Make sure they are included before this script");
        }

        function loadSectionData () {
            const browserStorage = hyva.getBrowserStorage();
            if (!browserStorage) {
                typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                    [{
                        type: "warning",
                        text: window.p24PleaseEnableLocalStorage,
                    }]
                );
                return;
            }
            try {
                let isInvalid = false;
                if (hyva.getCookie(last_visited_store_key) != CURRENT_STORE_CODE) {
                    isInvalid = true;
                }
                hyva.setCookie(last_visited_store_key, CURRENT_STORE_CODE, false, false);

                if (!hyva.getCookie(mage_cache_session_id_key)) {
                    isInvalid = true;
                    const skipSetDomain = true;
                    const days = false;
                    hyva.setCookie(mage_cache_session_id_key, true, days, skipSetDomain)
                }

                const cookieVersion = hyva.getCookie(private_content_version_key);
                const storageVersion = browserStorage.getItem(private_content_version_key);

                if (cookieVersion && !storageVersion || cookieVersion !== storageVersion) {
                    isInvalid = true;
                }

                const privateContentExpires = browserStorage.getItem(private_content_expire_key);
                if (privateContentExpires && new Date(privateContentExpires) < new Date()) {
                    browserStorage.removeItem(private_content_key);
                }

                if (isInvalid) {
                    fetchPrivateContent([]);
                } else if (cookieVersion && storageVersion && cookieVersion === storageVersion) {
                    const privateContent = JSON.parse(browserStorage.getItem(private_content_key));
                    if (
                        privateContent &&
                        privateContentExpires &&
                        privateContent.cart &&
                        privateContent.customer
                    ) {
                        dispatchPrivateContent(privateContent);
                    } else {
                        fetchPrivateContent([]);
                    }
                } else {
                    dispatchPrivateContent({});
                }

            } catch (error) {
              console.warn('Error retrieving Private Content:', error);
            }
        }

        window.addEventListener('load', loadSectionData);
        window.addEventListener('reload-customer-section-data', loadSectionData);

        function dispatchPrivateContent(data) {
            const privateContentEvent = new CustomEvent("private-content-loaded", {
                detail: {
                    data: data
                }
            });
            window.dispatchEvent(privateContentEvent);
        }

        function fetchPrivateContent(sections) {
            fetch('/customer/section/load/?sections=' + encodeURIComponent(
                sections.join(',')
            ), {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(function (response) {
                    return response.json()
                })
                .then(
                    function (data) {
                        if (data) {
                            dispatchPrivateContent(data);

                            // don't persist messages, they've been dispatched already
                            if ( data.messages && data.messages.messages ) {
                                data.messages.messages = []
                            }
                            try {
                                const browserStorage = hyva.getBrowserStorage();
                                const expiresAt = new Date(Date.now() + (ttl * 1000)).toISOString();
                                browserStorage.setItem(private_content_key, JSON.stringify(data));
                                browserStorage.setItem(
                                    private_content_expire_key,
                                    expiresAt
                                );
                                const newCookieVersion = hyva.getCookie(private_content_version_key);
                                browserStorage.setItem(private_content_version_key, newCookieVersion);

                                // We don't need the section_data_ids in Hyvä, but we store them for compatibility
                                // with Luma Fallback. Otherwise, not all sections are loaded in Luma Checkout
                                hyva.setCookie(
                                    section_data_ids_key,
                                    JSON.stringify(
                                        Object.keys(data).reduce((sectionDataIds, sectionKey) => {
                                            sectionDataIds[sectionKey] = data[sectionKey]['data_id'];
                                            return sectionDataIds;
                                        }, {})
                                    ),
                                    false,
                                    true
                                );
                            } catch (error) {
                                console.warn("Couldn't store privateContent", error);
                            }
                        }
                    }
                );
        }
    }
