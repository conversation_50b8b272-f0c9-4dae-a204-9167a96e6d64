<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Framework\View\Element\Template;

/** @var Template $block */
?>
<?php if ($block->getData('render_in_script_tag')): ?>
    <script>
        <?php endif; ?>

        const magentoDataSources = {
            mageCacheStorage: {
                cartId: {
                    storageKey: 'mage-cache-storage',
                    value: 'cart.cartId',
                    timestamp: 'cart.data_id',
                },
                token: {
                    storageKey: 'mage-cache-storage',
                    value: 'customer.signin_token',
                    timestamp: 'customer.data_id',
                },
            },
        };

        const activeSource = magentoDataSources.mageCacheStorage;

        const getConfigFromLocalStorage = ({storageKey, value: path, ttl, timestamp,}) => {
            const item = window.localStorage.getItem(storageKey);
            const now = Date.now();
            if (!item) {
                return undefined;
            }
            const itemData = JSON.parse(item);

            const ttlValue = (ttl && getByPath(ttl, itemData)) || 3600;
            const timeStored = timestamp && getByPath(timestamp, itemData);

            if (timeStored) {
                const timePassed =
                    now -
                    timeStored *
                    (timeStored.toString().length < now.toString().length ? 1000 : 1);

                if (ttlValue && timePassed > ttlValue * 1000) {
                    window.localStorage.removeItem(storageKey);
                    return undefined;
                }
            }
            return getByPath(path, itemData);
        };

        const getByPath = (path, object) => {
            return path
                .split('.')
                .reduce(
                    (object, key) => (object && object[key] ? object[key] : null),
                    object
                );
        };
        <?php if ($block->getData('render_in_script_tag')): ?>
    </script>
<?php endif; ?>
