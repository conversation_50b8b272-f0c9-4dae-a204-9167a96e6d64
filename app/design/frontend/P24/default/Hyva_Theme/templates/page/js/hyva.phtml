<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Currency;
use Magento\Cookie\Helper\Cookie;
use Magento\Framework\Escaper;

use Hyva\Theme\ViewModel\Store as StoreViewModel;

/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Currency $currencyViewModel */
$currencyViewModel = $viewModels->require(Currency::class);

$cookieViewModel = $viewModels->require(Hyva\Theme\ViewModel\Cookie::class);
$storeViewModel = $viewModels->require(StoreViewModel::class);
$cookieHelper = $this->helper(Cookie::class);
$cookieRestriction = $cookieHelper->isCookieRestrictionModeEnabled();
?>
<script>
var p24UserAllowedSaveCookie = "<?=Cookie::IS_USER_ALLOWED_SAVE_COOKIE?>";
var p24CurrencyCode = "<?=$escaper->escapeHtml($currencyViewModel->getCurrentCurrencyCode())?>";
var p24CurrencySymbol = "<?=$escaper->escapeHtml($currencyViewModel->getCurrentCurrencySymbol())?>";
var p24PleaseEnableLocalStorage = "<?=$escaper->escapeHtml(__("Please enable LocalStorage in your browser."))?>";

var BASE_URL = "<?= /* @noEscape */ $block->getUrl("/") ?>";
var THEME_PATH = "<?= /* @noEscape */ $block->getViewFileUrl("/") ?>";
var COOKIE_CONFIG = {
	"expires": null,
	"path": "<?=$escaper->escapeJs($cookieViewModel->getPath())?>",
	"domain": "<?=$escaper->escapeJs($cookieViewModel->getDomain())?>",
	"secure": false,
	"lifetime": "<?=$escaper->escapeJs($cookieViewModel->getLifetime())?>",
	"cookie_restriction_enabled": <?=($cookieRestriction ? 'true' : 'false')?>
};
var CURRENT_STORE_CODE = '<?= /* @noEscape */ $storeViewModel->getStoreCode(); ?>';
var CURRENT_WEBSITE_ID = '<?= /* @noEscape */ $storeViewModel->getWebsiteId(); ?>';
</script>
