<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="order_top_items">
            <block class="Magento\Framework\View\Element\Html\Links" name="sales.order.info.links" before="-">
                <arguments>
                    <argument name="css_class" xsi:type="string">items order-links</argument>
                </arguments>
                <block class="Magento\Sales\Block\Order\Link" name="sales.order.info.links.information">
                    <arguments>
                        <argument name="path" xsi:type="string">sales/order/view</argument>
                        <argument name="label" xsi:type="string" translate="true">Order</argument>
                    </arguments>
                </block>
                <block class="Magento\Sales\Block\Order\Link" name="sales.order.info.links.invoice">
                    <arguments>
                        <argument name="key" xsi:type="string">Invoices</argument>
                        <argument name="path" xsi:type="string">sales/order/invoice</argument>
                        <argument name="label" xsi:type="string" translate="true">Invoices</argument>
                    </arguments>
                </block>
                <block class="Magento\Sales\Block\Order\Link" name="sales.order.info.links.shipment">
                    <arguments>
                        <argument name="key" xsi:type="string">Shipments</argument>
                        <argument name="path" xsi:type="string">sales/order/shipment</argument>
                        <argument name="label" xsi:type="string" translate="true">Shipments</argument>
                    </arguments>
                </block>
                <block class="Magento\Sales\Block\Order\Link" name="sales.order.info.links.creditmemo">
                    <arguments>
                        <argument name="key" xsi:type="string">Creditmemos</argument>
                        <argument name="path" xsi:type="string">sales/order/creditmemo</argument>
                        <argument name="label" xsi:type="string" translate="true">Refunds</argument>
                    </arguments>
                </block>
            </block>
        </referenceContainer>
    </body>
</page>
