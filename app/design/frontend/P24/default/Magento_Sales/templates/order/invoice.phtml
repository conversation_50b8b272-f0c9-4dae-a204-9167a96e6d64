<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

/** @var \Magento\Sales\Block\Order\Invoice $block */
?>
<?php $order = $block->getOrder() ?>
<div class="md:flex md:justify-between items-center mb-3">
    <div class="text-center md:text-left">
        <div class="text-2xl"><?= $escaper->escapeHtml(__('Order # %1', $order->getRealOrderId())) ?></div>
        <?= $block->getChildHtml('order.date') ?>
    </div>
    <?= $block->getChildHtml('sales.invoice.buttons') ?>
</div>
<div class="order-details-items invoice bg-container-lighter">
    <div class="-mx-4">
        <?= $block->getChildHtml('order_top_items') ?>
    </div>
    <div class="p-4 card">
        <div class="mb-4">
            <?= $block->getChildHtml('sales.order.info') ?>
        </div>
        <?= $block->getChildHtml('invoice_items') ?>
        <div class="actions-toolbar">
            <div class="secondary">
                <a class="action back" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                    <span><?= $escaper->escapeHtml($block->getBackTitle()) ?></span>
                </a>
            </div>
        </div>
    </div>
</div>
