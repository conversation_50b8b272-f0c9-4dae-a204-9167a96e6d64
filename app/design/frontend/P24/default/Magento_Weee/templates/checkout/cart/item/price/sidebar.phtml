<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// phpcs:disable Magento2.Templates.ThisInTemplate

/** @var $block \Magento\Weee\Block\Item\Price\Renderer */

$item = $block->getItem();

// ensure we use the zone for the shopping cart / minicart
$originalZone = $block->getZone();
$block->setZone(\Magento\Framework\Pricing\Render::ZONE_CART);
$finalPrice = $item->getProduct()->getFinalPrice();
$normalPrice = $item->getProduct()->getPrice();
?>

<?php if ($block->displayPriceInclTax() || $block->displayBothPrices()) : ?>
<span class="price-including-tax" data-label="<?= $block->escapeHtmlAttr(__('Incl. Tax')) ?>">
    <?php if ($block->displayPriceWithWeeeDetails()) : ?>
        <span class="minicart-tax-total">
    <?php else : ?>
        <span class="minicart-price">
    <?php endif; ?>
            <?php if ($normalPrice > $finalPrice ) : ?>
    <div class="old-price line-through text-xs font-normal"><?= /* @noEscape */ $block->formatPrice($normalPrice); ?></div>
            <?php endif; ?>
    <div class="final-price"><?= /* @noEscape */ $block->formatPrice($finalPrice); ?></div>
        </span>

    <?php if ($block->displayPriceWithWeeeDetails()) : ?>
        <?php if ($this->helper(\Magento\Weee\Helper\Data::class)->getApplied($item)) : ?>
            <span class="minicart-tax-info">
            <?php foreach ($this->helper(\Magento\Weee\Helper\Data::class)->getApplied($item) as $tax) : ?>
                <span class="weee" data-label="<?= $block->escapeHtmlAttr($tax['title']) ?>">
                    <?= /* @noEscape */ $block->formatPrice($tax['amount_incl_tax'], true, true) ?>
                </span>
            <?php endforeach; ?>
            </span>

            <?php if ($block->displayFinalPrice()) : ?>
                <span class="minicart-tax-total">
                    <span class="weee" data-label="<?= $block->escapeHtmlAttr(__('Total Incl. Tax')) ?>">
                        <?= /* @noEscape */ $block->formatPrice($block->getFinalUnitDisplayPriceInclTax()) ?>
                    </span>
                </span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>
    </span>
<?php endif; ?>

    <?php if ($block->displayPriceExclTax() || $block->displayBothPrices()) : ?>
    <span class="price-excluding-tax" data-label="<?= $block->escapeHtmlAttr(__('Excl. Tax')) ?>">
    <?php if ($block->displayPriceWithWeeeDetails()) : ?>
        <span class="minicart-tax-total">
    <?php else : ?>
        <span class="minicart-price">
    <?php endif; ?>
    <?= /* @noEscape */ $block->formatPrice($block->getUnitDisplayPriceExclTax()) ?>
        </span>

    <?php if ($block->displayPriceWithWeeeDetails()) : ?>
        <?php if ($this->helper(\Magento\Weee\Helper\Data::class)->getApplied($item)) : ?>
            <span class="minicart-tax-info">
            <?php foreach ($this->helper(\Magento\Weee\Helper\Data::class)->getApplied($item) as $tax) : ?>
                <span class="weee" data-label="<?= $block->escapeHtmlAttr($tax['title']) ?>">
                    <?= /* @noEscape */ $block->formatPrice($tax['amount'], true, true) ?>
                </span>
            <?php endforeach; ?>
            </span>

            <?php if ($block->displayFinalPrice()) : ?>
                <span class="minicart-tax-total">
                    <span class="weee" data-label="<?= $block->escapeHtmlAttr(__('Total')) ?>">
                        <?= /* @noEscape */ $block->formatPrice($block->getFinalUnitDisplayPriceExclTax()) ?>
                    </span>
                </span>
            <?php endif; ?>
        <?php endif; ?>
    <?php endif; ?>
    </span>
<?php endif; ?>
<?php $block->setZone($originalZone); ?>
