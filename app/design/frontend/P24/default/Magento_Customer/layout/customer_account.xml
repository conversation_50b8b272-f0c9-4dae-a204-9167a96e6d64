<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <title>My Account</title>
    </head>
    <body>
        <referenceContainer name="sidebar.main">
            <block class="Magento\Framework\View\Element\Template" name="sidebar.main.account_nav"
                   template="Magento_Theme::html/collapsible.phtml" before="-">
                <arguments>
                    <argument name="block_title" xsi:type="string">My Account</argument>
                    <argument name="block_css" xsi:type="string">account-nav</argument>
                </arguments>
                <block class="Magento\Customer\Block\Account\Navigation" name="customer_account_navigation" before="-">
                    <arguments>
                        <argument name="css_class" xsi:type="string">nav items</argument>
                    </arguments>
                    <block class="Magento\Customer\Block\Account\SortLinkInterface"
                           name="customer-account-navigation-account-link">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Dashboard</argument>
                            <argument name="path" xsi:type="string">customer/account</argument>
                            <argument name="sortOrder" xsi:type="number">250</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-account-edit-link">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Account Information</argument>
                            <argument name="path" xsi:type="string">customer/account/edit</argument>
                            <argument name="sortOrder" xsi:type="number">240</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Customer\Block\Account\SortLinkInterface"
                           name="customer-account-navigation-address-link">
                        <arguments>
                            <argument name="label" xsi:type="string" translate="true">Address Book</argument>
                            <argument name="path" xsi:type="string">customer/address</argument>
                            <argument name="sortOrder" xsi:type="number">230</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Customer\Block\Account\Delimiter"
                           name="customer-account-navigation-delimiter-2"
                           template="Magento_Customer::account/navigation-delimiter.phtml">
                        <arguments>
                            <argument name="sortOrder" xsi:type="number">10</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Customer\Block\Account\AuthorizationLink"
                           name="authorization-link-login"
                           template="Magento_Customer::account/link/authorization.phtml"
                    >
                        <arguments>
                            <argument name="sortOrder" xsi:type="number">0</argument>
                        </arguments>
                    </block>
                </block>
            </block>
        </referenceContainer>
    </body>
</page>
