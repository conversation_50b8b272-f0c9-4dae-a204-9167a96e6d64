<?php

/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Customer\ReviewList;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\Store;

/** @var $block Template */
/** @var Escaper $escaper */
/** @var $viewModels ViewModelRegistry */

/** @var ReviewList $viewModelCustomerReviews */
$viewModelCustomerReviews = $viewModels->require(ReviewList::class);

/** @var Hyva\Theme\ViewModel\StoreConfig $viewModelStoreConfig */
$viewModelStoreConfig = $viewModels->require(\Hyva\Theme\ViewModel\StoreConfig::class);

/** @var Store $viewModelStore */
$viewModelStore = $viewModels->require(Store::class);

$productUrlSuffix = $viewModelStoreConfig->getStoreConfig('catalog/seo/product_url_suffix');
$baseUrl = $block->getBaseUrl();
$ratingSteps = 5;
?>
<div class="mb-6 text-2xl"><?= $escaper->escapeHtml(__('My Product Reviews')) ?></div>

<div id="content"
     x-data="initReviewList()"
     @private-content-loaded.window="onPrivateContentLoaded(event.detail.data)">
    <template x-if="pageInfo && pageInfo.page_size && pageInfo.total_pages">
        <div>
            <template x-if="pageInfo.total_pages > 1">
                <div class="flex items-center toolbar-pager">
                    <ul class="relative z-0 inline-flex w-2/3 items pages-items">
                        <li class="relative inline-flex items-center px-2 py-2 text-sm font-medium leading-5
                                text-gray-500 transition duration-150 ease-in-out bg-white border border-r-0
                                border-gray-300 item pages-item-previous rounded-l-md hover:text-gray-400
                                focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue
                                active:bg-gray-100 active:text-gray-500"
                        >
                            <a href="#" @click.prevent="currentPage > 1 && setCurrentPage(currentPage - 1)"
                               :class="{'cursor-default' : currentPage < 2}"
                               title="<?= $escaper->escapeHtmlAttr(__('Previous')) ?>">
                                <span>
                                    <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                              d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0
                                              01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z"
                                              clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </a>
                        </li>
                        <template x-for="i in Object.values(totalPagesObject)">
                            <li :class="{
                                    'border-primary -ml-px ': i === pageInfo.current_page,
                                    'border-gray-300' : i !== pageInfo.current_page
                                }"
                                class="relative inline-flex items-center text-sm font-medium leading-5 text-gray-700
                                    transition duration-150 ease-in-out bg-white border item hover:text-gray-500
                                    focus:z-10 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue
                                    active:bg-gray-100 active:text-gray-700">
                                <template x-if="i === pageInfo.current_page">
                                    <strong class="px-4 py-2 cursor-default page">
                                        <span x-text="i"></span>
                                    </strong>
                                </template>
                                <template x-if="i !== pageInfo.current_page">
                                    <a class="px-4 py-2 page" href="#" @click.prevent="setCurrentPage(i)">
                                        <span x-text="i"></span>
                                    </a>
                                </template>
                            </li>
                        </template>
                        <li class="relative inline-flex items-center px-2 py-2 text-sm font-medium leading-5
                            text-gray-500 transition duration-150 ease-in-out bg-white border border-l-0
                            border-gray-300 item pages-item-next rounded-r-md hover:text-gray-400 focus:z-10
                            focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:bg-gray-100
                            active:text-gray-500"
                        >
                            <a @click.prevent="currentPage < pageInfo.total_pages && setCurrentPage(currentPage + 1)"
                               :class="{'cursor-default' : currentPage >= pageInfo.total_pages}"
                               href="#" title="<?= $escaper->escapeHtmlAttr(__('Next')) ?>">
                                <span>
                                    <svg class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd"
                                              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1
                                              1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                                              clip-rule="evenodd"></path>
                                    </svg>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </template>
        </div>
    </template>
    <template x-if="reviews && reviews.length">
        <div>
            <template x-for="review in reviews" :key="review">
                <div class="flex my-6 card">
                    <div class="w-32">
                        <img :src="review.product && review.product.image && review.product.image.url"
                             :alt="review.product && review.product.image && review.product.image.label"/>
                    </div>
                    <div class="pl-6">
                        <p class="text-lg font-medium">
                            <a :href="review.product && review.product.url_key && (
                                '<?= $escaper->escapeJs($baseUrl) ?>' +
                                 review.product.url_key  +
                                 '<?= $escaper->escapeJs($productUrlSuffix) ?>'
                               )"
                               x-text="review.product && review.product.name">
                            </a>
                        </p>
                        <p class="mb-2 text-sm">
                            <?= $escaper->escapeHtml(__('Created at')) ?>
                            <span x-text="review &&
                                review.created_at &&
                                new Date(review.created_at).toLocaleDateString()"
                            ></span>
                        </p>
                        <template x-for="(item, index) in review.ratings_breakdown" :key="index">
                            <div class="flex">
                                <template x-for="i in <?= $escaper->escapeJs($ratingSteps) ?>">
                                    <div>
                                        <template x-if="i <= item.value">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                 class="w-6 h-6 text-yellow-400 fill-current"
                                                 viewBox="0 0 20 20"
                                                 fill="currentColor">
                                                <path
                                                    d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0
                                                    00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0
                                                    00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54
                                                    1.118l-2.8-2.034a1 1 0 00-1.175
                                                    0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0
                                                    00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1
                                                     0 00.951-.69l1.07-3.292z"
                                                />
                                            </svg>
                                        </template>
                                        <template x-if="i > item.value">
                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                 class="w-6 h-6 text-gray-400 fill-current"
                                                 viewBox="0 0 20 20"
                                                 fill="currentColor">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0
                                                00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364
                                                1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0
                                                00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1
                                                1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1
                                                0 00.951-.69l1.07-3.292z"
                                                />
                                            </svg>
                                        </template>
                                    </div>
                                </template>
                            </div>
                        </template>
                        <div class="mt-1 leading-relaxed">
                            <p class="font-semibold" x-text="review.summary"></p>
                            <p x-text="review.text"></p>
                        </div>
                    </div>
                </div>
            </template>
        </div>
    </template>
    <?= $block->getChildHtml('loading') ?>
    <template x-if="!isLoading && !(reviews && reviews.length)">
        <p><?= $escaper->escapeHtml(__('You have submitted no reviews.')) ?></p>
    </template>
</div>
<script>
    function initReviewList() {
        return {
            customerToken: false,
            onPrivateContentLoaded(data) {
                this.customerToken = data.customer['signin_token'];
                if (this.customerToken) {
                    this.getReviewsList();
                } else {
                    this.isLoading = false;
                }

            },
            currentPage: 1,
            pageSize: 5,
            totalPagesObject: {},
            pageInfo: {},
            isLoading: true,
            reviews: {},
            setCurrentPage(page) {
                this.currentPage = page;
                this.getReviewsList();
            },
            getCustomerReviewsQuery() {
                return <?= /** @noEscape */ json_encode(
                    '{customer {' . $viewModelCustomerReviews->getCustomerReviewsGraphQlQuery() . '}}'
                ); ?>.replace('%currentPage%', this.currentPage).replace('%pageSize%', this.pageSize)
            },
            getReviewsList() {
                this.isLoading = true;
                return fetch(`${BASE_URL}graphql`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Store': '<?= $escaper->escapeHtmlAttr($viewModelStore->getStoreCode()) ?>'
                        },
                        credentials: 'include',
                        body: JSON.stringify({
                            query: this.getCustomerReviewsQuery()
                        })
                    }
                ).then(
                    (response) => response.json()
                ).then((data) => {
                    if (data && data.errors) {
                        this.initErrorMessages(data.errors);
                    } else {
                        this.reviews = (
                            data &&
                            data.data &&
                            data.data.customer &&
                            data.data.customer.reviews &&
                            data.data.customer.reviews.items || []
                        );
                        this.pageInfo = (
                            data &&
                            data.data &&
                            data.data.customer &&
                            data.data.customer.reviews &&
                            data.data.customer.reviews.page_info || []
                        );
                        this.totalPagesObject = (
                            this.pageInfo &&
                            this.pageInfo.total_pages &&
                            Array.from(
                                { length: this.pageInfo.total_pages },
                                (v, i) => i + 1
                            ) || []
                        );
                    }
                    this.isLoading = false;
                });
            },
            initErrorMessages(errors) {
                const messages = [];
                for (const error in Object.keys(errors)) {
                    messages.push({type: 'error', text: errors[error].message});
                }
                dispatchMessages(messages)
            },
        }
    }
</script>
