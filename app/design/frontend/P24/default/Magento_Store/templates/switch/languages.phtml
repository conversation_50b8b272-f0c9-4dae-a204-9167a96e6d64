<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Store;
use Hyva\Theme\ViewModel\StoreSwitcher;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Store\ViewModel\SwitcherUrlProvider;
use Jajuma\HyvaFlags\ViewModel\FlagiconsCircle;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SwitcherUrlProvider $switcherUrlProvider */
$switcherUrlProvider = $viewModels->require(SwitcherUrlProvider::class);

/** @var Store $storeViewModel */
$storeViewModel = $viewModels->require(Store::class);

/** @var StoreSwitcher $storeSwitcherViewModel */
$storeSwitcherViewModel = $viewModels->require(StoreSwitcher::class);

/** @var FlagiconsCircle $flagiconscircle */
$flagiconscircle = $viewModels->require(FlagiconsCircle::class);

$currentStore = $storeSwitcherViewModel->getStore();
$stores = $storeSwitcherViewModel->getStores();
$allLocales = implode("/",array_map(function($store) { return strtoupper(explode("_",$store->getLocaleCode())[0]);}, $stores));
$currentStore = current(array_filter($stores, function($store) use($currentStore) {return $store->getCode() === $currentStore->getCode();}));
?>
<?php
if (count($stores) > 1): ?>
    <div x-data="{ open: false }" class="w-max flex">
        <div class="relative inline-block text-left">
            <div>
                <button @click.prevent="open = !open"
                        @click.outside="open = false"
                        @keydown.window.escape="open=false"
                        type="button"
                        class="flex items-center w-full py-t px-2 focus:outline-none bg-transparent border-0"
                        aria-haspopup="true"
                        aria-expanded="true"
                >
                    <div class="flex items-center gap-2">
                        <?= $flagiconscircle->renderHtml(strtolower(explode("_",$currentStore->getLocaleCode())[1]), "w-4 h-4",24,24,['title' => __('Choose Language %1',$allLocales)]) ?>
                    </div>
                </button>
            </div>
            <nav x-cloak x-show="open" class="z-2 bg-container-blue absolute right-0 px-2 top-6 md:top-10">
                <div role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                    <?php foreach ($stores as $lang): ?>
                        <?php if ($lang->getId() != $storeViewModel->getStoreId()): ?>
                            <a href="<?= $escaper->escapeUrl($switcherUrlProvider->getTargetStoreRedirectUrl($lang)) ?>"
                               class="flex items-center gap-2 py-2">
                                <span class="text-sm leading-6 px-2 font-semibold whitespace-nowrap">
                                    <?= $escaper->escapeHtml($lang->getName()) ?>
                                </span>
                                <?= $flagiconscircle->renderHtml(strtolower(explode("_",$lang->getLocaleCode())[1]), 'w-4 h-4') ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </nav>
        </div>
    </div>
<?php endif; ?>
