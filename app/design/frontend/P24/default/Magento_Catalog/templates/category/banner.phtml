<?php
/**
 * web-vision GmbH
 *
 * NOTICE OF LICENSE
 *
 * <!--LICENSETEXT-->
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.web-vision.de for more information.
 *
 * @category    WebVision
 * @package     P24/default
 * @copyright   Copyright (c) 2001-2021 web-vision GmbH (https://www.web-vision.de)
 * @license     <!--LICENSEURL-->
 * <AUTHOR> <<EMAIL>>
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\View\Element\Template;
use Pumpe24\Category\ViewModel\CategoryData;

/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var CategoryData $categoryViewModel */
$categoryViewModel = $viewModels->require(CategoryData::class);
?>

<div class="container p-0 md:block row-start-1 col-span-3">
    <div class="category-banner w-full">
        <?php
        $topBlock = $categoryViewModel->contentRenderer('category_top','desktop');
        $mobileTopBlock = $categoryViewModel->contentRenderer('category_top','mobile');
        ?>
        <?php if($topBlock) : ?>
        <div class="<?= $mobileTopBlock ? 'hidden md:block' : '' ?>">
            <?= $topBlock ?>
        </div>
        <?php endif; ?>
        <?php if($mobileTopBlock): ?>
        <div class="block md:hidden">
            <?= $mobileTopBlock ?>
        </div>
        <?php endif; ?>
    </div>
</div>
