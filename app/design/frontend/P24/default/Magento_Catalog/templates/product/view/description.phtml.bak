<?php
/**
 * web-vision GmbH
 *
 * NOTICE OF LICENSE
 *
 * <!--LICENSETEXT-->
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.web-vision.de for more information.
 *
 * @category    WebVision
 * @package     P24/default
 * @copyright   Copyright (c) 2001-2021 web-vision GmbH (https://www.web-vision.de)
 * @license     <!--LICENSEURL-->
 * <AUTHOR> <<EMAIL>>
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var Product $product */
$product = $productViewModel->getProduct();

/** @var ProductData $productDataViewModel */
$productDataViewModel = $viewModels->require(\Pumpe24\Product\ViewModel\ProductData::class);

$productAttributes=$product->getAttributes();
$group_id=23;
$attributeSetId = $product->getAttributeSetId();

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$tabs = [];
if ($product->getDescription()) {
	$tabs[] = [
		"title" => __("Description"),
		"content" => "<div id=\"description-wrapper\" class=\"grid mt-1\" "
				. "x-data=\"initProductDetail('description-wrapper', 'description', '')\" "
				. "@adddetail.window=\"addElement(\$event.detail)\" "
				. "@selectdetail.window=\"selectElement(\$event.detail)\" "
			. ">"
				. "<div class=\"transition-all ease-in duration-200 overflow-hidden\" "
					. "x-show=\"selected == 0\" "
					. "x-transition:enter-start=\"opacity-0\" "
					. "x-transition:enter-end=\"opacity-100\" "
					. "x-transition:leave-start=\"opacity-100\" "
					. "x-transition:leave-end=\"opacity-0\" "
					. "style=\"grid-area:1/1;\" "
				. ">"
					. trim($product->getDescription())
				. "</div>"
			. "</div>",
		"showMore" => true
	];
}

if ($product->getTypeId() == "configurable") {
	$tabs[] = [
		"title" => __("Variants"),
		"content" => $block->getChildHtml("variants")
	];
}

if ($product->getRelatedProductIds()) {
	$tabs[] = [
		"title" => __("Accessories"),
		"content" => $block->getChildHtml("related")
	];
}

if ($product->getUpSellProductIds()) {
	$tmp = [
		"title" => __("Alternatives"),
		"content" => $block->getChildHtml("upsells")
	];

	if (!$productDataViewModel->isEndOfLife($product))
		$tabs[] = $tmp;
	else
		array_unshift($tabs, $tmp);
}

$shippingBlock = $this->getLayout()->createBlock("Magento\Cms\Block\Block")->setBlockId("shipping_delivery");
if (($shippingBlock) && (trim($shippingBlock->toHtml()) != "")) {
	$tabs[] = [
		"title" => __("Shipping & delivery"),
		"content" => trim($shippingBlock->toHtml()),
	];
}

?>

<div class="container mx-auto mb-3 lg:mb-6 items-center">
	<div class="grid grid-cols-1 lg:grid-cols-prod-details lg:grid-rows-auto-1fr gap-x-6">
		<div class="lg:pt-3 lg:mr-4 row-start-2 lg:row-start-1 lg:row-span-2"
			x-data="{ tab: '0'<?php for ($x = 0; $x < sizeof($tabs); $x++) echo ", tab" . $x . ": " . ($x == 0 ? "true" : "false");?> }"
		>
			<?php if (sizeof($tabs) > 0): ?>
				<nav class="tab-nav hidden lg:flex grid-cols-2 border-b mb-4 border-gray-300 text-xl lg:text-lg xl:text-xl">
					<?php foreach ($tabs as $key => $tab): ?>
						<span class="grid content-end text-gray-500 font-bold hover:text-black cursor-pointer border-gray-300 border-b sm:border-b-0<?=(($key > 0) ? " sm:border-l" : "")?>"
							@click.prevent="tab = '<?=$key?>'; <?php for ($x = 0; $x < sizeof($tabs); $x++) echo "tab" . $x . " = false; "; ?> "
						>
							<span class="inline-block py-2 sm:pb-1 sm:pt-0 sm:px-2 w-full text-center border-b-3 border-transparent "
								:class="{'active text-black border-applegreen': tab === '<?=$key?>'}"
							>
								<?= $tab["title"] ?>
							</span>
						</span>
					<?php endforeach; ?>
				</nav>
				<div class="grid">
					<?php foreach ($tabs as $key => $tab): ?>
						<div class="lg:grid-area-1-1 bg-container-darker border-applegreen border-b-4 lg:bg-white lg:border-0 mb-4 lg:mb-0 py-3 lg:py-0 px-5 lg:px-0"
							id="tabContainer<?=$key?>"
							x-ref="container<?=$key?>"
						>
							<div class="flex lg:hidden uppercase font-bold text-xl cursor-pointer items-center"
								<?php if ($key != 0): ?>
									@click.prevent="tab<?=$key?> = !tab<?=$key?>; if (tab<?=$key?>) window.scrollTo({top: $refs.container<?=$key?>.offsetTop, behavior: 'smooth'});"
								<?php endif; ?>
							>
								<span class="w-full"><?=$tab["title"]?></span>
								<?php if ($key != 0): ?>
									<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" width="24" height="24"
										class="transform duration-200 transition-all ease-in text-p24blue"
										:class="tab<?=$key?> ? 'rotate-180' : ''"
									>
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
									</svg>
								<?php endif; ?>
							</div>
							<div class="prod-description overflow-hidden transition-all ease-in duration-200"
								x-show="(tab === '<?=$key?>') || (tab<?=$key?>)"
								x-transition:enter-start="opacity-0"
								x-transition:enter-end="opacity-100"
								x-transition:leave-start="opacity-100"
								x-transition:leave-end="opacity-0"
								style="<?=(($key > 0) ? " display:none;" : "")?>"
							>
								<?php if (isset($tab["showMore"])): ?>
									<div class=""
										x-data="{isCollapsed : true, showMore: false, parentContainer: document.querySelector('#tabContainer<?=$key?>')}"
										x-init="isCollapsed = ($refs.container.scrollHeight > $refs.container.offsetHeight); showMore = isCollapsed;"
									>
									<div class="max-h-200 lg:max-h-full overflow-hidden transition-all ease-in duration-200"
										x-ref="container"
										x-bind:style="(($refs.container.scrollHeight) ? (!isCollapsed ? 'max-height: ' + $refs.container.scrollHeight + 'px' : 'max-height: 200px;') : '')"
									>
								<?php endif; ?>
								<?=$tab["content"]?>
								<?php if (isset($tab["showMore"])): ?>
									</div>
									<button
										class="lg:hidden underline text-sm focus:outline-none mt-3 transition-all ease-in duration-200"
										x-show="showMore"
										@click="isCollapsed = !isCollapsed; window.scrollTo({top: parentContainer.offsetTop, behavior: 'smooth'});"
										x-text="isCollapsed ? '<?= $escaper->escapeHtml(__('Show more')); ?>' : '<?= $escaper->escapeHtml(__('Show less')); ?>'"
										x-transition:enter-start="opacity-0"
										x-transition:enter-end="opacity-100"
										x-transition:leave-start="opacity-100"
										x-transition:leave-end="opacity-0"
									>
									</button>
									</div>
								<?php endif; ?>
							</div>
						</div>
					<?php endforeach; ?>
				</div>
			<?php endif; ?>
		</div>

		<div class="row-start-1 mt-7 lg:mt-0 lg:mr-6">
			<?php $keyfacts = $product->getCustomAttribute("keyfacts"); ?>
			<?php if ($keyfacts): ?>
				<?php $keyfacts = trim($keyfacts->getValue()); ?>
					<?php if ($keyfacts != ""): ?>
						<div class="mb-4 py-3 px-5 leading-relaxed border-b-4 border-applegreen product-description bg-container-darker">
							<div class="uppercase text-black pb-2 font-bold text-xl"> <?= $escaper->escapeHtml(__('Keyfacts')); ?></div>
							<div id="keyfacts-wrapper" class="grid"
								x-data="initProductDetail('keyfacts-wrapper', 'keyfacts', 'keyfacts-content')"
								@adddetail.window="addElement($event.detail);"
								@selectdetail.window="selectElement($event.detail);"
							>
								<div class="keyfacts-content grid-area-1-1"
									x-show="selected == 0"
									x-transition:enter-start="opacity-0"
									x-transition:enter-end="opacity-100"
									x-transition:leave-start="opacity-100"
									x-transition:leave-end="opacity-0"
								>
									<?=trim($keyfacts)?>
								</div>
							</div>
						</div>
					<?php endif; ?>
			<?php endif; ?>
		</div>

		<div class="details-wrap row-start-3 lg:row-start-2 lg:mr-6">

			<?php $voltage = $product->getAttributeText("spannung"); ?>
			<?php if (($voltage) && (strpos($voltage, "400") !== false)): ?>
				<div class="mb-4 py-3 px-5 border-b-4 border-applegreen bg-container-darker flex items-center justify-center text-red-500 font-bold">
					<?=$heroicons->exclamationHtml("w-10 h-10 mr-2")?>
					<span>
						<?=$block->escapeHtml(__("installation requires a qualified electrician"))?>
					</span>
				</div>
			<?php endif; ?>

			<?php $ghs = $productDataViewModel->getGhs($product); ?>
			<?php if ($ghs): ?>
				<div class="mb-4 py-3 px-5 border-b-4 border-applegreen bg-container-darker">
					<?php if (isset($ghs->title)): ?>
						<div class="uppercase text-black pb-2 font-bold text-xl"><?=$block->escapeHtml($ghs->title)?></div>
					<?php endif; ?>
					<?php if ((isset($ghs->images)) && (sizeof($ghs->images) > 0)): ?>
						<div class="flex justify-center gap-2 mb-2 h-16">
						<?php foreach ($ghs->images as $img): ?>
							<img src="<?=$block->escapeHtml($img->src)?>" alt="<?=$block->escapeHtml($img->alt)?>" />
						<?php endforeach; ?>
						</div>
					<?php endif; ?>
					<?php if (isset($ghs->text)): ?>
						<div class="text-center text-sm">
							<?=$ghs->text?>
						</div>
					<?php endif; ?>
				</div>
			<?php endif; ?>

			<?php if ($productAttributes): ?>
				<div class="tech-details mb-4 py-3 px-5 border-b-4 border-applegreen text-sm bg-container-darker">
					<div class="uppercase pb-2 font-bold text-xl"> <?= $escaper->escapeHtml(__('Technical details')); ?></div>
					<div x-data="initAttributes(200)"
						x-init="init()"
						@adddetail.window="refresh()"
						@selectdetail.window="refresh()"
					>
						<div class="overflow-hidden transition-all ease-in duration-200"
							x-ref="container"
<?php /*
							x-bind:style="isCollapsed ? 'max-height: ' + maxHeight + 'px' : 'max-height: ' + $refs.container.scrollHeight + 'px'"
							style="max-height:200px;"
*/ ?>
						>
							<div id="attribute-wrapper" class="grid"
								x-data="initProductDetail('attribute-wrapper', 'attributes', 'grid grid-cols-2 grid-cols-prod-details lg:grid-cols-1 xl:grid-cols-2')"
								@adddetail.window="addElement($event.detail);"
								@selectdetail.window="selectElement($event.detail);"
							>
								<div class="grid grid-cols-2 grid-cols-prod-details lg:grid-cols-1 xl:grid-cols-2 overflow-hidden transition-all ease-in duration-200 grid-area-1-1"
									x-show="selected == 0"
									x-transition:enter-start="opacity-0"
									x-transition:enter-end="opacity-100"
									x-transition:leave-start="opacity-100"
									x-transition:leave-end="opacity-0"
								>
									<?php foreach ($productAttributes as $attribute): ?>
											<?php if (($attribute->isInGroup($attributeSetId, $group_id)) && ($attribute->getFrontend()->getValue($product)) && ($attribute->getAttributeCode() != "einstufung_nach_eg_richtlinien")): ?>
												<div class="font-bold break-words"><?=$attribute->getFrontendLabel()?>:</div><div class="break-words"><?=$attribute->getFrontend()->getValue($product)?>&nbsp;<?=$attribute->getData("p24_unit")?></div>
											<?php endif; ?>
									<?php endforeach; ?>
								</div>
							</div>
						</div>
<?php /*
						<button
							class="underline text-sm focus:outline-none mt-3 transition-all ease-in duration-200 text-p24blue"
							@click="isCollapsed = !isCollapsed"
							x-text="isCollapsed ? '<?= $escaper->escapeHtml(__('Show more')); ?>' : '<?= $escaper->escapeHtml(__('Show less')); ?>'"
							x-show="showMore"
							x-transition:enter-start="opacity-0"
							x-transition:enter-end="opacity-100"
							x-transition:leave-start="opacity-100"
							x-transition:leave-end="opacity-0"
						>
						</button>
*/ ?>
					</div>
				</div>
			<?php endif; ?>

		<?php if (($product->getCustomAttribute('p24_show_consultation')) && ($product->getCustomAttribute('p24_show_consultation')->getValue())): ?>
			<button
				class="btn-green p-2 mb-4 w-full lg:hidden"
				x-data=""
				@click="$dispatch('opencontactmodal','product')"
			>
				<span class="m-auto">
					<?=__("I need consultation");?>
				</span>
			</button>
		<?php endif; ?>

			<?php if (trim($block->getChildHtml('productattachment'))) : ?>
				<div class="download py-3 px-5 leading-relaxed border-b-4 border-applegreen product-description bg-container-darker">
					<div class="uppercase text-black pb-2 font-bold text-xl"> <?= $escaper->escapeHtml('Download'); ?></div>
					<?= $block->getChildHtml("productattachment"); ?>
				</div>
			<?php endif; ?>
		</div>
	</div>
</div>
