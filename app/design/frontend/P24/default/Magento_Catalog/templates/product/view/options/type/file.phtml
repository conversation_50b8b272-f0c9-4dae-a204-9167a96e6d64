<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Helper\SecureHtmlRenderer;
use Magento\Catalog\Block\Product\View\Options\Type\File;

/** @var File $block */
/** @var Escaper $escaper */
/** @var SecureHtmlRenderer $secureRenderer */
?>
<?php $option = $block->getOption(); ?>
<?php $fileInfo = $block->getFileInfo(); ?>
<?php $fileExists = $fileInfo->hasData(); ?>
<?php $fileName = 'options_' . $escaper->escapeHtmlAttr($option->getId()) . '_file'; ?>
<?php $fieldNameAction = $fileName . '_action'; ?>
<?php $fieldValueAction = $fileExists ? 'save_old' : 'save_new'; ?>
<?php $fileNamed = $fileName . '_name'; ?>

<div class="flex border-t border-gray-300 py-2 last:mb-6 last:border-b w-full items-center">
    <label class="label text-gray-700 text-left w-1/2"
           for="options_<?= $escaper->escapeHtmlAttr($option->getId()) ?>_text">
        <span><?= $escaper->escapeHtml($option->getTitle()) ?></span>
        <?php if ($option->getIsRequire()): ?>
            <span class="sup text-sm">*</span>
        <?php endif; ?>
        <span x-html="getFormattedOptionPrice(<?= $escaper->escapeHtmlAttr($option->getId()) ?>) || ''">
            <?= /* @noEscape */ $block->getFormattedPrice() ?>
        </span>
    </label>

    <?php if ($fileExists):?>
    <div class="ml-2 text-gray-900 text-left w-1/2">
        <span class="<?= /* @noEscape */ $fileNamed ?>"><?= $escaper->escapeHtml($fileInfo->getTitle()) ?></span>
        <a href="#"
           class="btn btn-secondary inline-block ml-2"
           id="change-<?= /* @noEscape */ $fileName ?>"
           @click.prevent="$event.target.parentNode.remove();
                document.getElementById('input-box-<?= /* @noEscape */ $fileName ?>').style.display = 'block';
                $refs['option-<?= $escaper->escapeHtmlAttr($option->getId()) ?>'].disabled = false;"
        >
            <?= $escaper->escapeHtml(__('Change')) ?>
        </a>
    </div>
    <?php endif; ?>
    <div class="ml-2 text-gray-900 text-left w-1/2 <?= $fileExists ? 'display:none' : ''?>"
         id="input-box-<?= /* @noEscape */ $fileName ?>"
    >
        <input type="file"
               name="<?= /* @noEscape */ $fileName ?>"
               id="<?= /* @noEscape */ $fileName ?>"
               class="product-custom-option form-file"
            <?= $fileExists ? 'disabled="disabled"' : '' ?>
            <?= $option->getIsRequire() ? 'required' : '' ?>
               data-price-amount="<?= $escaper->escapeHtmlAttr($option->getPrice()) ?>"
               data-price-type="<?= $escaper->escapeHtmlAttr($option->getPriceType()) ?>"
               x-ref="option-<?= $escaper->escapeHtmlAttr($option->getId()) ?>"
               x-on:change="updateCustomOptionValue(
                    $dispatch,
                    '<?= $escaper->escapeHtmlAttr($option->getId()) ?>',
                    $event.target
                )"

        />
        <input type="hidden" name="<?= /* @noEscape */ $fieldNameAction ?>"
               value="<?= /* @noEscape */ $fieldValueAction ?>" />
        <?php if ($option->getFileExtension()):?>
            <p class="my-2 text-sm">
                <?= $escaper->escapeHtml(__('Compatible file extensions to upload')) ?>:
                <strong><?= $escaper->escapeHtml($option->getFileExtension()) ?></strong>
            </p>
        <?php endif; ?>
        <?php if ($option->getImageSizeX() > 0):?>
            <p class="my-2 text-sm">
                <?= $escaper->escapeHtml(__('Maximum image width')) ?>: <strong><?= (int)$option->getImageSizeX()
                ?> <?= $escaper->escapeHtml(__('px.')) ?></strong>
            </p>
        <?php endif; ?>
        <?php if ($option->getImageSizeY() > 0):?>
            <p class="my-2 text-sm">
                <?= $escaper->escapeHtml(__('Maximum image height')) ?>: <strong><?= (int)$option->getImageSizeY()
                ?> <?= $escaper->escapeHtml(__('px.')) ?></strong>
            </p>
        <?php endif; ?>
    </div>
    <?= $fileExists ?
        /* @noEscape */ $secureRenderer->renderStyleAsTag(
            'display:none',
            '#input-box-' . /* @noEscape */ $fileName
        ) : '' ?>
</div>
