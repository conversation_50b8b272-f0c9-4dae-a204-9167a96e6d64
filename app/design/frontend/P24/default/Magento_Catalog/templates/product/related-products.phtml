<?php
/**
 * web-vision GmbH
 *
 * NOTICE OF LICENSE
 *
 * <!--LICENSETEXT-->
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade Magento to newer
 * versions in the future. If you wish to customize Magento for your
 * needs please refer to http://www.web-vision.de for more information.
 *
 * @category    WebVision
 * @package     P24/default
 * @copyright   Copyright (c) 2001-2021 web-vision GmbH (https://www.web-vision.de)
 * @license     <!--LICENSEURL-->
 * <AUTHOR> <<EMAIL>>
 */

declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Catalog\Block\Product\View\Gallery;
use Magento\Catalog\Helper\Image;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductPage;

/** @var Gallery $block */
/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var Product $product */
$product = $productViewModel->getProduct();
?>
<?php if ($product->getRelatedProductIds()): ?>
	<?php
		$images = $block->getGalleryImages()->getItems();
		$mainImageData = "";
		foreach ($images as $image) {
			$mainImageData = $image->getData("medium_image_url");
			break;
		}

		$helper = $block->getData('imageHelper');
		$smallWidth = $block->getImageAttribute('product_page_image_small', 'width', 90);
		$smallHeight = $block->getImageAttribute('product_page_image_small', 'height', 90);
		$mediumWidth = $block->getImageAttribute('product_page_image_medium', 'width', 700);
		$mediumHeight = $block->getImageAttribute('product_page_image_medium', 'height', 700);
	?>
	<div class="related-products-wrap hidden lg:block container py-6 xl:grid-cols-related-bottom xl:grid">
		<div class="img-wrap hidden xl:flex">
			<div class="relative self-center w-full">
				<img
					alt="<?=$block->getProduct()->getName()?>"
					title="<?=$block->getProduct()->getName()?>"
					class="object-contain object-center w-full h-auto max-h-75"
					loading="lazy"
					src="<?=$mainImageData ?>"
					width="<?=$mediumWidth ?>"
					height="<?=$mediumHeight ?>"
				/>
			</div>

			<div class="place-self-center md:pt-10 ml-5">
				<svg xmlns="http://www.w3.org/2000/svg" fill="#0168B3" class="w-12 h-12" viewBox="0 0 24 24" stroke="#0168B3">
					<path stroke-linecap="round" stroke-linejoin="round" stroke-width="0.5" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
				</svg>
			</div>
		</div>

		<div class="xl:ml-5">
			<h2 class="text-gray-900 md:ml-8 lg:ml-10 text-xl title-font font-medium font-base text-center md:text-left w-full">
				<?= $escaper->escapeHtml(__('Suitable accessories')); ?>
			</h2>
			<?=$block->getChildBlock("related-bottom-slider")->setProductColl($product->getRelatedProductCollection())->toHtml();?>
		</div>

	</div>
<?php endif; ?>
