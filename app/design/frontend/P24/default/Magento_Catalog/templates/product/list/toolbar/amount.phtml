<?php

declare(strict_types=1);

use Hyva\Theme\Model\LocaleFormatter;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var LocaleFormatter $localeFormatter */
?>
<p class="block md:hidden text-xs text-primary-lighter font-light absolute right-0 -top-7" id="toolbar-amount" class="w-max">
    <span>(</span>
    <?php if ($block->getTotalNum() == 1): ?>
    <?= $escaper->escapeHtml(
        __('%1 Product', '<span>' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'),
        ['span']
    ) ?>
    <?php else:?>
        <?= $escaper->escapeHtml(
            __('%1 Products', '<span>' . $localeFormatter->formatNumber($block->getTotalNum()) . '</span>'),
            ['span']
        ) ?>
    <?php endif; ?>
    <span>)</span>
</p>
