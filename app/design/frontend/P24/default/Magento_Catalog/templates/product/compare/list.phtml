<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

// phpcs:disable Magento2.Templates.ThisInTemplate
// phpcs:disable PSR2.ControlStructures.SwitchDeclaration
// phpcs:disable Generic.WhiteSpace.ScopeIndent

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\Wishlist;
use Magento\Catalog\Block\Product\Compare\ListCompare;
use Magento\Catalog\Helper\Product\Compare;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Wishlist\Helper\Data as WishlistHelper;

/** @var ListCompare $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Compare $compareHelper */
$compareHelper = $this->helper(Compare::class);
/** @var WishlistHelper $wishlistHelper */
$wishlistHelper = $this->helper(WishlistHelper::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var Wishlist $wishlistViewModel */
$wishlistViewModel = $viewModels->require(Wishlist::class);
?>
<?php $total = $block->getItems()->getSize(); ?>
<?php if ($total): ?>
<script>
    function initPrint() {
        return {
            print() {
                window.print();
            }
        };
    }

    function initCompareOnCompareList() {
        return {
            removeFromCompare(productId, productName) {
                const formKey = hyva.getFormKey();
                const postUrl = BASE_URL + 'catalog/product_compare/remove/';
                const confirmMessage = "<?= $escaper->escapeHtml(
                    __('Are you sure you want to remove this item from your Compare Products list?')
                ) ?>";
                if (!window.confirm(confirmMessage)) {
                    return;
                }

                fetch(postUrl, {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": "form_key=" + formKey + "&product=" + productId + "&uenc=" + hyva.getUenc(),
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                }).then(function (response) {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                            [{
                                type: "warning",
                                text: "<?= $escaper->escapeHtml(__('Could not remove item from compare.')) ?>"
                            }], 5000
                        );
                    }
                }).then(function (response) {
                    if (!response) { return; }
                    typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                        [{
                            type: (response.success) ? "success" : "error",
                            text: (response.success)
                                ? "<?= $escaper->escapeHtml(
                                    __("You removed product %1 from the comparison list")
                                ) ?>".replace('%1', productName)
                                : response.error_message
                        }], 5000
                    );
                    let reloadCustomerDataEvent = new CustomEvent("reload-customer-section-data");
                    window.dispatchEvent(reloadCustomerDataEvent);
                }).catch(function (error) {
                    typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                        [{
                            type: "error",
                            text: error
                        }], 5000
                    );
                });
            }
        };
    }

    function initWishlist() {
        return {
            addToWishlist(productId) {
                const formKey = hyva.getFormKey();
                const postUrl = BASE_URL + 'wishlist/index/add/';

                fetch(postUrl, {
                    "headers": {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                    "body": "form_key=" + formKey + "&product=" + productId + "&uenc=" + hyva.getUenc(),
                    "method": "POST",
                    "mode": "cors",
                    "credentials": "include"
                }).then(function (response) {
                    if (response.redirected) {
                        window.location.href = response.url;
                    } else if (response.ok) {
                        return response.json();
                    } else {
                        typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                            [{
                                type: "warning",
                                text: "<?= $escaper->escapeHtml(__('Could not add item to wishlist.')) ?>"
                            }], 5000
                        );
                    }
                }).then(function (response) {
                    if (!response) { return }
                    typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                        [{
                            type: (response.success) ? "success" : "error",
                            text: (response.success)
                                ? "<?= $escaper->escapeHtml(__("%1 has been added to your Wish List.", __("Product"))) ?>"
                                : response.error_message
                        }], 5000
                    );
                    const reloadCustomerDataEvent = new CustomEvent("reload-customer-section-data");
                    window.dispatchEvent(reloadCustomerDataEvent);
                }).catch(function (error) {
                    typeof window.dispatchMessages !== "undefined" && window.dispatchMessages(
                        [{
                            type: "error",
                            text: error
                        }], 5000
                    );
                });
            }
        }
    }
</script>
<section>
    <div class="container">
        <button x-data="initPrint()"
            @click.prevent="print()"
            class="mb-2"
            title="<?= $escaper->escapeHtmlAttr(__('Print This Page')) ?>">
            <svg class="w-6 h-6 inline"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2
                    0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z">
                </path>
            </svg>
            <span><?= $escaper->escapeHtml(__('Print This Page')) ?></span>
        </button>
    </div>
    <div class="table-wrapper container md:mx-auto md:px-6 overflow-x-auto py-4">
        <table class="table-auto">
            <caption class="sr-only"><?= $escaper->escapeHtml(__('Compare Products')); ?></caption>
            <tbody>
                <tr>
                    <?php $index = 0; ?>
                    <?php /** @var Product $product */ ?>
                    <?php foreach ($block->getItems() as $product): ?>
                        <?php if ($index++ === 0): ?>
                            <th scope="row" class="min-w-40 border border-gray-300 py-2 px-3 text-left align-top">
                                <span><?= $escaper->escapeHtml(__('Product')) ?></span>
                            </th>
                        <?php endif; ?>
                        <td class="relative min-w-48 bg-gray-100 border border-gray-300 py-2 px-3 align-top">
                            <div class="flex flex-col">
                                <a href="<?= $escaper->escapeUrl($block->getProductUrl($product)) ?>"
                                    class="block mb-3"
                                    title="<?= $escaper->escapeHtmlAttr($product->getName()) ?>">
                                    <?= $block->getImage(
                                        $product,
                                        'product_comparison_list'
                                    )->setTemplate('Magento_Catalog::product/image.phtml')->toHtml() ?>
                                </a>
                                <strong>
                                    <a href="<?= $escaper->escapeUrl($block->getProductUrl($product)) ?>"
                                        title="<?= $escaper->escapeHtmlAttr($product->getName()) ?>">
                                        <?= $escaper->escapeHtml($product->getName()) ?>
                                    </a>
                                </strong>
                                <?= /* @noEscape */ $block->getProductPrice($product, '-compare-list-top') ?>
                                <div class="flex mt-auto pt-3 items-center">
                                    <?php if ($product->isSaleable()): ?>
                                        <form data-role="tocart-form"
                                            action="<?= $escaper
                                                ->escapeUrl($compareHelper->getAddToCartUrl($product)) ?>"
                                            method="post"
                                            class="mr-auto">
                                            <?= $block->getBlockHtml('formkey') ?>
                                            <button type="submit"
                                                    class="w-auto mr-auto btn btn-primary justify-center text-sm px-2"
                                                    aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Cart')) ?>"
                                            >
                                                <svg class="h-6 w-6 border-current inline"
                                                     fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round"
                                                          stroke-linejoin="round"
                                                          stroke-width="2"
                                                          d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293
                                                          2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100
                                                          4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
                                                    />
                                                </svg>
                                                <span class="sr-only">
                                                    <?= $escaper->escapeHtml(__('Add to Cart')) ?>
                                                </span>
                                            </button>
                                        </form>
                                    <?php endif; ?>
                                    <?php if ($wishlistViewModel->isEnabled()): ?>
                                        <button x-data="initWishlist()"
                                            @click.prevent="addToWishlist(<?= (int)$product->getId() ?>)"
                                            aria-label="<?= $escaper->escapeHtmlAttr(__('Add to Wish List')) ?>"
                                            class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex
                                            flex-shrink-0 items-center justify-center text-gray-500 hover:text-red-600
                                            ml-2"
                                        >
                                            <svg fill="currentColor" stroke-linecap="round"
                                                 stroke-linejoin="round" stroke-width="2" class="w-5 h-5"
                                                viewBox="0 0 24 24">
                                                <path d="M20.84 4.61a5.5 5.5 0 00-7.78 0L12 5.67l-1.06-1.06a5.5 5.5
                                                    0 00-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0
                                                    000-7.78z"
                                                />
                                            </svg>
                                        </button>
                                    <?php endif; ?>
                                    <button x-data="initCompare()"
                                        @click.prevent="removeFromCompare(<?= (int)$product->getId() ?>, '<?= $escaper
                                            ->escapeJs($product->getName()) ?>')"
                                        aria-label="<?= $escaper->escapeHtmlAttr(__('Remove Product')) ?>"
                                        class="rounded-full w-9 h-9 bg-gray-200 p-0 border-0 inline-flex flex-shrink-0
                                        items-center justify-center text-gray-500 hover:text-black ml-2"
                                        title="<?= $escaper->escapeHtmlAttr(__('Remove Product')) ?>">
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none"
                                             viewBox="0 0 24 24" stroke="currentColor" stroke-width="2" class="w-5 h-5">
                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                  stroke-width="2" d="M6 18L18 6M6 6l12 12"
                                            />
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </td>
                    <?php endforeach; ?>
                </tr>
                <tr>
                    <?php $index = 0; ?>
                    <?php /** @var Product $product */ ?>
                    <?php foreach ($block->getItems() as $product): ?>
                        <?php if ($index++ === 0): ?>
                            <th scope="row" class="min-w-40 border border-gray-300 py-2 px-3 text-left align-top">
                                <span><?= $escaper->escapeHtml(__('Review score')) ?></span>
                            </th>
                        <?php endif; ?>
                        <td class="relative min-w-48 border border-gray-300 py-2 px-3 align-top">
                            <?= $block->getReviewsSummaryHtml($product, 'short') ?>
                        </td>
                    <?php endforeach; ?>
                </tr>
                <?php foreach ($block->getAttributes() as $attribute): ?>
                    <?php $index = 0; ?>
                    <?php if ($block->hasAttributeValueForProducts($attribute)): ?>
                        <tr>
                            <?php /** @var Product $product */ ?>
                            <?php foreach ($block->getItems() as $product): ?>
                                <?php if ($index++ === 0): ?>
                                    <th scope="row" class="border border-gray-300 py-2 px-3 text-left align-top">
                                        <span>
                                            <?= $escaper
                                                ->escapeHtml(
                                                    $attribute->getStoreLabel() ?:
                                                        __($attribute->getFrontendLabel())
                                                ) ?>
                                        </span>
                                    </th>
                                <?php endif; ?>
                                <td class="border border-gray-300 py-2 px-3 align-top">
                                    <div>
                                        <?php switch ($attribute->getAttributeCode()) {
                                            case 'price': ?>
                                                <?=
                                                /* @noEscape */ $block->getProductPrice(
                                                    $product,
                                                    '-compare-list-' . $attribute->getCode()
                                                )
                                                ?>
                                                <?php break;
                                            case 'small_image': ?>
                                                <?= $block->getImage($product, 'product_small_image')->toHtml(); ?>
                                                <?php break;
                                            case 'short_description':
                                            case 'description': ?>
                                                    <?php
                                                    $attributeData = $attributesViewModel->getAttributeData(
                                                        $attribute,
                                                        $product
                                                    )
                                                    ?>
                                                    <?= /* @noEscape */ $attributeData['value'] ?>
                                                <?php break;
                                            default :?>
                                                    <?php
                                                    $attributeData = $attributesViewModel->getAttributeData(
                                                        $attribute,
                                                        $product
                                                    )
                                                    ?>
                                                    <?= $escaper->escapeHtml($attributeData['value'], ['p', 'br']) ?>
                                                <?php break;
                                        } ?>
                                    </div>
                                </td>
                            <?php endforeach; ?>
                        </tr>
                    <?php endif; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</section>
<?php else: ?>
<section>
    <div class="container"><?= $escaper->escapeHtml(__('You have no items to compare.')) ?></div>
</section>
<?php endif; ?>
