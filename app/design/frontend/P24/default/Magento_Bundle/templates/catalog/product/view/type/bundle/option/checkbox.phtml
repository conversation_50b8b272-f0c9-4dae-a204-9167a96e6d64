<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Magento\Bundle\Block\Catalog\Product\View\Type\Bundle\Option\Checkbox;
use Magento\Framework\Escaper;

/** @var Checkbox $block */
/** @var Escaper $escaper */

$option = $block->getOption();
$selections = $option->getSelections();
$default = $option->getDefaultSelection();

$optionId = $option->getId();
?>
<?php if ($block->showSingle()): ?>
    <?= /* @noEscape */ $block->getSelectionQtyTitlePrice($selections[0]) ?>
    <?= /* @noEscape */ $block->getTierPriceRenderer()->renderTierPrice($selections[0]) ?>
    <input type="hidden"
           class="bundle-option-<?= (int)$optionId ?>  product bundle option"
           name="bundle_option[<?= (int)$optionId ?>]"
           form="product_addtocart_form"
           value="<?= (int)$selections[0]->getSelectionId() ?>"
           id="bundle-option-<?= (int)$optionId ?>-<?= (int)$selections[0]->getSelectionId() ?>"
           checked="checked"
           form="product_addtocart_form"
           data-option-id="<?= (int)$optionId ?>"
           data-selection-id="<?= (int)$selections[0]->getSelectionId() ?>"
           x-ref="option-<?= (int)$optionId ?>"
    />
<?php else: ?>
    <?php foreach ($selections as $selection): ?>
        <?php $selectionId = $selection->getSelectionId(); ?>
        <?php $optionSelectionId = $optionId . '-' . $selectionId ?>
        <div class="field choice mt-1 flex items-center gap-x-2 my-1">
            <input class="bundle-option-<?= (int)$optionId ?>
                form-checkbox"
                   id="bundle-option-<?= $escaper->escapeHtmlAttr($optionSelectionId) ?>"
                   name="bundle_option[<?= (int)$optionId ?>][<?= (int)$selectionId ?>]"
                   form="product_addtocart_form"
                   type="checkbox"
                   <?php if ($block->isSelected($selection)) { echo ' checked="checked"'; } ?>
                   <?php if (!$selection->isSaleable()): ?>
                       disabled="disabled"
                   <?php endif; ?>
                   value="<?= (int)$selectionId ?>"
                   x-ref="option-<?= $escaper->escapeHtmlAttr($optionSelectionId) ?>"
                   x-on:change="calculateTotalPrice($dispatch)"
            />
            <label class="label"
                   for="bundle-option-<?= $escaper->escapeHtmlAttr($optionSelectionId) ?>">
                <span><?= /* @noEscape */ $block->getSelectionQtyTitlePrice($selection) ?></span>
            </label>
        </div>
        <?= /* @noEscape */ $block->getTierPriceRenderer()->renderTierPrice($selection) ?>
    <?php endforeach; ?>
    <div id="bundle-option-<?= (int)$optionId ?>-container"></div>
<?php endif; ?>
