<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Customer\Block\Account\SortLinkInterface" ifconfig="wishlist/general/active" name="customer-account-navigation-wish-list-link">
                <arguments>
                    <argument name="path" xsi:type="string">wishlist</argument>
                    <argument name="label" xsi:type="string" translate="true">My Wish List</argument>
                    <argument name="sortOrder" xsi:type="number">210</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
