<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\Wishlist\Block\Customer\Wishlist\Item\Column\Remove;
use Magento\Framework\Escaper;

/** @var Remove $block */
/** @var Escaper $escaper */
?>

<a href="#"
   data-role="remove"
   @click.prevent='hyva.postForm(<?= /* @noEscape */ $block->getItemRemoveParams($block->getItem()) ?>)'
   title="<?= $escaper->escapeHtmlAttr(__('Remove Item')) ?>"
   class="btn-remove action delete btn btn-secondary inline-flex p-2">
    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor" size="16" class="h-5 w-5">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5
              4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16">
        </path>
    </svg>
    <span class="sr-only"><?= $escaper->escapeHtml(__('Remove item')) ?></span>
</a>
