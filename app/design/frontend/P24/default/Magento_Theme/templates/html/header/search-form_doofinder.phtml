<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Search\Helper\Data as SearchHelper;

// phpcs:disable Magento2.Templates.ThisInTemplate.FoundHelper

/** @var Escaper $escaper */
/** @var Template $block */
/** @var SearchHelper $helper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$helper = $this->helper(SearchHelper::class);
?>
<div class="h-full leading-10 mx-auto text-black">
	<div class="form minisearch grid grid-cols-search-form h-full" id="search_mini_form">
		<label class="hidden" for="search" data-role="minisearch-label">
			<span><?= $escaper->escapeHtml(__('Search')) ?></span>
		</label>
		<input id="search"
			aria-label="Search"
			x-ref="searchInput"
			name="<?= $escaper->escapeHtmlAttr($helper->getQueryParamName()) ?>"
			value="<?= $escaper->escapeHtml($helper->getEscapedQueryText(), "\"") ?>"
			placeholder="<?= $escaper->escapeHtmlAttr(__('What are you looking for?')) ?>"
			maxlength="<?= $escaper->escapeHtmlAttr($helper->getMaxQueryLength()) ?>"
			autocomplete="off"
			class="search-input pl-5 leading-normal appearance-none text-grey-800 text-base border-container-blue border focus:outline-none z-1 pr-11 bg-transparent w-full"
		/>
		<div class="search-icon w-11 h-full z-0 -ml-11">
			<?=$heroicons->searchHtml("w-6 text-gray-500 m-auto h-full", 25, 25)?>
		</div>
	</div>
</div>
