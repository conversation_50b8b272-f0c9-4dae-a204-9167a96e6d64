
<button onclick="window.dispatchEvent(new CustomEvent('scrolltotop'))"
        class="scroll-to-top btn hidden transition z-20 left-5 border-gray-500 border fixed p-2 bg-white rounded"
        aria-label="<?= __('Back To Top') ?>"
        style="opacity: 0;"
        id="backtotop"
>
    <span class="gt-arrow">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" height="24" width="24" stroke="currentColor" class="transform rotate-180">
            <path stroke-linecap="round" stroke="#6B7280" stroke-linejoin="round" stroke-width="3" d="M19 9L12 16L5 9"></path>
        </svg>
    </span>
</button>
<script defer>
    (() => {
        window.addEventListener('scrolltotop',() => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
        const backtotop = document.querySelector('#backtotop');
        document.addEventListener('scroll', function(e) {
            if(window.scrollY > 400){
                backtotop.style.opacity = "0.9";
                backtotop.style.visibility = "";
                backtotop.classList.remove('hidden');
            }else {
                backtotop.style.opacity = "0";
                backtotop.style.visibility = "hidden";
            }
        });
    }) ();
</script>
