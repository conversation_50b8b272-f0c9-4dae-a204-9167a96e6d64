<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

?>
<section id="messages"
         x-data="initMessages()"
         @messages-loaded.window="addMessages(event.detail.messages, event.detail.hideAfter)"
         @private-content-loaded.window="getData(event.detail.data)"
>
    <template x-if="!isEmpty()">
        <div class="w-full">
            <div role="alert" class="messages container mx-auto">
                <template x-for="(message, index) in messages" :key="index">
                    <div>
                        <template x-if="message">
                            <div :class="message.type
                + ' message flex items-center justify-between w-full p-2 bg-gray-600 shadow text-white my-2'"
                                 :ui-id="'message-' + message.type"
                            >
                                <span x-html="message.text"></span>
                                <a href="#" class="close cursor-pointer" title="close"
                                   @click.prevent="removeMessage(index)">
                                    <svg class="fill-current text-white" xmlns="http://www.w3.org/2000/svg"
                                         width="18" height="18" viewBox="0 0 18 18">
                                        <path
                                            d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47
                                                4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z">
                                        </path>
                                    </svg>
                                </a>
                            </div>
                        </template>
                    </div>
                </template>
            </div>
        </div>
    </template>
</section>
