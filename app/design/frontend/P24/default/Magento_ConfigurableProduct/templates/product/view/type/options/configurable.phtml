<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Magento\ConfigurableProduct\Block\Product\View\Type\Configurable;
use Magento\Framework\Escaper;

/** @var Configurable $block */
/** @var Escaper $escaper */

$product = $block->getProduct();
$productId = $product->getId();
$attributes = $block->decorateArray($block->getAllowAttributes());
?>
<?php if ($product->isSaleable() && count($attributes)):?>

    <?= $block->getChildHtml('options_configurable_js') ?>

    <div x-data="initConfigurableOptions('<?= (int) $productId ?>', <?= /* @noEscape */ $block->getJsonConfig() ?>)"
         x-init="findAllowedAttributeOptions(); preselectQuerystringItems();"
         class="mb-6"
    >
        <h2 class="text-gray-900 text-xl title-font font-medium mb-4">
            <?= $escaper->escapeHtml(__('Options')) ?>
        </h2>

        <?php foreach ($attributes as $attribute): ?>
            <div class="flex border-t last:border-b  border-gray-300 py-2 w-full items-center">

                <label class="label text-gray-700 text-left w-1/2"
                       for="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                >
                <span>
                    <?= $escaper->escapeHtml($attribute->getProductAttribute()->getStoreLabel()) ?>
                </span>
                </label>
                <div class="ml-2 text-gray-900 text-left w-1/2">
                    <select name="super_attribute[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>]"
                            id="attribute<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>"
                            class="form-select super-attribute-select"
                            x-on:change="changeOption(<?= (int) $attribute->getAttributeId() ?>, event.target.value)"
                            required="required">
                        <option value="">
                            <?= $escaper->escapeHtml(__('Choose an Option...')) ?>
                        </option>
                        <template
                            x-for="(item, index) in getAllowedAttributeOptions(<?= (int) $attribute->getAttributeId() ?>)"
                            :key="item.id"
                        >
                            <option
                                :value="item.id"
                                x-html="item.label"
                                :selected="selectedValues[<?= $escaper->escapeHtmlAttr($attribute->getAttributeId()) ?>] ===
                                item.id">
                            </option>
                        </template>
                    </select>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
<?php endif;?>
