<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;
use Hyva\Theme\ViewModel\Cart\TotalsOutput;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var TotalsOutput $viewModelTotalsOutput */

$viewModelTotalsOutput = $viewModels->require(TotalsOutput::class);
$subtotalField = $viewModelTotalsOutput->getSubtotalField();
$subtotalFieldShowBoth = $viewModelTotalsOutput->getSubtotalFieldDisplayBoth();
$taxLabelAddition = $viewModelTotalsOutput->getTaxLabelAddition();

$p24Helper = $viewModels->require(\Pumpe24\Helper\ViewModel\Helper::class);
?>
	<div class="pt-2">
		<template x-if="cartData.p24CouponCode">
			<div class="grid gap-1 mx-2 border-b border-gray-200 text-lg">
				<div class="flex flex-row">
					<div class="w-full ml-1 text-left">
						<?= $escaper->escapeHtml(__('Subtotal')) ?>
					</div>
					<div
						x-text="window.hyva.formatPrice(cartData.p24SubTotal)">
					</div>
				</div>

				<template x-if="cartData.p24CouponCode">
					<div class="flex flex-row">
						<div class="w-full ml-1 text-left">
							<?= $escaper->escapeHtml(__('Discount')) ?>
						</div>
						<div
							x-text="'-&nbsp;' + window.hyva.formatPrice(cartData.p24DiscountAmount)"
						>
						</div>
					</div>
				</template>
			</div>
		</template>

		<template x-if="cartData.p24GrandTotal">
			<div class="mx-2">
				<div class="flex flex-row text-lg font-bold">
					<div class="w-full ml-1 text-left">
						<?= $escaper->escapeHtml(__('Grand Total')) ?>
					</div>
					<div
						x-text="window.hyva.formatPrice(cartData.p24GrandTotal, false)"
					>
					</div>
				</div>
				<div class="text-right text-sm">
					<?php if ($p24Helper->isCustomerVatFree()): ?>
						<span><?=__("plus shipping costs");?></span>
					<?php else: ?>
						<span><?=__("incl. VAT plus shipping costs");?></span>
					<?php endif; ?>
				</div>
			</div>
		</template>
	</div>
