<?xml version="1.0"?>
<!--
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<head>
		<meta name="robots" content="NOINDEX,FOLLOW"/>
	</head>
    <update handle="checkout_cart_item_renderers"/>
    <body>
        <move element="p24_share_cart" destination="cart_dynamic" />
        <move element="p24_cart_compatibility_check" destination="items" />

        <referenceBlock name="crosssell" remove="true" />

        <referenceBlock name="header-content">
            <arguments>
                <argument name="cart_amount_hidden" xsi:type="boolean">true</argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="page.messages">
            <block class="Magento\Checkout\Block\Cart\ValidationMessages" name="checkout.cart.validationmessages"/>
        </referenceContainer>
        <referenceContainer name="content">
            <block name="cart_dynamic" template="Magento_Checkout::cart.phtml" cacheable="false">
                <block name="loading" template="Hyva_Theme::ui/loading.phtml"/>
                <block name="items" template="Magento_Checkout::cart/items.phtml">
                </block>
                <block name="coupon" class="Magento\Checkout\Block\Cart\Coupon"
                       template="Magento_Checkout::cart/coupon/form.phtml"/>
                <block name="totals" template="Magento_Checkout::cart/totals.phtml"/>
                <block name="empty" template="Magento_Checkout::cart/noItems.phtml"/>
                <block name="checkout.cart.methods.bottom" class="Magento\Checkout\Block\Cart"
                       template="Magento_Checkout::cart/methods.phtml">
                    <container name="checkout.cart.methods" as="methods" label="Payment Methods After Checkout Button">
                        <block class="Magento\Checkout\Block\Onepage\Link" name="checkout.cart.methods.onepage.bottom"
                               template="Magento_Checkout::onepage/link.phtml"/>
                        <block class="Magento\Checkout\Block\QuoteShortcutButtons"
                               name="checkout.cart.shortcut.buttons"/>
                    </container>
                </block>
		<block name="p24_shipping_costs" template="Magento_Checkout::cart/p24_shipping_costs.phtml" />
		<block name="p24_installment_hint" template="Magento_Checkout::cart/p24_installment_hint.phtml" />

		<block name="crosssell_cart" template="Magento_Checkout::cart/related.phtml" class="Magento\Checkout\Block\Cart\Crosssell">
			<block name="crosssell_items"
				class="Magento\Catalog\Block\Product\AbstractProduct"
				template="Magento_Catalog::product/list/slider.phtml"
			/>
		</block>

            </block>
        </referenceContainer>
        <referenceBlock name="checkout.cart" remove="true" />
        <referenceBlock name="hyva-theme-modal" remove="true" />
        <referenceBlock name="checkout.cart.methods.onepage.bottom.php-cart" remove="true" />
        <referenceBlock name="magefan.blog.custom.css" remove="true" />
        <referenceBlock name="mfblog.lazyload.js" remove="true" />
        <referenceBlock name="footer_blog_link" remove="true" />
        <referenceBlock name="payone_amazon_scripts" remove="true" />
        <referenceBlock name="catalog.compare.sidebar" remove="true" />
        <referenceBlock name="sale.reorder.sidebar" remove="true" />
        <referenceBlock name="recaptcha.customer_login" remove="true" />
        <referenceBlock name="recaptcha.newsletter" remove="true" />
        <referenceBlock name="wishlist_sidebar" remove="true" />
    </body>
</page>
