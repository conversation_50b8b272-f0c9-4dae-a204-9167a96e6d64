<script>
    window.cookieConsents = <?php echo $this->getJsonConfig() ?>;
    'use strict';

    (() => {
        const cookieConsentActions = () => {
            return {
                methods: ['append', 'prepend', 'after', 'before'],
                template: '<div class="my-2"><template x-for="consent in  consents">' +
                    '<div class="field required choice consent gdpr-js-consent">' +
                    '<input type="checkbox"' +
                    ' x-bind:name="consent.name"' +
                    ' required checked' +
                    ' value="1" ' +
                    ' x-bind:for="consent.input_id" ' +
                    'class="checkbox mt-1">' +
                    '<label x-bind:for="consent.input_id" class="label">' +
                    '<span x-html="consent.title"></span>' +
                    '</label>' +
                    '</div>' +
                    '</template>',
                templateWithoutCheckbox: '<template x-for="consent in  consents">' +
                    ' <span class="consent gdpr-js-consent" x-text="consent.title"></span>' +
                    '</template></div>',
                initHiddenConsents: function (form) {
                    form.classList.add('hidden-consents');

                    for (const element of form.querySelectorAll('input, select, textarea')) {
                        element.addEventListener("click", () => {
                            form.classList.add('visible-consents');
                            form.classList.remove('hidden-consents');
                        })
                    }
                },
                render: function (form, config) {
                    var el, consents;

                    form = document.querySelector(form);
                    if (!form) return;
                    form = form.closest('form');
                    config = Object.assign({
                        checkbox: true,
                        destination: 'fieldset .field:not(.captcha):not(.g-recaptcha):not(.field-recaptcha)',
                        method: 'after'
                    }, config);
                    if (config.destination.startsWith(">")) config.destination = config.destination.substr(1);
                    if (config.destination.endsWith(":last")) config.destination = config.destination.replace(":last", "");
                    const destination = form.querySelectorAll(config.destination);
                    if (!destination.length) {
                        return;
                    }

                    for (const consent of config.consents) {
                        consent.input_id = 'swissup_gdpr_' + consent.html_id + '_' + (Math.random() * (9999 - 1000) + 1000);
                        consent.name = 'swissup_gdpr_consent[' + consent.html_id + ']';
                    }

                    el = destination[destination.length - 1];

                    if (this.methods.indexOf(config.method) === -1) {
                        config.method = 'append';
                    }

                    consents = document.createElement('div');
                    consents.innerHTML = config.checkbox ? this.template : this.templateWithoutCheckbox;
                    consents.setAttribute("x-data", "{consents: " + JSON.stringify(config.consents) + "}");

                    if (el.style.maxWidth) {
                        consents.style.maxWidth = el.style.maxWidth;
                    }
                    switch (config.method) {
                        case 'append':
                        case 'after':
                            el.appendChild(consents);
                            break;
                        case 'prepend':
                        case 'before':
                            el.insertBefore(consents, el.firstChild);
                            break;
                    }

                    if (config.checkbox &&
                        config.consents.length &&
                        config.consents[0].forms.indexOf('magento:newsletter-subscription') !== -1) {
                        this.initHiddenConsents(form);
                    }
                },
                init: function () {
                    for (const consent of Object.values(window.cookieConsents)) {
                        const selector = consent.form;
                        this.render(selector, consent);
                    }
                }
            }
        }
        cookieConsentActions().init();
    })();
</script>
