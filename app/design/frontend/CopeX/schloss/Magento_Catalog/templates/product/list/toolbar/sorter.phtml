<?php

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Catalog\Block\Product\ProductList\Toolbar;
use Magento\Framework\Escaper;

/** @var Toolbar $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);
?>
<div class="toolbar-sorter flex md:w-1/2 order-2 justify-self-end" style="width: max-content;">
    <span class="sr-only sorter-label">
        <?= $escaper->escapeHtml(__('Sort By')) ?>
    </span>
    <select data-role="sorter"
            class="text-sm form-select sorter-options border-0 border-l shadow-none font-semibold"
            style="padding-right:25px;padding-left:6px;"
            aria-label="<?= $escaper->escapeHtml(__('Sort By')) ?>"
            @change="changeUrl(
                'product_list_order',
                $event.currentTarget.options[$event.currentTarget.selectedIndex].value,
                options.orderDefault
            )">
        <?php foreach ($block->getAvailableOrders() as $orderCode => $orderLabel):?>
            <option value="<?= $escaper->escapeHtmlAttr($orderCode) ?>"
                <?php if ($block->isOrderCurrent($orderCode)):?>
                    selected="selected"
                <?php endif; ?>
            >
                <?= $escaper->escapeHtml(__($orderLabel)) ?>
            </option>
        <?php endforeach; ?>
    </select>
    <?php if ($block->getCurrentDirection() == 'desc'): ?>
        <a title="<?= $escaper->escapeHtmlAttr(__('Set Ascending Direction')) ?>"
           href="#"
           class="action sorter-action sort-desc mt-3"
           @click.prevent="changeUrl('product_list_dir', 'asc', options.directionDefault)"
        >
            <?= $heroicons->sortAscendingHtml(); ?>
        </a>
    <?php else: ?>
        <a title="<?= $escaper->escapeHtmlAttr(__('Set Descending Direction')) ?>"
           href="#"
           class="action sorter-action sort-asc mt-3"
           @click.prevent="changeUrl('product_list_dir', 'desc', options.directionDefault)"
        >
            <?= $heroicons->sortDescendingHtml(); ?>
        </a>
    <?php endif; ?>
</div>
