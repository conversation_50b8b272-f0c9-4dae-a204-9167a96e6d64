<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types = 1);

use CopeX\HyvaTheme\ViewModel\Cms;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\ProductAttributes;
use Hyva\Theme\ViewModel\ProductPage;
use Magento\Catalog\Model\Product;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use CopeX\HyvaTheme\ViewModel\ShippingInfo;
use Magento\Tax\Model\Config;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var ProductPage $productViewModel */
$productViewModel = $viewModels->require(ProductPage::class);
/** @var ProductAttributes $attributesViewModel */
$attributesViewModel = $viewModels->require(ProductAttributes::class);
/** @var ShippingInfo $shippingInfoViewModel */
$shippingInfoViewModel = $viewModels->require(ShippingInfo::class);
/** @var Product $product */
$product = $productViewModel->getProduct();

/** @var Cms $cmsViewModel */
$cmsViewModel = $viewModels->require(Cms::class);
$taxRate = $shippingInfoViewModel->getTaxRate($product->getTaxClassId());
$shippingCostUrl = $shippingInfoViewModel->getShippingCostUrl();

?>
<div class="order-2 w-full md:px-5 md:w-1/2 lg:w-1/3 relative">
    <div class="-ml-4"><?= $block->getChildHtml('breadcrumbs'); ?></div>
    <h1 class="text-xl mt-0 mb-3 md:mt-6 md:text-2xl leading-10 text-left" itemprop="name">
        <?= $escaper->escapeHtml($product->getName()) ?>
    </h1>
    <?php if($reviewHtml = $block->getChildHtml('product.info.review')): ?>
        <div class="my-2 cursor-pointer"
             onclick="(
              document.getElementById('customer-review-list') ||
              document.getElementById('customer-reviews') || document.getElementById('customer-review-form')).scrollIntoView({behavior: 'smooth'}
              )">
            <?= $reviewHtml ?>
        </div>
    <?php endif; ?>

    <?php if ($shortDescription = $productViewModel->getShortDescription(false, false)): ?>
        <div class="mb-4 leading-relaxed product-description border-t mt-2">
            <span class="inline-block prose"><?= /* @noEscape */
                $cmsViewModel->renderBlockContent($shortDescription) ?></span>
        </div>
    <?php endif; ?>

    <?php if ($shortDescription = $product->getDescription()) { ?>
        <div class="mb-4 leading-relaxed product-description border-t pt-8 mt-8">
            <span class="inline-block prose text-black">
                <?= /* @noEscape */ $cmsViewModel->renderBlockContent($shortDescription) ?>
            </span>
        </div>
    <?php } ?>

    <?= $block->getChildHtml("product.info.datasheet") ?>

    <div class="flex flex-col sm:flex-row justify-between md:my-4">
        <?= $block->getChildHtml("alert.urls") ?>
    </div>

    <?= $block->getChildHtml("product.info.additional") ?>
</div>
<div class="order-3 w-full md:w-1/2 lg:w-1/3 relative md:border-l mb-6 md:pl-5 md:mb-0">
    <div class="">
        <div>
            <?= $block->getChildHtml("product.info.price") ?>
        </div>
        <div class=""><?= $block->getChildHtml("product.info.stockstatus") ?></div>
        <?= $block->getChildHtml('product.info.form') ?>

        <div class="flex flex-row flex-wrap md:justify-end justify-start py-1 md:py-4 pr-6 md:pr-1 bg-white fixed left-0 bottom-0 w-screen z-10 md:bg-transparent md:relative md:w-auto">

            <div class="flex w-full my-1 items-center">
                <?php if ($product->isSaleable()): ?>
                    <?= $block->getChildHtml("product.info.quantity") ?>
                    <?= $block->getChildHtml("product.info.addtocart") ?>
                <?php endif; ?>
            </div>

        </div>

        <?php if ($product->isSaleable()): ?>
            <?php $buttons = $block->getChildHtml('addtocart.shortcut.buttons'); ?>
            <?php if ($buttons) : ?>
                <div class="flex mt-4 justify-end">
                    <?= $buttons ?>
                </div>
            <?php endif; ?>
        <?php endif; ?>
        <?php if ($tierPriceBlock = $block->getChildHtml("product.price.tier")): ?>
            <div class="py-4 my-2 tier-price-container">
                <?= /** @noEscape */
                $tierPriceBlock ?>
            </div>
        <?php endif; ?>
        <div class="relative flex items-center">
            <div class="grow border-t"></div>
            <span class="relative flex p-4 items-center text-primary-lighter"><?= __('or'); ?></span>
            <div class="grow border-t "></div>
        </div>
        <div class="flex justify-center gap-x-6">
            <?= $block->getChildHtml('product.info.additional.actions'); ?>
            <?= $block->getChildHtml('product.info.addtowishlist'); ?>
            <?= $block->getChildHtml('product.info.addtocompare'); ?>
            <?= $block->getChildHtml('product.info.emailtofriend'); ?>
        </div>
        <?= $block->getChildHtml('seolink'); ?>
        <?= $productViewModel->productAttributeHtml($product, $product->getBelowAddToCart(), 'below_add_to_cart') ?>
    </div>
</div>
