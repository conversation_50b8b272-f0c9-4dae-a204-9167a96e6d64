<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

// phpcs:disable Magento2.Templates.ThisInTemplate

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Cart\ItemOutput;
use Hyva\Theme\ViewModel\HeroiconsSolid;
use Hyva\Theme\ViewModel\Modal;
use Magento\Checkout\Block\Cart\Grid;
use Magento\Checkout\ViewModel\Cart as CartViewModel;
use Magento\Framework\Escaper;

/** @var Grid $block */
/** @var ViewModelRegistry $viewModels */
/** @var Escaper $escaper */

/** @var CartViewModel $cartViewModel */
$cartViewModel = $viewModels->require(CartViewModel::class);

/** @var Modal $modelViewModel */
$modalViewModel = $viewModels->require(Modal::class);

/** @var HeroiconsSolid $heroicons */
$heroicons = $viewModels->require(HeroiconsSolid::class);

/** @var ItemOutput $cartItemOutputViewModel */
$cartItemOutputViewModel = $viewModels->require(ItemOutput::class);
?>
<?php $mergedCells = ($cartItemOutputViewModel->isItemPriceDisplayBoth() ? 2 : 1); ?>
<?= $block->getChildHtml('form_before') ?>
<form action="<?= $escaper->escapeUrl($block->getUrl('checkout/cart/updatePost')) ?>"
      x-data="{updating: false}"
      @cart-qty-updated.window="updating=true"
      @submit.prevent="hyva.postCart($event.target)"
      method="post"
      id="form-validate"
      class="form form-cart w-full float-left text-base"
>
    <?= $block->getBlockHtml('formkey') ?>
    <div class="cart bg-white shadow px-2 table-wrapper<?= $mergedCells == 2 ? ' detailed' : '' ?>">
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-top toolbar">
                <?= $block->getPagerHtml() ?>
            </div>
        <?php endif ?>
        <table id="shopping-cart-table"
               class="cart items data table w-full table-row-items"
        >
            <caption class="table-caption sr-only">
                <?= $escaper->escapeHtml(__('Shopping Cart Items')) ?>
            </caption>
            <thead class="hidden lg:table-header-group">
            <tr class="text-right">
                <th class="col item text-left pt-4 pb-2" scope="col">
                    <?= $escaper->escapeHtml(__('Item')) ?>
                </th>
                <th class="col qty pt-4 px-4 pb-2" scope="col">
                    <?= $escaper->escapeHtml(__('Qty')) ?>
                </th>
                <th class="col price pt-4 px-4 pb-2 hidden md:block" scope="col">
                    <?= $escaper->escapeHtml(__('Unit Price')) ?>
                </th>
                <th class="col subtotal pt-4 px-4 pb-2" scope="col">
                    <?= $escaper->escapeHtml(__('Sum')) ?>
                </th>
            </tr>
            </thead>
            <?php foreach ($block->getItems() as $item): ?>
                <?= $block->getItemHtml($item) ?>
            <?php endforeach ?>
        </table>
        <?php if ($block->getPagerHtml()): ?>
            <div class="cart-products-toolbar cart-products-toolbar-bottom toolbar">
                <?= $block->getPagerHtml() ?>
            </div>
        <?php endif ?>
    </div>
    <div class="cart actions flex flex-col sm:flex-row justify-end gap-4 items-center my-4">
        <?php if ($cartViewModel->isClearShoppingCartEnabled()): ?>
            <script>
                function initClearShoppingCartModal() {
                    return Object.assign(
                        hyva.modal(),
                        {
                            postData: {
                                action: '<?= $escaper->escapeUrl($block->getUrl('checkout/cart/updatePost')) ?>',
                                data: {update_cart_action: 'empty_cart'}
                            }
                        }
                    );
                }
            </script>
            <div x-data="initClearShoppingCartModal()">
                <?= ($confirmation = $modalViewModel
                    ->confirm(__('Are you sure?'))
                    ->withDetails(__('Are you sure you want to remove all items from your shopping cart?'))
                ) ?>
                <button @click="<?= $confirmation->getShowJs() ?>.then(result => result && hyva.postForm(postData))"
                        type="button" title="<?= $escaper->escapeHtmlAttr(__('Clear Shopping Cart')) ?>"
                        class="action clear" id="empty_cart_button">
                    <span><?= $escaper->escapeHtml(__('Clear Shopping Cart')) ?></span>
                </button>
            </div>

        <?php endif ?>
        <button type="submit"
                name="update_cart_action"
                data-cart-item-update=""
                value="update_qty"
                title="<?= $escaper->escapeHtmlAttr(__('Update Shopping Cart')) ?>"
                class="action update text-primary-lighter flex gap-2 text-sm"
                x-cloak
                :class="{'hidden': !updating}"
        >
            <?= $heroicons->refreshHtml("h-5 w-5 animate-spin-reverse"); ?>
            <?= $escaper->escapeHtml(__('Updating Shopping Cart')) ?>
        </button>
    </div>
</form>
<?= $block->getChildHtml('checkout.cart.order.actions') ?>
<?= $block->getChildHtml('shopping.cart.table.after') ?>
