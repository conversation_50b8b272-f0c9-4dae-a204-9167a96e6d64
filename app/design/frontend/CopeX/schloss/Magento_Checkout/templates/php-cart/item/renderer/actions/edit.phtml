<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\Theme\Model\ViewModelRegistry;
use Jajuma\AwesomeHyva\ViewModel\Awesomeicons6Solid;
use Magento\Checkout\Block\Cart\Item\Renderer\Actions\Edit;
use Magento\Framework\Escaper;

/** @var Edit $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var Awesomeicons6Solid $awesomeIcons */
$awesomeIcons = $viewModels->require(Awesomeicons6Solid::class);
?>
<?php if ($block->isProductVisibleInSiteVisibility()): ?>
    <a class="action action-edit hover:text-secondary"
       href="<?= $escaper->escapeUrl($block->getConfigureUrl()) ?>"
       title="<?= $escaper->escapeHtmlAttr(__('Edit item parameters')) ?>"
    >
        <?= $awesomeIcons->pencilHtml('h-4 w-4 my-1') ?>
        <span class="sr-only"><?= $escaper->escapeHtml(__('Edit')) ?></span>
    </a>
<?php endif ?>
