<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use CopeX\HyvaTheme\ViewModel\Cms;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\HeroiconsOutline;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;


/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var HeroiconsOutline $heroicons */
$heroicons = $viewModels->require(HeroiconsOutline::class);

$cmsHelper = $viewModels->require(Cms::class);

/** @var Hyva\Theme\ViewModel\StoreConfig $storeConfig */
$storeConfig = $viewModels->require(Hyva\Theme\ViewModel\StoreConfig::class);
$showMiniCart = $storeConfig->getStoreConfig(\Magento\Checkout\Block\Cart\Sidebar::XML_PATH_CHECKOUT_SIDEBAR_DISPLAY);

?>
<div id="header"
     class="relative bg-transparent border-b">
    <?php $topContent = $cmsHelper->renderBlockByIdentifier('header_top'); ?>
    <div class="hidden md:block p-2 bg-primary">
        <div class="container flex">
            <div class="text-white uppercase w-full text-right mx-auto">
                <?= $topContent ?>
            </div>
        </div>
    </div>
    <div class="bg-primary hidden lg:block">
        <?= $cmsHelper->renderBlockByIdentifier('header_top_banner'); ?>
    </div>
    <div>
        <div class="flex flex-wrap items-center justify-between columns mx-auto mt-0 items-center">
            <!--Logo-->
            <?= $block->getChildHtml('logo'); ?>
            <div class="order-2"><?=$cmsHelper->renderBlockByIdentifier('cart_header_usps'); ?></div>
        </div>
    </div>
    <!--Authentication Pop-Up-->
    <?= $block->getChildHtml('authentication-popup'); ?>
</div>

