<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="hyva_modal"/>
    <body>
        <referenceBlock name="elasticsuite_footer" remove="true"/>
        <referenceBlock name="report.bugs" remove="true"/>
        <referenceBlock name="top.links" remove="true" />
        <referenceBlock name="footer_links" remove="true"/>
        <referenceBlock name="footer-content" remove="true"/>
        <referenceBlock name="store.links" remove="true"/>
        <referenceBlock name="store.settings" remove="true"/>
        <referenceBlock name="store-language-switcher" remove="true"/>
        <referenceBlock name="form.subscribe" remove="true"/>
        <referenceBlock name="smile.tracker.config" remove="true"/>
        <referenceContainer name="after.body.start">
            <block class="Magento\Framework\View\Element\Template" template="Magento_Theme::html/icons.phtml" name="icon_paths" />
        </referenceContainer>
        <referenceContainer name="footer">
            <block class="Magento\Framework\View\Element\Template" name="footer.content" template="Magento_Theme::html/footer.phtml" >
                <block class="Magento\Framework\View\Element\Template" name="footer.logo" template="Magento_Theme::html/footer/logo.phtml">
                </block>
            </block>
        </referenceContainer>
        <referenceContainer name="footer-container">
            <container name="footer-bottom" htmlTag="div" htmlClass="footer bottom">
                <container name="footer-bottom-container" htmlTag="div" htmlClass="container">
                    <block class="Magento\Theme\Block\Html\Footer" name="copyright" template="Magento_Theme::html/copyright.phtml"/>
                </container>
            </container>
        </referenceContainer>
        <referenceContainer name="before.body.end">
            <block name="go.to.top" template="Magento_Theme::gototop.phtml" />
        </referenceContainer>
        <referenceBlock name="footer.content">
            <block class="Magento\Newsletter\Block\Subscribe" name="subscribe.new.form" template="Magento_Newsletter::subscribe.phtml" />
        </referenceBlock>
    </body>
</page>
