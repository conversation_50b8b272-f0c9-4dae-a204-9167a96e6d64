<?php
/**
 * Hyvä Themes - https://hyva.io
 * Copyright © Hyvä Themes 2020-present. All rights reserved.
 * This product is licensed per Magento install
 * See https://hyva.io/license
 */

declare(strict_types=1);

use Hyva\PaymentIcons\ViewModel\PaymentIconsLight;
use Hyva\Theme\Model\ViewModelRegistry;
use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Magento\Cms\Block\Block;

/** @var Escaper $escaper */
/** @var Template $block */
/** @var ViewModelRegistry $viewModels */

/** @var PaymentIconsLight $paymentIcons */
$paymentIcons = $viewModels->require(PaymentIconsLight::class);

$paymentIconsWidth = 48;
$paymentIconsHeight = 32;
?>

<div class="container block lg:flex w-full justify-between">
    <div class="flex gap-4 flex-wrap" aria-label="<?= $escaper->escapeHtml(__('Payment Providers')) ?>">
        <?= $paymentIcons->mastercardHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->visaHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->paypalHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->sofortHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
        <?= $paymentIcons->prepaymentHtml('', $paymentIconsWidth, $paymentIconsHeight) ?>
    </div>
    <div class="flex gap-4 flex-wrap">
        <?php echo $this->getLayout()->createBlock(Block::class)->setBlockId('footer_barlinks')->toHtml(); ?>
    </div>
</div>
