<?php
return [
    'modules' => [
        'Magento_Csp' => 1,
        'Magento_Store' => 1,
        'Magento_Directory' => 1,
        'Magento_Config' => 1,
        'Magento_AsyncConfig' => 1,
        'Magento_Theme' => 1,
        'Magento_Backend' => 1,
        'Magento_Variable' => 1,
        'Magento_Eav' => 1,
        'Magento_Authorization' => 1,
        'Magento_Backup' => 1,
        'Magento_Customer' => 1,
        'Magento_AdminNotification' => 0,
        'Magento_CacheInvalidate' => 0,
        'Magento_Indexer' => 1,
        'Magento_Rule' => 1,
        'Magento_GraphQl' => 1,
        'Magento_EavGraphQl' => 1,
        'Magento_Search' => 1,
        'Magento_CatalogImportExport' => 1,
        'Magento_Cms' => 1,
        'Magento_Catalog' => 1,
        'Magento_CatalogRule' => 1,
        'Magento_Payment' => 1,
        'Magento_CatalogRuleGraphQl' => 1,
        'Magento_CatalogInventory' => 1,
        'Magento_CatalogUrlRewrite' => 1,
        'Magento_GraphQlResolverCache' => 1,
        'Magento_MediaStorage' => 1,
        'Magento_Quote' => 1,
        'Magento_SalesSequence' => 1,
        'Magento_CheckoutAgreementsGraphQl' => 1,
        'Magento_Robots' => 1,
        'Magento_CmsGraphQl' => 1,
        'Magento_CmsUrlRewrite' => 1,
        'Magento_CmsUrlRewriteGraphQl' => 1,
        'Magento_StoreGraphQl' => 1,
        'Magento_Security' => 1,
        'Magento_Msrp' => 1,
        'Magento_Sales' => 1,
        'Magento_Checkout' => 1,
        'Magento_Contact' => 1,
        'Magento_ContactGraphQl' => 1,
        'Magento_Cookie' => 1,
        'Magento_Cron' => 1,
        'Magento_AdvancedSearch' => 1,
        'Magento_Widget' => 1,
        'Magento_Bundle' => 1,
        'Magento_Downloadable' => 1,
        'Magento_Newsletter' => 1,
        'Magento_DataExporter' => 1,
        'Magento_Deploy' => 1,
        'Magento_Developer' => 1,
        'Magento_User' => 1,
        'Magento_DirectoryGraphQl' => 1,
        'Magento_CatalogGraphQl' => 1,
        'Magento_QuoteGraphQl' => 1,
        'Magento_CatalogCustomerGraphQl' => 1,
        'Magento_BundleGraphQl' => 1,
        'Magento_CatalogSearch' => 1,
        'Magento_Elasticsearch' => 1,
        'Magento_Email' => 1,
        'Magento_EncryptionKey' => 1,
        'Magento_GiftMessage' => 1,
        'Magento_GiftMessageGraphQl' => 1,
        'Magento_GoogleAnalytics' => 1,
        'Magento_GoogleGtag' => 1,
        'Magento_GraphQlServer' => 1,
        'Magento_PageCache' => 1,
        'Magento_GraphQlNewRelic' => 1,
        'Magento_CatalogCmsGraphQl' => 1,
        'Magento_AdminGraphQlServer' => 1,
        'Magento_GroupedProduct' => 1,
        'Magento_GroupedCatalogInventory' => 1,
        'Magento_GroupedProductGraphQl' => 1,
        'Magento_ImportExport' => 1,
        'Magento_ConfigurableProduct' => 1,
        'Magento_InstantPurchase' => 1,
        'Magento_Integration' => 1,
        'Magento_IntegrationGraphQl' => 1,
        'Magento_Inventory' => 0,
        'Magento_InventoryAdminUi' => 0,
        'Magento_InventoryAdvancedCheckout' => 0,
        'Magento_InventoryApi' => 0,
        'Magento_InventoryBundleImportExport' => 0,
        'Magento_InventoryBundleProduct' => 0,
        'Magento_InventoryBundleProductAdminUi' => 0,
        'Magento_InventoryBundleProductIndexer' => 0,
        'Magento_InventoryCatalog' => 0,
        'Magento_InventorySales' => 0,
        'Magento_InventoryCatalogAdminUi' => 0,
        'Magento_InventoryCatalogApi' => 0,
        'Magento_InventoryCatalogFrontendUi' => 0,
        'Magento_InventoryCatalogRule' => 1,
        'Magento_InventoryCatalogSearch' => 0,
        'Magento_InventoryCatalogSearchBundleProduct' => 1,
        'Magento_InventoryCatalogSearchConfigurableProduct' => 1,
        'Magento_ConfigurableProductGraphQl' => 1,
        'Magento_InventoryConfigurableProduct' => 0,
        'Magento_InventoryConfigurableProductFrontendUi' => 0,
        'Magento_InventoryConfigurableProductIndexer' => 0,
        'Magento_InventoryConfiguration' => 0,
        'Magento_InventoryConfigurationApi' => 0,
        'Magento_InventoryDistanceBasedSourceSelection' => 0,
        'Magento_InventoryDistanceBasedSourceSelectionAdminUi' => 0,
        'Magento_InventoryDistanceBasedSourceSelectionApi' => 0,
        'Magento_InventoryElasticsearch' => 0,
        'Magento_InventoryExportStockApi' => 0,
        'Magento_InventoryIndexer' => 0,
        'Magento_InventorySalesApi' => 0,
        'Magento_InventoryGroupedProduct' => 0,
        'Magento_InventoryGroupedProductAdminUi' => 0,
        'Magento_InventoryGroupedProductIndexer' => 0,
        'Magento_InventoryImportExport' => 0,
        'Magento_InventoryInStorePickupApi' => 0,
        'Magento_Ui' => 1,
        'Magento_InventorySourceSelectionApi' => 0,
        'Magento_InventoryInStorePickup' => 0,
        'Magento_InventoryInStorePickupGraphQl' => 0,
        'Magento_Shipping' => 1,
        'Magento_InventoryInStorePickupShippingApi' => 0,
        'Magento_InventoryInStorePickupQuoteGraphQl' => 0,
        'Magento_InventoryInStorePickupSales' => 0,
        'Magento_InventoryInStorePickupSalesApi' => 0,
        'Magento_InventoryInStorePickupQuote' => 0,
        'Magento_InventoryInStorePickupShipping' => 0,
        'Magento_InventoryInStorePickupShippingAdminUi' => 0,
        'Magento_InventoryInStorePickupMultishipping' => 0,
        'Magento_Webapi' => 1,
        'Magento_InventoryCache' => 0,
        'Magento_InventoryLowQuantityNotification' => 0,
        'Magento_Reports' => 1,
        'Magento_InventoryLowQuantityNotificationApi' => 0,
        'Magento_InventoryMultiDimensionalIndexerApi' => 0,
        'Magento_InventoryProductAlert' => 0,
        'Magento_InventoryQuoteGraphQl' => 1,
        'Magento_InventoryRequisitionList' => 0,
        'Magento_InventoryReservations' => 0,
        'Magento_InventoryReservationCli' => 0,
        'Magento_InventoryReservationsApi' => 0,
        'Magento_InventoryExportStock' => 0,
        'Magento_InventorySalesAdminUi' => 0,
        'Magento_CatalogInventoryGraphQl' => 0,
        'Magento_InventorySalesAsyncOrder' => 1,
        'Magento_InventorySalesFrontendUi' => 0,
        'Magento_InventorySetupFixtureGenerator' => 0,
        'Magento_InventoryShipping' => 0,
        'Magento_InventoryShippingAdminUi' => 0,
        'Magento_InventorySourceDeductionApi' => 0,
        'Magento_InventorySourceSelection' => 0,
        'Magento_InventoryInStorePickupFrontend' => 0,
        'Magento_InventorySwatchesFrontendUi' => 0,
        'Magento_InventoryVisualMerchandiser' => 0,
        'Magento_InventoryWishlist' => 0,
        'Magento_JwtFrameworkAdapter' => 1,
        'Magento_JwtUserToken' => 1,
        'Magento_LayeredNavigation' => 1,
        'Magento_LoginAsCustomer' => 1,
        'Magento_LoginAsCustomerAdminUi' => 1,
        'Magento_LoginAsCustomerApi' => 1,
        'Magento_LoginAsCustomerAssistance' => 1,
        'Magento_LoginAsCustomerFrontendUi' => 1,
        'Magento_LoginAsCustomerGraphQl' => 1,
        'Magento_LoginAsCustomerLog' => 1,
        'Magento_LoginAsCustomerPageCache' => 1,
        'Magento_LoginAsCustomerQuote' => 1,
        'Magento_LoginAsCustomerSales' => 1,
        'Magento_MediaContent' => 1,
        'Magento_MediaContentApi' => 1,
        'Magento_MediaContentCatalog' => 1,
        'Magento_MediaContentCms' => 1,
        'Magento_MediaContentSynchronization' => 1,
        'Magento_MediaContentSynchronizationApi' => 1,
        'Magento_MediaContentSynchronizationCatalog' => 1,
        'Magento_MediaContentSynchronizationCms' => 1,
        'Magento_MediaGallery' => 1,
        'Magento_MediaGalleryApi' => 1,
        'Magento_MediaGalleryCatalog' => 1,
        'Magento_MediaGalleryCatalogIntegration' => 1,
        'Magento_MediaGalleryCatalogUi' => 1,
        'Magento_MediaGalleryCmsUi' => 1,
        'Magento_MediaGalleryIntegration' => 1,
        'Magento_MediaGalleryMetadata' => 1,
        'Magento_MediaGalleryMetadataApi' => 1,
        'Magento_MediaGalleryRenditions' => 1,
        'Magento_MediaGalleryRenditionsApi' => 1,
        'Magento_MediaGallerySynchronization' => 1,
        'Magento_MediaGallerySynchronizationApi' => 1,
        'Magento_MediaGallerySynchronizationMetadata' => 1,
        'Magento_MediaGalleryUi' => 1,
        'Magento_MediaGalleryUiApi' => 1,
        'Magento_CatalogWidget' => 1,
        'Magento_MessageQueue' => 1,
        'Magento_InventoryConfigurableProductAdminUi' => 0,
        'Magento_MsrpConfigurableProduct' => 1,
        'Magento_MsrpGroupedProduct' => 1,
        'Magento_MysqlMq' => 1,
        'Magento_NewRelicReporting' => 1,
        'Magento_CustomerGraphQl' => 1,
        'Magento_NewsletterGraphQl' => 1,
        'Magento_OfflinePayments' => 1,
        'Magento_SalesRule' => 1,
        'Magento_OpenSearch' => 1,
        'Magento_OrderCancellation' => 1,
        'Magento_OrderCancellationGraphQl' => 1,
        'Magento_OrderCancellationUi' => 1,
        'Magento_Sitemap' => 1,
        'Magento_PageBuilder' => 1,
        'Magento_GraphQlCache' => 1,
        'Magento_Captcha' => 1,
        'Magento_PaymentGraphQl' => 1,
        'Magento_ServiceProxy' => 1,
        'Magento_Vault' => 1,
        'Magento_PaymentServicesDashboard' => 1,
        'Magento_PaymentServicesPaypalGraphQl' => 1,
        'Magento_QueryXml' => 1,
        'Magento_Paypal' => 1,
        'Magento_PaypalGraphQl' => 1,
        'Magento_ProductAlert' => 1,
        'Magento_ProductVideo' => 1,
        'Magento_ServicesConnector' => 1,
        'Magento_CheckoutAgreements' => 1,
        'Magento_QuoteBundleOptions' => 1,
        'Magento_QuoteConfigurableOptions' => 1,
        'Magento_QuoteDownloadableLinks' => 1,
        'Magento_DownloadableGraphQl' => 1,
        'Magento_ReCaptchaAdminUi' => 1,
        'Magento_ReCaptchaCheckout' => 1,
        'Magento_ReCaptchaCheckoutSalesRule' => 1,
        'Magento_ReCaptchaContact' => 1,
        'Magento_ReCaptchaCustomer' => 1,
        'Magento_ReCaptchaFrontendUi' => 1,
        'Magento_ReCaptchaMigration' => 1,
        'Magento_ReCaptchaNewsletter' => 1,
        'Magento_ReCaptchaPaypal' => 1,
        'Magento_ReCaptchaResendConfirmationEmail' => 1,
        'Magento_ReCaptchaReview' => 1,
        'Magento_ReCaptchaStorePickup' => 1,
        'Magento_ReCaptchaUi' => 1,
        'Magento_ReCaptchaUser' => 1,
        'Magento_ReCaptchaValidation' => 1,
        'Magento_ReCaptchaValidationApi' => 1,
        'Magento_ReCaptchaVersion2Checkbox' => 1,
        'Magento_ReCaptchaVersion2Invisible' => 1,
        'Magento_ReCaptchaVersion3Invisible' => 1,
        'Magento_ReCaptchaWebapiApi' => 1,
        'Magento_ReCaptchaWebapiGraphQl' => 1,
        'Magento_ReCaptchaWebapiRest' => 1,
        'Magento_ReCaptchaWebapiUi' => 1,
        'Magento_ReCaptchaWishlist' => 1,
        'Magento_RelatedProductGraphQl' => 1,
        'Magento_ReleaseNotification' => 1,
        'Magento_RemoteStorage' => 1,
        'Magento_InventoryLowQuantityNotificationAdminUi' => 0,
        'Magento_RequireJs' => 1,
        'Magento_Review' => 1,
        'Magento_ReviewGraphQl' => 1,
        'Magento_AwsS3' => 1,
        'Magento_Rss' => 1,
        'Magento_PageBuilderImageAttribute' => 1,
        'Magento_ConfigurableProductSales' => 1,
        'Magento_ServicesId' => 1,
        'Magento_SalesGraphQl' => 1,
        'Magento_SalesInventory' => 1,
        'Magento_OfflineShipping' => 1,
        'Magento_SalesRuleGraphQl' => 1,
        'Magento_CatalogRuleConfigurable' => 1,
        'Magento_UrlRewriteGraphQl' => 1,
        'Magento_Analytics' => 1,
        'Magento_Securitytxt' => 1,
        'Magento_ServicesIdGraphQlServer' => 1,
        'Magento_ServicesIdLayout' => 1,
        'Magento_PaymentServicesBase' => 1,
        'Magento_SalesDataExporter' => 1,
        'Magento_PaymentServicesPaypal' => 1,
        'Magento_InventoryInStorePickupSalesAdminUi' => 0,
        'Magento_AwsS3PageBuilder' => 1,
        'Magento_InventoryGraphQl' => 0,
        'Magento_StoreDataExporter' => 1,
        'Magento_CompareListGraphQl' => 1,
        'Magento_Swatches' => 1,
        'Magento_SwatchesGraphQl' => 1,
        'Magento_SwatchesLayeredNavigation' => 1,
        'Magento_Tax' => 1,
        'Magento_TaxGraphQl' => 1,
        'Magento_CustomerDownloadableGraphQl' => 1,
        'Magento_ThemeGraphQl' => 1,
        'Magento_Translation' => 1,
        'Magento_InventoryInStorePickupAdminUi' => 0,
        'Magento_UrlRewrite' => 1,
        'Magento_CatalogUrlRewriteGraphQl' => 1,
        'Magento_AsynchronousOperations' => 1,
        'Magento_Elasticsearch8' => 1,
        'Magento_PaypalCaptcha' => 1,
        'Magento_VaultGraphQl' => 1,
        'Magento_InventoryInStorePickupWebapiExtension' => 0,
        'Magento_WebapiAsync' => 0,
        'Magento_WebapiSecurity' => 1,
        'Magento_Weee' => 1,
        'Magento_WeeeGraphQl' => 1,
        'Magento_CurrencySymbol' => 1,
        'Magento_Wishlist' => 1,
        'Magento_WishlistGraphQl' => 1,
        'AvS_ScopeHint' => 1,
        'CopeX_AppCatalog' => 1,
        'Ytec_Base' => 1,
        'CopeX_Cleanup' => 1,
        'CopeX_CookieNotification' => 1,
        'CopeX_CopyrightHtml' => 1,
        'CopeX_CustomerSavings' => 1,
        'CopeX_DbFix' => 1,
        'CopeX_KeycloakAuth' => 1,
        'Ytec_RestPdfInvoice' => 1,
        'CopeX_SerialNumber' => 1,
        'Dibs_EasyCheckout' => 1,
        'EthanYehuda_CronjobManager' => 1,
        'Experius_EmailCatcher' => 1,
        'Experius_WysiwygDownloads' => 1,
        'FireGento_MageSetup' => 1,
        'Hyva_Admin' => 1,
        'MSP_Common' => 1,
        'MSP_DevTools' => 1,
        'MagePal_Core' => 1,
        'MagePal_CustomerAccountLinksManager' => 1,
        'MagePal_PreviewCheckoutSuccessPage' => 1,
        'OlegKoval_RegenerateUrlRewrites' => 1,
        'Opengento_Gdpr' => 1,
        'Phoenix_MediaStorageSync' => 1,
        'Vivo_SerialNumber' => 1,
        'Yireo_CspUtilities' => 1,
        'Yireo_GoogleTagManager2' => 1,
        'Yireo_Whoops' => 1,
        'CopeX_AppPayment' => 1,
        'CopeX_PdfInvoice' => 1
    ]
];
