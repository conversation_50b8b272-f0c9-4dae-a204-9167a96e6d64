<?php
/**
 * Copyright © Magefan (<EMAIL>). All rights reserved.
 * Please visit Magefan.com for license details (https://magefan.com/end-user-license-agreement).
 */
/**
 * @var $block \Magefan\GoogleTagManager\Block\GtmCode
 * @var $mfSecureRenderer \Magefan\Community\Api\SecureHtmlRendererInterface
 * @var $mfHyvaThemeDetection \Magefan\Community\Api\HyvaThemeDetectionInterface
 */
?>
<?php
if (!isset($escaper)) {
    /* Compatability fix for old Magento versions */
    $escaper = $block;
}

$script = '';
?>
<!-- Google Tag Manager -->
<?php if ($block->isSpeedOptimizationEnabled() && $block->getRequest()->getModuleName() !== 'checkout') { ?>
    <?php $script .= "
        document.addEventListener('scroll', mfGtmUserActionDetect);
        document.addEventListener('mousemove', mfGtmUserActionDetect);
        document.addEventListener('touchstart', mfGtmUserActionDetect);
        function mfGtmUserActionDetect()
        {
            document.removeEventListener('scroll', mfGtmUserActionDetect);
            document.removeEventListener('mousemove', mfGtmUserActionDetect);
            document.removeEventListener('touchstart', mfGtmUserActionDetect);
            window.mfGtmUserActionDetected = true;
            mfLoadGtm();
        }
    "; ?>
<?php } else { ?>
    <?php $script .= "
        window.mfGtmUserActionDetected = true;
        "; ?>
<?php } ?>

<?php if ($block->isProtectCustomerDataEnabled()) { ?>
    <?php
    if (!$block->isMfCookieConsentExtensionEnabled()) {
        $script .= "
                window.dataLayer = window.dataLayer || [];
                function gtag() { dataLayer.push(arguments); }
                gtag('consent', 'default', {
                    'ad_user_data': 'denied',
                    'ad_personalization': 'denied',
                    'ad_storage': 'denied',
                    'analytics_storage': 'denied',
                    /* cookieyes.com start */
        
                    'functionality_storage': 'denied',
                    'personalization_storage': 'denied',
                    'security_storage': 'granted',
                    /* cookieyes.com end */
        
                    'wait_for_update': 2000
                });
                /* cookieyes.com start */
                gtag('set', 'ads_data_redaction', true);
                gtag('set', 'url_passthrough', true);
                /* cookieyes.com end */
            ";
    }
    ?>

    <?php if ($block->isLoadBeforeConsent()) { ?>
        <?php $script .= "
            window.mfGtmLoadBeforeCookieAllowed = true;
            mfLoadGtm();
            "; ?>
    <?php } ?>

    <?php if ($block->isCookieRestrictionModeEnabled()) { ?>
        <?php $script .= "
        (function () {
            function getCookieValue(cookieName) {
                let name = cookieName + '=';
                let cookieSplit = document.cookie.split(';');

                for (let i = 0; i < cookieSplit.length; i++) {
                    let a = cookieSplit[i];

                    while (a.charAt(0) === ' ') {
                        a = a.substring(1);
                    }

                    if (a.indexOf(name) === 0) {
                        return a.substring(name.length, a.length);
                    }
                }
                return '';
            };

            function customerDataAllowed() {
                if (getCookieValue('mf_cookie_consent')) {
                    return true;
                };
                let cookie = getCookieValue(
                    '{$escaper->escapeHtml(\Magento\Cookie\Helper\Cookie::IS_USER_ALLOWED_SAVE_COOKIE)}'
                );
                if (cookie) {
                    cookie = JSON.parse(decodeURIComponent(cookie));
                    if (cookie[{$escaper->escapeHtml($block->getWebsiteId())}]) {
                        return true;
                    }
                }

                return false;
            };

            function grantConsent()
            {
                window.mfGtmUserCookiesAllowed = true;
            ";

        if (!$block->isMfCookieConsentExtensionEnabled()) {
            $script .= "gtag('consent', 'update', {
                    'ad_user_data': 'granted',
                    'ad_personalization': 'granted',
                    'ad_storage': 'granted',
                    'analytics_storage': 'granted',
                    'functionality_storage': 'granted',
                    'personalization_storage': 'granted',
                    'security_storage': 'granted'
                });";
        }

        $script .= "
            };

            if (customerDataAllowed()) {
                grantConsent();
                mfLoadGtm();
            } else {
                let interval = setInterval(function () {
                    if (!customerDataAllowed()) return;
                    clearInterval(interval);
                    grantConsent();
                    mfLoadGtm();
                }, 1000);
            };
        })();
        "; ?>
    <?php } ?>

<?php } else { ?>
    <?php $script .= "
        window.mfGtmUserCookiesAllowed = true;
        mfLoadGtm();
        "; ?>
<?php } ?>
<?php $script .= "

    function getMfGtmCustomerIdentifier() {
        return localStorage.getItem('mf_gtm_customer_identifier') ? localStorage.getItem('mf_gtm_customer_identifier') : null;
    };

    function mfLoadGtm() {
        if (!window.mfGtmUserActionDetected) return false;
        if (!window.mfGtmLoadBeforeCookieAllowed && !window.mfGtmUserCookiesAllowed) return false;

        if (window.mfGTMTriedToLoad) return;
        window.mfGTMTriedToLoad = true;
        "; ?>
<?php if ($block->getConfig()->isWebContainerEnabled() && $block->getPublicId()) { ?>
    <?php if ('use_public_id' === $block->getConfig()->getInstallGtm()) { ?>
        <?php $script .= "
            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
                    new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
                j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
                'https:' + '/' + '/' + 'www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
            })(window,document,'script','dataLayer','{$escaper->escapeHtml($block->getPublicId())}');
        "; ?>
    <?php } elseif ('use_head_and_body_script' === $block->getConfig()->getInstallGtm()) { ?>
        <?php $script .= $block->getGtmScript(); ?>
    <?php } ?>
<?php } ?>
<?php $script .= "
    };
    "; ?>
<?= /* @noEscape */ $mfSecureRenderer->renderTag('script', [], $script, false) ?>

<?php if (!$mfHyvaThemeDetection->execute()) { ?>
    <?php $script = "
        if (!window.MagefanRocketJsCustomOptimization && window.require)
        require(['jquery', 'Magento_Customer/js/customer-data'], function ($, customer) {
            function updateMfGtmCustomerIdentifier(data) {
                if (data.mf_gtm_customer_identifier) {
                    localStorage.setItem('mf_gtm_customer_identifier', data.mf_gtm_customer_identifier);
                }
            }
            let customerData = customer.get('customer');

            customerData.subscribe(function (data) {
                updateMfGtmCustomerIdentifier(data);
            }.bind(this));
            updateMfGtmCustomerIdentifier(customerData());
        });
    "; ?>
    <?= /* @noEscape */ $mfSecureRenderer->renderTag('script', [], $script, false) ?>
<?php } else { ?>
    <?php $script = "
        /* Hyva code */
        function mfHyvaGtmSetCustomerIdentifier(event) {
            let data, j;
            let sections = event.detail.data;
            for (j in sections) {
                data = sections[j];
                if (data.mf_gtm_customer_identifier) {
                    localStorage.setItem('mf_gtm_customer_identifier', 'data.mf_gtm_customer_identifier');
                    return;
                }
            }
        };
        window.addEventListener('private-content-loaded', mfHyvaGtmSetCustomerIdentifier);
        /* End Hyva code */
    "; ?>
    <?= /* @noEscape */ $mfSecureRenderer->renderTag('script', [], $script, false) ?>
<?php } ?>
<!-- End Google Tag Manager -->
