<?php
/**
 * Copyright © Magefan (<EMAIL>). All rights reserved.
 * Please visit Magefan.com for license details (https://magefan.com/end-user-license-agreement).
 */
/** @var \Magefan\GoogleTagManager\Block\GtmCode $block */
/** @var \Magento\Framework\Escaper $escaper */
?>
<?php
if (!isset($escaper)) {
    /* Compatability fix for old Magento versions */
    $escaper = $block;
}
?>
<?php if ($block->getConfig()->isWebContainerEnabled() && $block->getPublicId() && (!$block->isProtectCustomerDataEnabled() || $block->isLoadBeforeConsent())) { ?>
    <!-- Google Tag Manager (noscript) -->
    <?php if ('use_public_id' === $block->getConfig()->getInstallGtm()) { ?>
        <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=<?= $escaper->escapeHtml($block->getPublicId()) ?>"
                          height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
        <!-- End Google Tag Manager (noscript) -->
    <?php } elseif ('use_head_and_body_script' === $block->getConfig()->getInstallGtm()) { ?>
        <?= $block->getGtmNoScript() ?>
    <?php } ?>
<?php } ?>
