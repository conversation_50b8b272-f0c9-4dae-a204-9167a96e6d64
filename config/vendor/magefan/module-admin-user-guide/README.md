# [Magento 2 Admin User Guide Extension](https://magefan.com/) by <PERSON><PERSON><PERSON>

[![Total Downloads](https://poser.pugx.org/magefan/module-admin-user-guide/downloads)](https://packagist.org/packages/magefan/module-admin-user-guide)
[![Latest Stable Version](https://poser.pugx.org/magefan/module-admin-user-guide/v/stable)](https://packagist.org/packages/magefan/module-admin-user-guide)

<a href="https://savelife.in.ua/en/donate-en/#donate-army-card-monthly"><img width="830" height="208" src="https://cm.magefan.com/blog/support-ukraine.png"></a>

<img width="150" height="100" src="https://magefan.com/media/wysiwyg/made_in_ukraine.jpg">

This Magento 2 Admin User Guide module adds guide links in admin panel to improve admin experience.

## Requirements
  * Magento Community Edition 2.1.x-2.4.x or Magento Enterprise Edition 2.1.x-2.4.x

## Installation Method 1 - Installing via composer
  * Open command line
  * Using command "cd" navigate to your magento2 root directory
  * Run command: composer require magefan/module-admin-user-guide

  

## Installation Method 2 - Installing using archive
  * Download [ZIP Archive](https://github.com/magefan/module-admin-user-guide/archive/refs/heads/main.zip)
  * Extract files
  * In your Magento 2 root directory create folder app/code/Magefan/AdminUserGuide
  * Copy files and folders from archive to that folder
  * In command line, using "cd", navigate to your Magento 2 root directory
  * Run commands:
```
php bin/magento setup:upgrade
php bin/magento setup:di:compile
php bin/magento setup:static-content:deploy
```

## Support
If you have any issues, please [contact us](mailto:<EMAIL>)
then if you still need help, open a bug report in GitHub's
[issue tracker](https://github.com/magefan/module-admin-user-guide/issues).

Please do not use Magento Marketplace Reviews or (especially) the Q&A for support.
There isn't a way for us to reply to reviews and the Q&A moderation is very slow.

## Need More Features?
Please contact us to get a quote
https://magefan.com/contact

## License
The code is licensed under [EULA](https://magefan.com/end-user-license-agreement).

## [Magento 2 Extensions](https://magefan.com/magento-2-extensions) by Magefan

### [Magento 2 Google Extensions](https://magefan.com/magento-2-extensions/google-extensions)

  * [Magento 2 Google Indexing API](https://magefan.com/magento-2-google-indexing-api)
  * [Magento 2 Google Analytics 4](https://magefan.com/magento-2-google-analytics-4)
  * [Magento 2 Google Tag Manager](https://magefan.com/magento-2-google-tag-manager)
  * [Magento 2 Google Shopping Feed](https://magefan.com/magento-2-google-shopping-feed-extension)
  * [Magento 2 Google Customer Reviews](https://magefan.com/magento-2-google-customer-reviews)

### [Magento 2 SEO Extensions](https://magefan.com/magento-2-extensions/magento-2-seo-extensions)

  * [Magento 2 SEO Extension](https://magefan.com/magento-2-seo-extension)
  * [Magento 2 Rich Snippets](https://magefan.com/magento-2-rich-snippets)
  * [Magento 2 HTML Sitemap](https://magefan.com/magento-2-html-sitemap-extension)
  * [Magento 2 XML Sitemap](https://magefan.com/magento-2-xml-sitemap-extension)
  * [Magento 2 Twitter Cards](https://magefan.com/magento-2-twitter-cards-extension)
  * [Magento 2 Facebook Open Graph Tags](https://magefan.com/magento-2-open-graph-extension-og-tags)

### [Magento 2 Speed Optimization Extensions](https://magefan.com/magento-2-extensions/speed-optimization)

  * [Magento 2 Google Page Speed Optimizer](https://magefan.com/magento-2-google-page-speed-optimizer)
  * [Magento 2 Full Page Cache Warmer](https://magefan.com/magento-2-full-page-cache-warmer)
  * [Magento 2 Image Lazy Load](https://magefan.com/magento-2-image-lazy-load-extension)
  * [Magento 2 WebP Images](https://magefan.com/magento-2-webp-optimized-images)
  * [Magento 2 Rocket JavaScript](https://magefan.com/rocket-javascript-deferred-javascript)

  ### [Magento 2 Admin Panel Extensions](https://magefan.com/magento-2-extensions/admin-extensions)

  * [Magento 2 Admin Action Log](https://magefan.com/magento-2-admin-action-log)
  * [Magento 2 Order Editor](https://magefan.com/magento-2-edit-order-extension)
  * [Magento 2 Better Order Grid](https://magefan.com/magento-2-better-order-grid-extension)
  * [Magento 2 Extended Product Grid](https://magefan.com/magento-2-product-grid-inline-editor)
  * [Magento 2 Product Tabs](https://magefan.com/magento-2/extensions/product-tabs)
  * [Magento 2 Facebook Pixel](https://magefan.com/magento-2-facebook-pixel-extension)
  * [Magento 2 Email Attachments](https://magefan.com/magento-2-email-attachments)
  * [Magento 2 Admin View](https://magefan.com/magento-2-admin-view-extension)
  * [Magento 2 Admin Email Notifications](https://magefan.com/magento-2-admin-email-notifications)
  * [Magento 2 Login As Customer](https://magefan.com/login-as-customer-magento-2-extension)

### [Magento 2 Blog Extensions](https://magefan.com/magento-2-extensions/blog-extensions)

  * [Magento 2 Blog](https://magefan.com/magento2-blog-extension)
  * [Magento 2 Multi Blog](https://magefan.com/magento-2-multi-blog-extension)
  * [Magento 2 Product Widget](https://magefan.com/magento-2-product-widget)

### [Magento 2 Marketing Automation Extensions](https://magefan.com/magento-2-extensions/magento-marketing-automation)

  * [Magento 2 Cookie Consent](https://magefan.com/magento-2-cookie-consent)
  * [Magento 2 Product Labels](https://magefan.com/magento-2-product-labels)
  * [Magento 2 Base Price](https://magefan.com/magento-2-base-price)
  * [Magento 2 Dynamic Categories](https://magefan.com/magento-2-dynamic-categories)
  * [Magento 2 Dynamic Blocks and Pages Extension](https://magefan.com/magento-2-cms-display-rules-extension)
  * [Magento 2 Automatic Related Products](https://magefan.com/magento-2-automatic-related-products)
  * [Magento 2 Price History](https://magefan.com/magento-2-price-history)
  * [Magento 2 Mautic Extension](https://magefan.com/magento-2-mautic-extension)
  * [Magento 2 YouTube Video Extension](https://magefan.com/magento2-youtube-extension)    
 
### [Magento 2 Cart Extensions](https://magefan.com/magento-2-extensions/cart-extensions)

  * [Better Magento 2 Checkout Extension](https://magefan.com/better-magento-2-checkout-extension)
  * [Magento 2 Coupon Code Link](https://magefan.com/magento-2-coupon-code-link)
  * [Magento 2 Guest to Customer](https://magefan.com/magento2-convert-guest-to-customer)

### [Magento 2 Multi-Language Extensions](https://magefan.com/magento-2-extensions/multi-language-extensions)

  * [Magento 2 Hreflang Tags](https://magefan.com/magento2-alternate-hreflang-extension)
  * [Magento 2 Auto Currency Switcher](https://magefan.com/magento-2-currency-switcher-auto-currency-by-country)
  * [Magento 2 Auto Language Switcher](https://magefan.com/magento-2-auto-language-switcher)
  * [Magento 2 GeoIP Store Switcher](https://magefan.com/magento-2-geoip-switcher-extension)
  * [Magento 2 Translation Extension](https://magefan.com/magento-2-translation-extension)

### [Developers Tools](https://magefan.com/magento-2-extensions/developer-tools)

  * [Magento 2 Zero Downtime Deployment](https://magefan.com/blog/magento-2-zero-downtime-deployment)
  * [Magento 2 Cron Schedule](https://magefan.com/magento-2-cron-schedule)
  * [Magento 2 CLI Extension](https://magefan.com/magento2-cli-extension)
  * [Magento 2 Conflict Detector](https://magefan.com/magento2-conflict-detector)
     
  ## [Shopify Apps](https://magefan.com/shopify/apps) by Magefan

  * [Shopify Login As Customer](https://apps.shopify.com/login-as-customer)
  * [Shopify Blog](https://apps.shopify.com/magefan-blog)
  * [Shopify Size Chart](https://magefan.com/shopify/apps/size-chart)
  * [Shopify Google Indexer](https://magefan.com/shopify/apps/google-indexing)
  * [Shopify Product Feeds](https://magefan.com/shopify/apps/product-feed)
