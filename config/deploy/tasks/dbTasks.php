<?php

namespace Deployer;

task('db:download', function () {
    $docker = get('docker');
    $n98command = "{{release_or_current_path}}/bin/n98-magerun2";
    if( test('[ -f {{release_or_current_path}}/vendor/bin/n98-magerun2 ]') ) {
        $n98command = "{{release_or_current_path}}/vendor/bin/n98-magerun2";
    }
    $localDirectory = "/tmp";
    if( test('[ -d /var/backups/{{application}}/database ]') ) {
        $localDirectory = "/var/backups/{{application}}/database";
    }
    else if (test('[ -d /var/backups/database ]')){
        $localDirectory = "/var/backups/database";
    }

    if ($docker) {
        run('docker exec -u $(id -u ${USER}):www-data {{application}}-app ' . $n98command . ' --root-dir={{release_or_current_path}} --skip-root-check db:dump --strip="@stripped @trade" -c gzip /backups/database/database_bkp.sql.gz');
        download( $localDirectory . "/database_bkp.sql.gz", "./db/import/database_bkp_" . date('Y.m.d-H:i:s') . ".sql.gz");
        run("rm " . $localDirectory . "/database_bkp.sql.gz");
    }
    else {
        run($n98command . ' --root-dir={{release_or_current_path}} db:dump --strip="@stripped @trade" -c gzip ' . $localDirectory . '/database_bkp.sql.gz');
        download($localDirectory . "/database_bkp.sql.gz", "./db/import/database_bkp_" . date('Y.m.d-H:i:s') . ".sql.gz");
        run("rm " . $localDirectory ."/database_bkp.sql.gz");
    }
})->desc('Connects to remote host, take a dump from the database via n98-magerun and download to local db/import folder');