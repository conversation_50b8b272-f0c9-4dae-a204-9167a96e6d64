<?php

namespace Deployer;

/**
 * Hosts
 */
host('production')
    // If you need to hop through a SSH-Config Proxy 
    ->setHostname(getenv('CI') === 'true' ? '*************' : 'altair')
    //->setHostname('{{INSERT_HOSTNAME_OR_IP_HERE}}')
    ->setPort('2232')
    ->setConfigFile('~/.ssh/config')
    ->setRemoteUser('copex')
    ->set('restart_command','docker restart {{application}}-app')
    ->set('hyva_theme',[])
    ->set('docker', true)
    ->setDeployPath('{{deployment_root_path}}/production')
    ->setForwardAgent(true)
    ->setLabels([
        'env'  => ENV_PRODUCTION,
    ])
    ->set('branch', 'master');
