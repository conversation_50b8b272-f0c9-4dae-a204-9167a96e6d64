{"name": "copex/pros-pro-m2", "license": ["OSL-3.0", "AFL-3.0"], "type": "project", "version": "2.4.7", "description": "Magento 2 Pros Pro project", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://copex.io/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://copex.io/"}], "repositories": {"xtento": {"type": "composer", "url": "https://repo.xtento.com"}, "mageplaza": {"type": "composer", "url": "https://repo.mageplaza.com"}, "private-packagist": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/pros-pro-com-l48xigo"}, "magento": {"type": "composer", "url": "https://repo.magento.com/", "canonical": false}, "devtools": {"type": "vcs", "url": "https://github.com/CopeX/m2-MSP_DevTools.git"}, "copex": {"type": "composer", "url": "https://packages.copex.io"}, "amasty": {"type": "composer", "url": "https://composer.amasty.com/community/"}, "fooman": {"type": "composer", "url": "https://customer-repos.fooman.co.nz/www.pros-pro.com-79c756b2f34d7a8a843e5a94caccf134b5884a92"}}, "require": {"magento/product-community-edition": "2.4.8-p2", "creativestyle/composer-plugin-patchset": "^3", "firegento/magesetup2": "^1.0", "splendidinternet/mage2-locale-de-de": "^1.16", "ethanyehuda/magento2-cronjobmanager": "^2", "avstudnitz/scopehint2": "^1", "yireo/magento2-googletagmanager2": "^3.3", "olegkoval/magento2-regenerate-url-rewrites": "^v1.4.1", "experius/module-wysiwygdownloads": "^1.2.0", "magepal/magento2-customeraccountlinksmanager": "^1.2", "experius/module-emailcatcher": "^4.0", "opengento/module-gdpr": "^4.4", "n98/magerun2-dist": "^8.1", "hyva-themes/hyva-ui": "^2.6", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-cms-tailwind-jit": "^1.1", "hyva-themes/magento2-hyva-widgets": "^1.0", "hyva-themes/magento2-luma-checkout": "^1.1", "hyva-themes/magento2-theme-fallback": "^1.0", "hyva-themes/magento2-payment-icons": "^2.0", "magento/data-migration-tool": "*", "mollie/magento2": "^2.45", "mollie/magento2-hyva-compatibility": "^2.4", "copex/module-prg": "^1.0", "amasty/payrestriction": "^2.5", "clever/magento2": "^3.5", "xtento/productexport": "^2.16", "fooman/surcharge-payment-m2": "^3.9", "hyva-themes/magento2-smile-elasticsuite": "^1.2", "smile/elasticsuite": "~2.11.13", "php-mqtt/client": "^2.2"}, "require-dev": {"yireo/magento2-whoops": "*", "mage2tv/magento-cache-clean": "*", "magepal/magento2-preview-checkout-success-page": "*", "lusitanian/oauth": "~0.8.10", "pdepend/pdepend": "2.*", "deployer/deployer": "^7.0", "msp/devtools": "^1.2", "magento/magento-coding-standard": "^31.0"}, "replace": {"adobe-commerce/adobe-ims-metapackage": "*", "magento/module-adobe-ims": "*", "magento/module-adobe-ims-api": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-configurable-import-export": "*", "magento/module-sampledata": "*", "shopialfb/facebook-module": "*", "temando/module-shipping-m2": "*", "braintree/braintree_php": "*", "braintree/braintree": "*", "paypal/module-braintree": "*", "dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "klarna/m2-payments": "*", "klarna/module-core": "*", "klarna/module-kp": "*", "klarna/module-ordermanagement": "*", "temando/module-shipping": "*", "vertex/product-magento-module": "*", "vertex/module-tax": "*", "vertex/sdk": "*", "vertexinc/product-magento-module": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*", "magento/google-shopping-ads": "*", "magento/module-advanced-pricing-import-export": "*", "magento/module-amqp": "*", "magento/module-amqp-store": "*", "magento/module-authorizenet": "*", "magento/module-authorizenet-acceptjs": "*", "magento/module-authorizenet-cardinal": "*", "magento/module-bundle-import-export": "*", "magento/module-catalog-analytics": "*", "magento/module-cardinal-commerce": "*", "magento/module-customer-analytics": "*", "magento/module-customer-import-export": "*", "magento/module-cybersource": "*", "magento/module-dhl": "*", "magento/module-downloadable-import-export": "*", "magento/module-eway": "*", "magento/module-fedex": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-grouped-import-export": "*", "magento/module-marketplace": "*", "magento/module-multishipping": "*", "magento/module-quote-analytics": "*", "magento/module-review-analytics": "*", "magento/module-sales-analytics": "*", "magento/module-sample-data": "*", "magento/module-send-friend": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-re-captcha-send-friend": "*", "magento/module-signifyd": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-tax-import-export": "*", "magento/module-ups": "*", "magento/module-usps": "*", "magento/module-version": "*", "magento/module-wishlist-analytics": "*", "magento/module-worldpay": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-admin-analytics": "*", "landofcoder/module-all": "*", "magento/module-two-factor-auth": "*", "magento/module-admin-adobe-ims": "*", "magefan/module-admin-user-guide": "*", "magento/module-application-performance-monitor": "*", "magento/module-application-performance-monitor-new-relic": "*", "magento/module-payment-services-saas-export": "*"}, "config": {"use-include-path": true, "allow-plugins": {"laminas/laminas-dependency-plugin": true, "magento/magento-composer-installer": true, "magento/inventory-composer-installer": true, "magento/composer-root-update-plugin": true, "magento/composer-dependency-version-audit-plugin": false, "creativestyle/composer-plugin-patchset": true, "php-http/discovery": true}}, "autoload": {"psr-4": {"Magento\\": "app/code/Magento/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\Framework\\": "lib/internal/Magento/Framework/", "Zend\\Mvc\\Controller\\": "setup/src/Zend/Mvc/Controller/"}, "psr-0": {"": "app/code/"}, "files": ["app/etc/NonComposerComponentRegistration.php"], "exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**", ".giti<PERSON>re"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/"}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "magento-deploy-ignore": {"*": ["/pub/.htaccess", "/.giti<PERSON>re", "/.htaccess"]}, "patchset": {"magento/data-migration-tool": [{"description": "placeholder", "filename": "config/patches/magento/data-migration-tool/placeholder.patch"}], "smile/elasticsuite": [{"description": "remove string from return type", "filename": "config/patches/smile/elasticsuite/elastic-virtual-category-indexer.patch"}], "magento/framework": [{"description": "CVE-2025-54236", "filename": "config/patches/magento/framework/VULN-32437-2-4-X.patch"}]}}}