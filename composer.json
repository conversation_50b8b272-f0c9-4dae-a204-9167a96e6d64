{"name": "copex/magento2-skeleton", "license": ["OSL-3.0", "AFL-3.0"], "type": "project", "version": "2.0.1", "description": "Magento 2 CE Skeleton", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://copex.io/"}], "repositories": {"magento": {"type": "composer", "url": "https://repo.magento.com/"}, "copex": {"type": "composer", "url": "https://repo.copex.io/banner-f2fbc6975bf15ed8d3f4d4cdh646a701"}, "firegento_extendedimport2": {"type": "vcs", "url": "https://github.com/firegento/FireGento_ExtendedImport2"}, "emailattachments": {"type": "vcs", "url": "https://github.com/CopeX/email-attachment"}, "fooman": {"type": "composer", "url": "https://customer-repos.fooman.co.nz/shop.bannerbatterien.com-fa86673cc506dba7b640a5cc33806d5f6a657e91"}, "magepal_customlink": {"type": "vcs", "url": "https://github.com/magepal/magento2-link-product"}, "mageplaza-webhook": {"type": "vcs", "url": "https://github.com/yiit-project/magento-2-webhook"}, "amasty": {"type": "composer", "url": "https://composer.amasty.com/community/"}}, "require": {"magento/product-community-edition": "2.4.6-p6", "firegento/magesetup2": "1.2.0", "splendidinternet/mage2-locale-de-de": "^1.16", "ethanyehuda/magento2-cronjobmanager": "^1", "avstudnitz/scopehint2": "^1", "copex/import-m2": "^0.4", "magepal/magento2-googletagmanager": "^2.2", "magepal/magento2-gmailsmtpapp": "^2.6", "geoip2/geoip2": "^2.0", "hybridauth/hybridauth": "^3.0", "firegento/extendedimport": "^1.0", "copex/category-headline-m2": "^1.0", "magepal/magento2-customeraccountlinksmanager": "^1.1.7", "experius/emailcatcher": "^3", "magepal/link-product": "^1.1", "shuchkin/simplexlsxgen": "^0.9.25", "smile/elasticsuite": "2.11.0", "cweagans/composer-patches": "^1.7", "amasty/module-advanced-permissions": "^1.4", "imi/magento2-store-switch-all-store-views": "^0.3.0", "phpseclib/mcrypt_compat": "^2.0", "fooman/pdfcustomiser-m2": "^8.9", "yireo/magento2-disable-csp": "^1.0", "laminas/laminas-zendframework-bridge": "^1.8"}, "require-dev": {"yireo/magento2-whoops": "*", "mage2tv/magento-cache-clean": "*", "squizlabs/php_codesniffer": "3.2.2", "lusitanian/oauth": "~0.8.10", "pdepend/pdepend": "2.13.0", "friendsofphp/php-cs-fixer": "^2.18", "smile/module-debug-toolbar": "^6.0.0", "deployer/deployer": "^7.0"}, "replace": {"amzn/amazon-pay-and-login-magento-2-module": "*", "amzn/amazon-pay-and-login-with-amazon-core-module": "*", "amzn/amazon-pay-module": "*", "amzn/amazon-pay-sdk-php": "*", "amzn/login-with-amazon-module": "*", "astock/stock-api-libphp": "*", "braintree/braintree": "*", "braintree/braintree_php": "*", "dotmailer/dotmailer-magento2-extension": "*", "dotmailer/dotmailer-magento2-extension-b2b": "*", "dotmailer/dotmailer-magento2-extension-chat": "*", "dotmailer/dotmailer-magento2-extension-enterprise": "*", "dotmailer/dotmailer-magento2-extension-enterprise-package": "*", "dotmailer/dotmailer-magento2-extension-package": "*", "dotmailer/dotmailer-magento2-extension-sms": "*", "klarna/m2-payments": "*", "klarna/module-core": "*", "klarna/module-kp": "*", "klarna/module-kp-graph-ql": "*", "klarna/module-onsitemessaging": "*", "klarna/module-ordermanagement": "*", "magento/adobe-stock-integration": "*", "magento/google-shopping-ads": "*", "magento/module-admin-adobe-ims": "*", "magento/module-admin-analytics": "*", "magento/module-adobe-ims": "*", "magento/module-adobe-ims-api": "*", "magento/module-adobe-stock-admin-ui": "*", "magento/module-adobe-stock-asset": "*", "magento/module-adobe-stock-asset-api": "*", "magento/module-adobe-stock-client": "*", "magento/module-adobe-stock-client-api": "*", "magento/module-adobe-stock-image": "*", "magento/module-adobe-stock-image-admin-ui": "*", "magento/module-adobe-stock-image-api": "*", "magento/module-advanced-pricing-import-export": "*", "magento/module-amqp": "*", "magento/module-amqp-store": "*", "magento/module-analytics": "*", "magento/module-authorizenet": "*", "magento/module-authorizenet-acceptjs": "*", "magento/module-authorizenet-cardinal": "*", "magento/module-authorizenet-graph-ql": "*", "magento/module-aws-s3": "*", "magento/module-aws-s3-page-builder": "*", "magento/module-braintree": "*", "magento/module-braintree-graph-ql": "*", "magento/module-bundle-graph-ql": "*", "magento/module-bundle-import-export": "*", "magento/module-bundle-import-export-staging": "*", "magento/module-bundle-sample-data": "*", "magento/module-bundle-staging": "*", "magento/module-cardinal-commerce": "*", "magento/module-catalog-analytics": "*", "magento/module-catalog-cms-graph-ql": "*", "magento/module-catalog-customer-graph-ql": "*", "magento/module-catalog-customer-ql": "*", "magento/module-catalog-import-export-staging": "*", "magento/module-catalog-page-builder-analytics": "*", "magento/module-catalog-page-builder-analytics-staging": "*", "magento/module-catalog-permissions-graph-ql": "*", "magento/module-catalog-rule-graph-ql": "*", "magento/module-catalog-rule-sample-data": "*", "magento/module-catalog-rule-staging": "*", "magento/module-catalog-sample-data": "*", "magento/module-catalog-staging": "*", "magento/module-catalog-staging-graph-ql": "*", "magento/module-catalog-staging-page-builder": "*", "magento/module-catalog-url-rewrite-graph-ql": "*", "magento/module-catalog-url-rewrite-staging": "*", "magento/module-checkout-agreements-graph-ql": "*", "magento/module-checkout-staging": "*", "magento/module-cms-graph-ql": "*", "magento/module-cms-page-builder-analytics": "*", "magento/module-cms-page-builder-analytics-staging": "*", "magento/module-cms-sample-data": "*", "magento/module-cms-staging": "*", "magento/module-cms-url-rewrite-graph-ql": "*", "magento/module-compare-list-graph-ql": "*", "magento/module-configurable-product-graph-ql": "*", "magento/module-configurable-product-staging": "*", "magento/module-configurable-sample-data": "*", "magento/module-customer-analytics": "*", "magento/module-customer-balance-graph-ql": "*", "magento/module-customer-downloadable-graph-ql": "*", "magento/module-customer-finance": "*", "magento/module-customer-sample-data": "*", "magento/module-cybersource": "*", "magento/module-dhl": "*", "magento/module-directory-graph-ql": "*", "magento/module-downloadable-graph-ql": "*", "magento/module-downloadable-import-export": "*", "magento/module-downloadable-sample-data": "*", "magento/module-downloadable-staging": "*", "magento/module-eway": "*", "magento/module-fedex": "*", "magento/module-gift-card-account-graph-ql": "*", "magento/module-gift-card-graph-ql": "*", "magento/module-gift-card-staging": "*", "magento/module-gift-message-graph-ql": "*", "magento/module-gift-message-staging": "*", "magento/module-gift-wrapping-graph-ql": "*", "magento/module-gift-wrapping-staging": "*", "magento/module-google-adwords": "*", "magento/module-google-optimizer": "*", "magento/module-google-optimizer-staging": "*", "magento/module-grouped-import-export": "*", "magento/module-grouped-product-graph-ql": "*", "magento/module-grouped-product-sample-data": "*", "magento/module-grouped-product-staging": "*", "magento/module-layered-navigation-staging": "*", "magento/module-login-as-customer-graph-ql": "*", "magento/module-marketplace": "*", "magento/module-msrp-sample-data": "*", "magento/module-msrp-staging": "*", "magento/module-multiple-wishlist-graph-ql": "*", "magento/module-multishipping": "*", "magento/module-newsletter-graph-ql": "*", "magento/module-offline-shipping-sample-data": "*", "magento/module-page-builder-analytics": "*", "magento/module-page-builder-admin-analytics": "*", "magento/module-payment-graph-ql": "*", "magento/module-payment-staging": "*", "magento/module-paypal-graph-ql": "*", "magento/module-product-links-sample-data": "*", "magento/module-product-video-staging": "*", "magento/module-quote-analytics": "*", "magento/module-re-captcha-webapi-graph-ql": "*", "magento/module-related-product-graph-ql": "*", "magento/module-review-analytics": "*", "magento/module-review-graph-ql": "*", "magento/module-review-sample-data": "*", "magento/module-review-staging": "*", "magento/module-reward-graph-ql": "*", "magento/module-reward-staging": "*", "magento/module-rma-graph-ql": "*", "magento/module-rma-staging": "*", "magento/module-sales-analytics": "*", "magento/module-sales-graph-ql": "*", "magento/module-sales-rule-sample-data": "*", "magento/module-sales-rule-staging": "*", "magento/module-sales-sample-data": "*", "magento/module-sample-data": "*", "magento/module-search-staging": "*", "magento/module-send-friend": "*", "magento/module-send-friend-graph-ql": "*", "magento/module-signifyd": "*", "magento/module-staging": "*", "magento/module-staging-graph-ql": "*", "magento/module-staging-page-builder": "*", "magento/module-store-graph-ql": "*", "magento/module-swagger": "*", "magento/module-swagger-webapi": "*", "magento/module-swagger-webapi-async": "*", "magento/module-swatches-graph-ql": "*", "magento/module-swatches-sample-data": "*", "magento/module-target-rule-graph-ql": "*", "magento/module-tax-graph-ql": "*", "magento/module-tax-import-export": "*", "magento/module-tax-sample-data": "*", "magento/module-theme-graph-ql": "*", "magento/module-theme-sample-data": "*", "magento/module-two-factor-auth": "*", "magento/module-ups": "*", "magento/module-url-rewrite-graph-ql": "*", "magento/module-usps": "*", "magento/module-vault-graph-ql": "*", "magento/module-version": "*", "magento/module-versions-cms-url-rewrite-graph-ql": "*", "magento/module-weee-graph-ql": "*", "magento/module-weee-staging": "*", "magento/module-widget-sample-data": "*", "magento/module-wishlist-analytics": "*", "magento/module-wishlist-graph-ql": "*", "magento/module-wishlist-sample-data": "*", "magento/module-worldpay": "*", "magento/page-builder": "*", "magento/sample-data-media": "*", "paypal/module-braintree": "*", "paypal/module-braintree-core": "*", "paypal/module-braintree-graph-ql": "*", "temando/module-shipping": "*", "temando/module-shipping-m2": "*", "temando/module-shipping-remover": "*", "vertex/module-address-validation": "*", "vertex/module-tax": "*", "vertex/module-tax-staging": "*", "vertex/product-magento-module": "*", "vertex/sdk": "*", "vertexinc/module-tax-staging": "*", "vertexinc/product-magento-module": "*", "vertexinc/product-magento-module-commerce": "*", "yotpo/magento2-module-yotpo-reviews": "*", "yotpo/magento2-module-yotpo-reviews-bundle": "*", "magento/module-admin-adobe-ims-two-factor-auth": "*"}, "config": {"allow-plugins": {"cweagans/composer-patches": true, "laminas/laminas-dependency-plugin": true, "magento/inventory-composer-installer": true, "magento/magento-composer-installer": true, "magento/composer-dependency-version-audit-plugin": true}}, "autoload": {"psr-4": {"Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/", "Magento\\": "app/code/Magento/", "Zend\\Mvc\\Controller\\": "setup/src/Zend/Mvc/Controller/"}, "psr-0": {"": "app/code/"}, "files": ["app/etc/NonComposerComponentRegistration.php"]}, "autoload-dev": {"psr-4": {"Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/lication/sanity/Magento/Tools/Sanity/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/"}}, "minimum-stability": "dev", "prefer-stable": true, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "patches": {"magento/framework": {"areaCode": "config/patches/areaCode.patch"}}, "magento-deploy-ignore": {"*": ["/pub/.htaccess", "/.giti<PERSON>re"]}}}